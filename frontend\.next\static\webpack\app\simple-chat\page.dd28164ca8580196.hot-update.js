"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/page.tsx":
/*!**************************************!*\
  !*** ./src/app/simple-chat/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks */ \"(app-pages-browser)/./src/app/simple-chat/hooks/index.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/simple-chat/components/index.ts\");\n/* harmony import */ var _services_streamingChatService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/streamingChatService */ \"(app-pages-browser)/./src/app/simple-chat/services/streamingChatService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SimpleChatPage() {\n    _s();\n    // 使用模块化的hooks\n    const { conversations, currentConversation, loading: conversationLoading, error: conversationError, loadConversations, loadConversationsIfNeeded, createConversation, switchConversation, deleteConversation } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationManager)();\n    const { messages, setMessages, inputMessage, setInputMessage, isStreaming, setIsStreaming, selectedModel, setSelectedModel, models, expandedThinkingMessages, enableTools, setEnableTools, selectedTools, setSelectedTools, activeTool, setActiveTool, toolCalls, setToolCalls, currentAssistantMessageId, setCurrentAssistantMessageId, systemPrompt, selectAgent, toggleThinkingExpand, stopGeneration, selectBestModel, abortController, setAbortController } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useChatMessages)();\n    const { chatStyle, displaySize, setChatStyle: handleChatStyleChange, setDisplaySize: handleDisplaySizeChange } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useChatStyle)();\n    // UI状态\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Agent related state\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAgentId, setSelectedAgentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectorMode, setSelectorMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('model');\n    // 优化：直接使用models数据，无需转换\n    // models现在是CustomModel[]类型，包含完整的display_name等信息\n    // 为组件兼容性生成customModels格式\n    const [customModels, setCustomModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从CustomModel[]生成customModels显示信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            if (models.length > 0) {\n                const formattedCustomModels = models.map({\n                    \"SimpleChatPage.useEffect.formattedCustomModels\": (model)=>({\n                            base_model: model.base_model,\n                            display_name: model.display_name,\n                            family: model.family\n                        })\n                }[\"SimpleChatPage.useEffect.formattedCustomModels\"]);\n                setCustomModels(formattedCustomModels);\n                console.log('✅ 生成customModels显示信息:', formattedCustomModels.length, '个模型');\n            }\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        models\n    ]);\n    // 使用ref来获取最新的messages值，避免在useCallback依赖中包含messages\n    const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages);\n    const selectedModelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(selectedModel);\n    const setMessagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setMessages);\n    const setActiveToolRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setActiveTool);\n    const setToolCallsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setToolCalls);\n    const setCurrentAssistantMessageIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setCurrentAssistantMessageId);\n    const setIsStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setIsStreaming);\n    const setErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setError);\n    const loadConversationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(loadConversations);\n    const systemPromptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(systemPrompt);\n    // 🔧 修复：添加清理函数的ref\n    const cleanupHandlersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            messagesRef.current = messages;\n            selectedModelRef.current = selectedModel;\n            setMessagesRef.current = setMessages;\n            setActiveToolRef.current = setActiveTool;\n            setToolCallsRef.current = setToolCalls;\n            setCurrentAssistantMessageIdRef.current = setCurrentAssistantMessageId;\n            setIsStreamingRef.current = setIsStreaming;\n            setErrorRef.current = setError;\n            loadConversationsRef.current = loadConversations;\n            systemPromptRef.current = systemPrompt;\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        messages,\n        selectedModel,\n        setMessages,\n        setActiveTool,\n        setToolCalls,\n        setCurrentAssistantMessageId,\n        setIsStreaming,\n        setError,\n        loadConversations,\n        systemPrompt\n    ]);\n    // 🔧 修复：组件卸载时清理所有pending的更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>{\n                    cleanupHandlersRef.current.forEach({\n                        \"SimpleChatPage.useEffect\": (cleanup)=>cleanup()\n                    }[\"SimpleChatPage.useEffect\"]);\n                    cleanupHandlersRef.current = [];\n                }\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], []);\n    // 从数据库数据更新消息的辅助函数\n    const updateMessagesFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[updateMessagesFromDatabase]\": (dbMessages)=>{\n            console.log('🔧 更新消息数据，总数:', dbMessages.length);\n            // 使用与useMessageLoader相同的逻辑处理工具调用消息\n            const allMessages = dbMessages.sort({\n                \"SimpleChatPage.useCallback[updateMessagesFromDatabase].allMessages\": (a, b)=>{\n                    if (a.timestamp !== b.timestamp) {\n                        return a.timestamp - b.timestamp;\n                    }\n                    return a.id - b.id;\n                }\n            }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase].allMessages\"]);\n            const formattedMessages = [];\n            const toolCallMessages = [];\n            for (const msg of allMessages){\n                if (msg.role === 'tool_call' && msg.tool_name) {\n                    // 处理工具调用消息\n                    let args = {};\n                    let result = '';\n                    try {\n                        args = msg.tool_args ? JSON.parse(msg.tool_args) : {};\n                    } catch (e) {\n                        args = {};\n                    }\n                    try {\n                        result = msg.tool_result ? typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result) : '';\n                    } catch (e) {\n                        result = msg.tool_result || '';\n                    }\n                    const toolCall = {\n                        id: \"tool-\".concat(msg.id),\n                        toolName: msg.tool_name,\n                        args: args,\n                        status: msg.tool_status || 'completed',\n                        result: result,\n                        error: msg.tool_error || undefined,\n                        startTime: msg.timestamp || new Date(msg.created_at).getTime(),\n                        executionTime: msg.tool_execution_time || 0\n                    };\n                    toolCallMessages.push(toolCall);\n                    formattedMessages.push({\n                        id: \"tool-placeholder-\".concat(msg.id),\n                        role: 'tool_call',\n                        content: '',\n                        timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                        toolCall: toolCall\n                    });\n                } else {\n                    formattedMessages.push({\n                        id: \"msg-\".concat(msg.id),\n                        role: msg.role,\n                        content: msg.content,\n                        timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                        model: msg.model,\n                        // 包含统计字段\n                        total_duration: msg.total_duration,\n                        load_duration: msg.load_duration,\n                        prompt_eval_count: msg.prompt_eval_count,\n                        prompt_eval_duration: msg.prompt_eval_duration,\n                        eval_count: msg.eval_count,\n                        eval_duration: msg.eval_duration\n                    });\n                }\n            }\n            // 检查是否有统计信息\n            const hasStats = formattedMessages.some({\n                \"SimpleChatPage.useCallback[updateMessagesFromDatabase].hasStats\": (msg)=>msg.role === 'assistant' && (msg.total_duration || msg.eval_count)\n            }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase].hasStats\"]);\n            console.log('🔧 更新后的消息是否包含统计信息:', hasStats);\n            console.log('🔧 更新后的工具调用数量:', toolCallMessages.length);\n            setMessagesRef.current(formattedMessages);\n            setToolCallsRef.current(toolCallMessages);\n        }\n    }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase]\"], []);\n    // 处理模型切换，传递对话ID以保存对话特定的模型选择\n    const handleModelChange = (modelName)=>{\n        const conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        setSelectedModel(modelName, conversationId);\n    };\n    // Fetch agents - 优化：添加缓存和错误处理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            let isMounted = true;\n            const fetchAgents = {\n                \"SimpleChatPage.useEffect.fetchAgents\": async ()=>{\n                    try {\n                        console.log('🤖 开始加载智能体列表');\n                        const response = await fetch('/api/agents');\n                        if (response.ok && isMounted) {\n                            const agents = await response.json();\n                            setAgents(agents);\n                            console.log(\"✅ 成功加载 \".concat(agents.length, \" 个智能体\"));\n                        }\n                    } catch (error) {\n                        if (isMounted) {\n                            console.error('❌ 加载智能体失败:', error);\n                        }\n                    }\n                }\n            }[\"SimpleChatPage.useEffect.fetchAgents\"];\n            fetchAgents();\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], []);\n    const handleAgentChange = (agentId)=>{\n        setSelectedAgentId(agentId);\n        selectAgent(agentId);\n    };\n    const handleSelectorModeChange = (mode)=>{\n        setSelectorMode(mode);\n    };\n    // 使用 URL 处理器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useUrlHandler)({\n        models,\n        selectedModel,\n        currentConversation,\n        conversationLoading,\n        createConversation,\n        switchConversation\n    });\n    // 使用消息加载器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useMessageLoader)({\n        currentConversation,\n        setSelectedModel,\n        setMessages,\n        setToolCalls,\n        selectedModel,\n        models,\n        selectBestModel\n    });\n    // 使用对话事件处理器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationEventHandlers)({\n        currentConversation,\n        conversations,\n        selectedModel,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        loadConversations,\n        setMessages,\n        setToolCalls,\n        setSelectedModel,\n        setError,\n        setIsProcessingUrl: {\n            \"SimpleChatPage.useConversationEventHandlers\": ()=>{}\n        }[\"SimpleChatPage.useConversationEventHandlers\"]\n    });\n    // 清空当前对话\n    const clearCurrentChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[clearCurrentChat]\": async ()=>{\n            if (!currentConversation) return;\n            try {\n                const response = await fetch(\"/api/conversations/\".concat(currentConversation.id, \"/clear\"), {\n                    method: 'POST'\n                });\n                if (response.ok) {\n                    // 清空当前消息\n                    setMessages([]);\n                    setToolCalls([]);\n                    setActiveTool(null);\n                    setError(null);\n                    // 重新加载对话列表\n                    loadConversations();\n                }\n            } catch (error) {\n                console.error('清空对话失败:', error);\n                setError('清空对话失败');\n            }\n        }\n    }[\"SimpleChatPage.useCallback[clearCurrentChat]\"], [\n        currentConversation,\n        setMessages,\n        setToolCalls,\n        setActiveTool,\n        setError,\n        loadConversations\n    ]);\n    // 创建流处理器句柄 - 修复：使用useCallback确保函数稳定性，移除不必要的依赖\n    const createStreamHandlers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n            const handlers = {\n                onMessageUpdate: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (messageId, content, stats)=>{\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>msg.id === messageId ? {\n                                            ...msg,\n                                            content,\n                                            ...stats || {}\n                                        } : msg\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallStart: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCall)=>{\n                        setActiveToolRef.current(toolCall);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    toolCall\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        const toolCallMessage = {\n                            id: \"tool-runtime-\".concat(toolCall.id),\n                            role: 'tool_call',\n                            content: '',\n                            timestamp: Date.now(),\n                            toolCall: toolCall\n                        };\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    toolCallMessage\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallComplete: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCallId, toolName, result, executionTime)=>{\n                        setActiveToolRef.current(null);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (tc)=>{\n                                        const isMatch = toolCallId ? tc.id === toolCallId : tc.toolName === toolName && tc.status === 'executing';\n                                        return isMatch ? {\n                                            ...tc,\n                                            status: 'completed',\n                                            result: typeof result === 'string' ? result : JSON.stringify(result),\n                                            executionTime: executionTime || Date.now() - tc.startTime\n                                        } : tc;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>{\n                                        if (msg.role === 'tool_call' && msg.toolCall) {\n                                            const isMatch = toolCallId ? msg.toolCall.id === toolCallId : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';\n                                            if (isMatch) {\n                                                return {\n                                                    ...msg,\n                                                    toolCall: {\n                                                        id: msg.toolCall.id,\n                                                        toolName: msg.toolCall.toolName,\n                                                        args: msg.toolCall.args,\n                                                        status: 'completed',\n                                                        result: typeof result === 'string' ? result : JSON.stringify(result),\n                                                        startTime: msg.toolCall.startTime,\n                                                        executionTime: executionTime || Date.now() - msg.toolCall.startTime\n                                                    }\n                                                };\n                                            }\n                                        }\n                                        return msg;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallError: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCallId, toolName, error, executionTime)=>{\n                        setActiveToolRef.current(null);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (tc)=>{\n                                        const isMatch = toolCallId ? tc.id === toolCallId : tc.toolName === toolName && tc.status === 'executing';\n                                        return isMatch ? {\n                                            ...tc,\n                                            status: 'error',\n                                            error: error || '工具调用失败',\n                                            executionTime: executionTime || Date.now() - tc.startTime\n                                        } : tc;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>{\n                                        if (msg.role === 'tool_call' && msg.toolCall) {\n                                            const isMatch = toolCallId ? msg.toolCall.id === toolCallId : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';\n                                            if (isMatch) {\n                                                return {\n                                                    ...msg,\n                                                    toolCall: {\n                                                        id: msg.toolCall.id,\n                                                        toolName: msg.toolCall.toolName,\n                                                        args: msg.toolCall.args,\n                                                        status: 'error',\n                                                        error: error || '工具调用失败',\n                                                        startTime: msg.toolCall.startTime,\n                                                        executionTime: executionTime || Date.now() - msg.toolCall.startTime\n                                                    }\n                                                };\n                                            }\n                                        }\n                                        return msg;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onNewAssistantMessage: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (messageId)=>{\n                        const newAssistantMessage = {\n                            id: messageId,\n                            role: 'assistant',\n                            content: '',\n                            timestamp: Date.now(),\n                            model: selectedModelRef.current\n                        };\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    newAssistantMessage\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setCurrentAssistantMessageIdRef.current(messageId);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onStreamEnd: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n                        setIsStreamingRef.current(false);\n                        setActiveToolRef.current(null);\n                        setCurrentAssistantMessageIdRef.current(null);\n                        // 优化：更智能的统计信息获取策略，减少API调用\n                        const cleanup = {\n                            \"SimpleChatPage.useCallback[createStreamHandlers].cleanup\": ()=>{\n                                if (currentConversation) {\n                                    console.log('🔧 对话完成，准备获取统计信息');\n                                    // 使用单次调用获取统计信息，如果没有则等待后重试一次\n                                    const fetchStats = {\n                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\": async function() {\n                                            let retryOnce = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n                                            try {\n                                                const response = await fetch(\"/api/conversations/\".concat(currentConversation.id));\n                                                const data = await response.json();\n                                                if (data.success && data.messages) {\n                                                    const hasStats = data.messages.some({\n                                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats.hasStats\": (msg)=>msg.role === 'assistant' && (msg.total_duration || msg.eval_count)\n                                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats.hasStats\"]);\n                                                    if (hasStats) {\n                                                        console.log('✅ 获取到统计信息，更新消息');\n                                                        updateMessagesFromDatabase(data.messages);\n                                                    } else if (retryOnce) {\n                                                        console.log('⏳ 统计信息未就绪，1秒后重试一次');\n                                                        setTimeout({\n                                                            \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\": ()=>fetchStats(false)\n                                                        }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\"], 1000);\n                                                    } else {\n                                                        console.log('⚠️ 统计信息仍未就绪，使用当前消息');\n                                                        updateMessagesFromDatabase(data.messages);\n                                                    }\n                                                } else {\n                                                    console.log('❌ 获取消息数据失败');\n                                                }\n                                            } catch (err) {\n                                                console.error('获取统计信息失败:', err);\n                                            }\n                                        }\n                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\"];\n                                    // 延迟300ms后开始获取，给服务器时间保存统计信息\n                                    setTimeout({\n                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup\": ()=>fetchStats()\n                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup\"], 300);\n                                } else {\n                                    console.log('🔄 无当前对话，刷新对话列表');\n                                    loadConversationsRef.current();\n                                }\n                            }\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup\"];\n                        // 添加到清理队列\n                        cleanupHandlersRef.current.push(cleanup);\n                        cleanup();\n                        // 从清理队列中移除\n                        setTimeout({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n                                const index = cleanupHandlersRef.current.indexOf(cleanup);\n                                if (index > -1) {\n                                    cleanupHandlersRef.current.splice(index, 1);\n                                }\n                            }\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"], 3000);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onError: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (errorMessage)=>{\n                        setErrorRef.current(errorMessage);\n                        setIsStreamingRef.current(false);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]\n            };\n            return handlers;\n        }\n    }[\"SimpleChatPage.useCallback[createStreamHandlers]\"], [\n        updateMessagesFromDatabase,\n        currentConversation\n    ]);\n    // 插入文本到输入框\n    const handleInsertText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[handleInsertText]\": (text)=>{\n            setInputMessage(text);\n        }\n    }[\"SimpleChatPage.useCallback[handleInsertText]\"], [\n        setInputMessage\n    ]);\n    // 发送消息的核心逻辑\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[sendMessage]\": async ()=>{\n            if (!inputMessage.trim() || !selectedModel || isStreaming) {\n                return;\n            }\n            let activeConversation = currentConversation;\n            if (!activeConversation) {\n                const title = inputMessage.trim().substring(0, 30) + (inputMessage.length > 30 ? '...' : '');\n                const conversationId = await createConversation(title, selectedModel);\n                if (!conversationId) {\n                    setError('创建对话失败');\n                    return;\n                }\n                await new Promise({\n                    \"SimpleChatPage.useCallback[sendMessage]\": (resolve)=>setTimeout(resolve, 100)\n                }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n                activeConversation = currentConversation;\n            }\n            const userMessage = {\n                id: \"user-\".concat(Date.now()),\n                role: 'user',\n                content: inputMessage.trim(),\n                timestamp: Date.now()\n            };\n            // 获取当前的消息列表（使用ref避免在依赖中包含messages）\n            const currentMessages = messagesRef.current;\n            setMessages({\n                \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        userMessage\n                    ]\n            }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n            setInputMessage('');\n            setIsStreaming(true);\n            setError(null);\n            setToolCalls([]);\n            setActiveTool(null);\n            const assistantMessageId = \"assistant-\".concat(Date.now());\n            const assistantMessage = {\n                id: assistantMessageId,\n                role: 'assistant',\n                content: '',\n                timestamp: Date.now(),\n                model: selectedModel\n            };\n            setMessages({\n                \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]\n            }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n            setCurrentAssistantMessageId(assistantMessageId);\n            try {\n                // 创建新的 AbortController\n                const controller = new AbortController();\n                setAbortController(controller);\n                const chatRequestBody = {\n                    model: selectedModel,\n                    conversationId: activeConversation === null || activeConversation === void 0 ? void 0 : activeConversation.id,\n                    messages: [\n                        ...systemPromptRef.current ? [\n                            {\n                                role: 'system',\n                                content: systemPromptRef.current\n                            }\n                        ] : [],\n                        ...currentMessages.map({\n                            \"SimpleChatPage.useCallback[sendMessage]\": (msg)=>({\n                                    role: msg.role,\n                                    content: msg.content\n                                })\n                        }[\"SimpleChatPage.useCallback[sendMessage]\"]),\n                        {\n                            role: 'user',\n                            content: userMessage.content\n                        }\n                    ],\n                    stream: true,\n                    enableTools,\n                    selectedTools\n                };\n                const response = await fetch('/api/chat', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(chatRequestBody),\n                    signal: controller.signal\n                });\n                if (!response.ok) {\n                    throw new Error('聊天请求失败');\n                }\n                // 使用流式服务处理响应，传递 AbortController\n                await _services_streamingChatService__WEBPACK_IMPORTED_MODULE_5__.streamingChatService.processStreamingResponse(response, createStreamHandlers(), assistantMessageId, controller);\n            } catch (err) {\n                // 如果是中断错误，不显示错误信息\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('🛑 用户主动停止了生成');\n                } else {\n                    setError(err instanceof Error ? err.message : '发送消息失败');\n                    setMessages({\n                        \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>prev.filter({\n                                \"SimpleChatPage.useCallback[sendMessage]\": (msg)=>msg.id !== assistantMessageId\n                            }[\"SimpleChatPage.useCallback[sendMessage]\"])\n                    }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n                }\n            } finally{\n                setIsStreaming(false);\n                setAbortController(null);\n            }\n        }\n    }[\"SimpleChatPage.useCallback[sendMessage]\"], [\n        inputMessage,\n        selectedModel,\n        isStreaming,\n        currentConversation,\n        enableTools,\n        selectedTools,\n        createConversation,\n        systemPrompt,\n        setAbortController\n    ]);\n    // 侧边栏事件处理函数 - 优化：按需加载对话列表\n    const handleCreateConversation = ()=>{\n        window.location.href = '/simple-chat?new=true';\n    };\n    const handleLoadConversation = async (conversationId)=>{\n        // 确保有对话列表数据\n        await loadConversationsIfNeeded();\n        window.location.href = \"/simple-chat?id=\".concat(conversationId);\n    };\n    const handleDeleteConversation = async (conversationId)=>{\n        try {\n            // 确保有对话列表数据\n            await loadConversationsIfNeeded();\n            const response = await fetch(\"/api/conversations/\".concat(conversationId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await loadConversations(); // 删除后刷新列表\n                // 如果删除的是当前对话，重定向到新对话\n                if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                    window.location.href = '/simple-chat?new=true';\n                }\n            }\n        } catch (error) {\n            console.error('Failed to delete conversation:', error);\n        }\n    };\n    // 确保侧边栏有对话列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            // 延迟加载对话列表，避免阻塞页面初始化\n            const timer = setTimeout({\n                \"SimpleChatPage.useEffect.timer\": ()=>{\n                    loadConversationsIfNeeded();\n                }\n            }[\"SimpleChatPage.useEffect.timer\"], 100);\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        loadConversationsIfNeeded\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-theme-background overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                conversations: conversations,\n                currentConversation: currentConversation,\n                onCreateConversation: handleCreateConversation,\n                onLoadConversation: handleLoadConversation,\n                onDeleteConversation: handleDeleteConversation\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n                lineNumber: 669,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_4__.ChatContainer, {\n                currentConversation: currentConversation,\n                models: models,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                agents: agents,\n                selectedAgentId: selectedAgentId,\n                onAgentChange: handleAgentChange,\n                selectorMode: selectorMode,\n                onSelectorModeChange: handleSelectorModeChange,\n                messages: messages,\n                inputMessage: inputMessage,\n                onInputChange: setInputMessage,\n                onSendMessage: sendMessage,\n                isStreaming: isStreaming,\n                onStopGeneration: stopGeneration,\n                expandedThinkingMessages: expandedThinkingMessages,\n                onToggleThinkingExpand: toggleThinkingExpand,\n                enableTools: enableTools,\n                selectedTools: selectedTools,\n                onToolsToggle: setEnableTools,\n                onSelectedToolsChange: setSelectedTools,\n                onInsertText: handleInsertText,\n                onClearChat: clearCurrentChat,\n                error: error,\n                onDismissError: ()=>setError(null),\n                chatStyle: chatStyle,\n                displaySize: displaySize,\n                onChatStyleChange: handleChatStyleChange,\n                onDisplaySizeChange: handleDisplaySizeChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n                lineNumber: 678,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n        lineNumber: 667,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatPage, \"iuqQE5DSQi3b8pkj33zfFo3Ol10=\", false, function() {\n    return [\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationManager,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useChatMessages,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useChatStyle,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useUrlHandler,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useMessageLoader,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationEventHandlers\n    ];\n});\n_c = SimpleChatPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/page.tsx\n"));

/***/ })

});