"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/model-manager/page",{

/***/ "(app-pages-browser)/./src/app/model-manager/components/ModelDetailsModal.tsx":
/*!****************************************************************!*\
  !*** ./src/app/model-manager/components/ModelDetailsModal.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelDetailsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/model-manager/components/ModelLogo */ \"(app-pages-browser)/./src/app/model-manager/components/ModelLogo.tsx\");\n/* harmony import */ var _ModalWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModalWrapper */ \"(app-pages-browser)/./src/app/model-manager/components/ModalWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// 格式化文件大小\nconst formatFileSize = (bytes)=>{\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\nconst InfoRow = (param)=>{\n    let { label, value, mono = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm font-medium text-theme-foreground-muted mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-theme-foreground \".concat(mono ? 'font-mono text-sm' : '', \" \").concat(mono ? 'bg-theme-background-secondary px-4 py-3 rounded-xl text-xs break-all' : ''),\n                children: value\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-theme-foreground-muted text-sm italic\",\n                children: \"未设置\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n};\n_c = InfoRow;\nfunction ModelDetailsModal(param) {\n    let { model, onClose } = param;\n    if (!model) return null;\n    const modalIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        modelName: model.family || model.base_model,\n        containerSize: 56,\n        imageSize: 32,\n        className: \"bg-theme-background-secondary rounded-2xl\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n    const headerContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-2 mt-3\",\n        children: model.tags && model.tags.length > 0 && model.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-3 py-1 bg-theme-primary/10 text-theme-primary text-xs rounded-full border border-theme-primary/20\",\n                children: tag\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModalWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        isOpen: true,\n        onClose: onClose,\n        title: model.display_name,\n        subtitle: model.base_model,\n        maxWidth: \"4xl\",\n        icon: modalIcon,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-8 pb-4\",\n                children: headerContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto scrollbar-thin px-8 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-title text-theme-foreground-muted mb-6\",\n                                    children: \"基本信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"模型别名\",\n                                                    value: model.display_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"基础模型\",\n                                                    value: model.base_model\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"模型家族\",\n                                                    value: model.family\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"架构\",\n                                                    value: model.architecture || '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"参数规模\",\n                                                    value: model.parameter_count ? \"\".concat((model.parameter_count / 1e9).toFixed(1), \"B\") : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"文件大小\",\n                                                    value: model.size ? formatFileSize(model.size) : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"上下文长度\",\n                                                    value: model.context_length ? model.context_length.toLocaleString() : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"嵌入长度\",\n                                                    value: model.embedding_length ? model.embedding_length.toLocaleString() : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"量化级别\",\n                                                    value: model.quantization_level || '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"文件格式\",\n                                                    value: model.format || '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"模型能力\",\n                                                    value: model.capabilities && model.capabilities.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: model.capabilities.map((capability, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-3 py-1 bg-theme-primary/10 text-theme-primary text-xs rounded-full border border-theme-primary/20\",\n                                                                children: capability\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 25\n                                                            }, void 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, void 0) : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"更新时间\",\n                                                    value: model.updated_at ? new Date(model.updated_at).toLocaleString('zh-CN') : new Date(model.created_at).toLocaleString('zh-CN')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"Ollama修改时间\",\n                                                    value: model.ollama_modified_at ? new Date(model.ollama_modified_at).toLocaleString('zh-CN') : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                        label: \"模型描述\",\n                                        value: model.description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-theme-foreground leading-relaxed text-sm\",\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, void 0) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-title !text-theme-foreground-muted mb-6\",\n                                    children: \"高级配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                            label: \"系统提示\",\n                                            value: model.system_prompt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"whitespace-pre-wrap text-sm bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border overflow-x-auto scrollbar-thin\",\n                                                children: model.system_prompt\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, void 0) : null\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                            label: \"模型参数\",\n                                            value: model.parameters && Object.keys(model.parameters).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border space-y-2\",\n                                                children: Object.entries(model.parameters).map((param)=>{\n                                                    let [key, value] = param;\n                                                    const displayValue = Array.isArray(value) ? value.join(', ') : String(value);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-theme-foreground-muted\",\n                                                                children: [\n                                                                    key,\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-mono text-theme-foreground\",\n                                                                children: displayValue\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        ]\n                                                    }, key, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 25\n                                                    }, void 0);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, void 0) : null\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                            label: \"模板\",\n                                            value: model.template ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"whitespace-pre-wrap text-sm bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border overflow-x-auto scrollbar-thin font-mono\",\n                                                children: model.template\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, void 0) : null\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-title !text-theme-foreground-muted mb-6\",\n                                    children: \"许可证\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                    label: \"许可证信息\",\n                                    value: model.license ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"whitespace-pre-wrap text-sm bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border overflow-x-auto scrollbar-thin\",\n                                        children: model.license\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, void 0) : null\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ModelDetailsModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"InfoRow\");\n$RefreshReg$(_c1, \"ModelDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/model-manager/components/ModelDetailsModal.tsx\n"));

/***/ })

});