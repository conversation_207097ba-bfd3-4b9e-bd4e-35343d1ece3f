# MCP (Model Context Protocol) 架构文档

## 概述

本文档详细说明了项目中 MCP (Model Context Protocol) 架构的设计与实现。MCP 是一个用于连接 AI 模型与外部工具和数据源的协议，本项目实现了完整的 MCP 客户端和服务器端解决方案，包括前端配置界面、API 层、数据库持久化和多种传输协议支持。

## 系统架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端配置界面   │────│    API 层       │────│   数据库层      │
│  (mcp-config)   │    │  (/api/mcp)     │    │ (SQLite + ORM)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MCP 核心库    │
                    │   (/lib/mcp)    │
                    └─────────────────┘
                             │
              ┌──────────────┼──────────────┐
              │              │              │
    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
    │ SSE 客户端  │  │ HTTP 客户端 │  │ STDIO 客户端│
    └─────────────┘  └─────────────┘  └─────────────┘
```

## 目录结构

### 核心 MCP 库 (`/frontend/src/lib/mcp/`)

```
frontend/src/lib/mcp/
├── mcp-client.ts                    # 浏览器端MCP客户端代理
├── mcp-client-server.ts             # 服务器端MCP客户端
├── mcp-client-sse.ts                # SSE传输方式的MCP客户端
├── mcp-client-streamable-http.ts    # Streamable HTTP传输方式的MCP客户端
├── mcp-multi-server-client.ts       # 多服务器MCP客户端管理器
├── mcp-server.ts                    # MCP服务器实现
└── mcp-tools.ts                     # MCP工具集成模块
```

### 前端配置界面 (`/frontend/src/app/mcp-config/`)

```
frontend/src/app/mcp-config/
├── page.tsx                         # 主配置页面
├── types.ts                         # TypeScript 类型定义
├── hooks/
│   └── useMcpConfig.ts             # 配置管理 Hook
└── components/
    ├── AddServerModal.tsx          # 添加服务器模态框
    ├── ToolsModal.tsx              # 工具管理模态框
    ├── StatsCards.tsx              # 统计卡片组件
    ├── ToolsList.tsx               # 工具列表组件
    ├── PageHeader.tsx              # 页面头部组件
    └── LoadingSpinner.tsx          # 加载动画组件
```

### API 层 (`/frontend/src/app/api/mcp/`)

```
frontend/src/app/api/mcp/
├── call-tool/route.ts              # 工具调用 API
├── config/route.ts                 # 配置管理 API
├── server-list/route.ts            # 服务器列表 API
├── server-status/route.ts          # 服务器状态检查 API
├── servers/route.ts                # 服务器 CRUD API
├── status/route.ts                 # 系统状态 API
├── tool-config/route.ts            # 工具配置 API
├── tools/route.ts                  # 工具管理 API
└── validate/route.ts               # 服务器连接验证 API
```

## 数据库设计

### 核心表结构

#### 1. MCP 服务器表 (`mcp_servers`)

```sql
CREATE TABLE mcp_servers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,           -- 服务器唯一标识
  display_name TEXT NOT NULL,          -- 显示名称
  description TEXT,                    -- 服务器描述
  type TEXT NOT NULL,                  -- 类型: stdio/sse/streamable-http
  status TEXT NOT NULL DEFAULT 'disconnected', -- 连接状态
  enabled BOOLEAN NOT NULL DEFAULT 1,  -- 是否启用
  
  -- STDIO 配置
  command TEXT,                        -- 启动命令
  args TEXT,                          -- 命令参数 (JSON)
  working_directory TEXT,              -- 工作目录
  
  -- SSE/HTTP 配置
  url TEXT,                           -- 服务器 URL
  base_url TEXT,                      -- 基础 URL
  port INTEGER,                       -- 端口号
  path TEXT DEFAULT '/',              -- 路径
  protocol TEXT DEFAULT 'http',       -- 协议
  
  -- 认证和配置
  headers TEXT,                       -- 请求头 (JSON)
  auth_type TEXT,                     -- 认证类型
  auth_config TEXT,                   -- 认证配置 (JSON)
  timeout_ms INTEGER DEFAULT 30000,   -- 超时时间
  retry_attempts INTEGER DEFAULT 3,   -- 重试次数
  
  -- 时间戳
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_connected_at DATETIME,
  error_message TEXT
);
```

#### 2. MCP 工具表 (`mcp_tools`)

```sql
CREATE TABLE mcp_tools (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  server_id INTEGER NOT NULL,          -- 关联服务器
  name TEXT NOT NULL,                  -- 工具名称
  description TEXT,                    -- 工具描述
  input_schema TEXT,                   -- 输入参数模式 (JSON)
  is_available BOOLEAN DEFAULT 1,     -- 是否可用
  enabled BOOLEAN DEFAULT 1,          -- 是否启用
  last_used_at DATETIME,              -- 最后使用时间
  usage_count INTEGER DEFAULT 0,      -- 使用次数
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,
  UNIQUE(server_id, name)
);
```

#### 3. MCP 工具调用记录表 (`mcp_tool_calls`)

```sql
CREATE TABLE mcp_tool_calls (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  tool_id INTEGER NOT NULL,            -- 关联工具
  conversation_id INTEGER NOT NULL,    -- 关联对话
  message_id INTEGER,                  -- 关联消息
  input_args TEXT,                     -- 输入参数 (JSON)
  output_result TEXT,                  -- 输出结果 (JSON)
  execution_time_ms INTEGER,           -- 执行时间
  status TEXT NOT NULL,                -- 执行状态: success/error/timeout
  error_message TEXT,                  -- 错误信息
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (tool_id) REFERENCES mcp_tools (id) ON DELETE CASCADE,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
);
```

## API 层详解

### 核心 API 端点

#### 1. 服务器管理 API

- **`GET /api/mcp/servers`**: 获取所有服务器列表
- **`POST /api/mcp/servers`**: 创建新服务器
- **`PUT /api/mcp/servers/[id]`**: 更新服务器配置
- **`DELETE /api/mcp/servers/[id]`**: 删除服务器
- **`GET /api/mcp/server-status`**: 检查服务器连接状态
- **`POST /api/mcp/validate`**: 验证服务器连接

#### 2. 工具管理 API

- **`GET /api/mcp/tools`**: 获取所有工具列表
- **`POST /api/mcp/call-tool`**: 调用工具执行
- **`GET /api/mcp/tool-config`**: 获取工具配置
- **`PUT /api/mcp/tools/[id]`**: 更新工具配置

#### 3. 系统状态 API

- **`GET /api/mcp/status`**: 获取系统整体状态
- **`GET /api/mcp/server-list`**: 获取服务器列表和统计信息

### API 设计特点

1. **RESTful 设计**: 遵循 REST 架构原则
2. **统一错误处理**: 标准化的错误响应格式
3. **数据验证**: 输入参数验证和类型检查
4. **异步处理**: 支持长时间运行的工具调用
5. **缓存机制**: 工具列表和服务器状态缓存

## 前端架构详解

### 1. 配置页面组件层次

```
MCP Config Page (page.tsx)
├── PageHeader                      # 页面头部和导航
├── StatsCards                      # 统计信息卡片
│   ├── 服务器数量统计
│   ├── 工具数量统计
│   └── 连接状态统计
├── Server Management Section        # 服务器管理区域
│   ├── AddServerModal              # 添加服务器模态框
│   └── Server List Display         # 服务器列表显示
└── Tools Management Section         # 工具管理区域
    ├── ToolsList                   # 工具列表组件
    └── ToolsModal                  # 工具详情模态框
```

### 2. 状态管理 (`useMcpConfig` Hook)

**核心状态**:
- `servers`: 服务器列表
- `tools`: 工具列表
- `selectedServer`: 当前选中的服务器
- `loading`: 加载状态
- `error`: 错误信息

**核心方法**:
- `fetchServers()`: 获取服务器列表
- `fetchTools()`: 获取工具列表
- `addServer()`: 添加新服务器
- `updateServer()`: 更新服务器配置
- `deleteServer()`: 删除服务器
- `testConnection()`: 测试服务器连接

### 3. 数据流架构

```
用户操作 → 前端组件 → useMcpConfig Hook → API 调用 → 后端处理 → 数据库操作
    ↓                                                              ↓
界面更新 ← 状态更新 ← Hook 状态管理 ← API 响应 ← 业务逻辑 ← 数据查询/更新
```

## 核心组件详解

### 1. mcp-client.ts - 浏览器端客户端代理

**作用**: 为浏览器环境提供 MCP 功能的代理层

**主要功能**:
- 通过 HTTP API 与服务器端 MCP 功能通信
- 提供统一的客户端接口给前端组件使用
- 管理工具列表和连接状态
- 支持单个和批量工具调用

**核心类**:
- `McpClientProxy`: 浏览器端代理类，通过 `/api/mcp/*` 路由与后端通信

**导出函数**:
- `initializeMcpClient()`: 初始化客户端
- `getMcpTools()`: 获取可用工具列表
- `executeMcpTool()`: 执行工具调用

### 2. mcp-client-server.ts - 服务器端客户端实现

**作用**: 在 Node.js 环境中直接与 MCP 服务器通信

**主要功能**:
- 使用 stdio 传输方式连接本地 MCP 服务器
- 管理工具列表和执行工具调用
- 清理 inputSchema 以符合 Ollama 要求
- 提供完整的连接生命周期管理

**核心类**:
- `McpServerClient`: 服务器端客户端类，直接使用 MCP SDK

**传输方式**: StdioClientTransport (标准输入输出)

### 3. mcp-client-sse.ts - SSE 客户端实现

**作用**: 通过 Server-Sent Events 连接远程 MCP 服务器

**主要功能**:
- 支持 HTTP SSE 传输方式
- 实现重试机制和错误处理
- 支持自定义请求头和认证
- 符合 MCP 协议规范的会话管理

**核心特性**:
- 自动重试连接（默认3次）
- 会话ID管理
- 协议版本控制
- 超时处理

### 4. mcp-client-streamable-http.ts - Streamable HTTP 客户端

**作用**: 使用官方推荐的 Streamable HTTP 传输方式连接远程服务器

**主要功能**:
- 实现最新的 Streamable HTTP 传输协议
- VPN 环境检测和兼容模式
- 代理设置支持
- 高级网络配置选项

**核心特性**:
- VPN 环境自动检测
- 网络延迟监控
- 代理服务器支持
- 增强的错误处理

### 5. mcp-multi-server-client.ts - 多服务器管理器

**作用**: 统一管理多个不同类型的 MCP 服务器连接

**主要功能**:
- 同时管理 stdio、SSE 和 Streamable HTTP 服务器
- 提供统一的工具调用接口
- 服务器配置管理
- 连接状态监控

**支持的服务器类型**:
- `stdio`: 本地标准输入输出服务器
- `sse`: 远程 SSE 服务器
- `streamable-http`: 远程 Streamable HTTP 服务器

**核心类**:
- `MultiServerMcpClient`: 多服务器管理器
- `McpServerConfig`: 服务器配置接口
- `ExtendedMcpTool`: 扩展工具接口（包含服务器信息）

### 6. mcp-server.ts - MCP 服务器实现

**作用**: 实现本地 MCP 服务器，提供内置工具

**内置工具**:
- `calculate`: 数学表达式计算器
- `get_current_time`: 获取当前时间

**主要功能**:
- 使用 Zod 进行参数验证
- 安全的表达式计算
- 本地化时间格式
- 独立运行支持

**核心函数**:
- `createMcpServer()`: 创建服务器实例
- `startMcpServer()`: 启动服务器

### 7. mcp-tools.ts - 工具管理器

**作用**: 提供高级的 MCP 工具管理和调用功能

**主要功能**:
- 工具生命周期管理
- 批量工具调用
- Ollama 工具格式转换
- 连接状态管理

**核心类**:
- `McpClientManager`: 工具管理器类

**导出函数**:
- `callMcpTool()`: 简化的工具调用
- `getAllMcpTools()`: 获取所有工具
- `isMcpClientConnected()`: 检查连接状态

## 数据接口

### 核心接口

```typescript
// 工具定义
interface McpTool {
  name: string;
  description: string;
  inputSchema: any;
}

// 工具调用
interface McpToolCall {
  name: string;
  arguments: Record<string, any>;
}

// 工具结果
interface McpToolResult {
  content: Array<{
    type: 'text';
    text: string;
  }>;
  isError?: boolean;
}
```

### 服务器配置

```typescript
interface McpServerConfig {
  [serverName: string]: {
    type: 'stdio' | 'sse' | 'streamable-http';
    url?: string;
    command?: string;
    args?: string[];
    apiKey?: string;
    headers?: Record<string, string>;
    timeout?: number;
    retryAttempts?: number;
    protocolVersion?: string;
    vpnCompatible?: boolean;
  };
}
```

## 架构设计原则

### 1. 分层架构
- **传输层**: 不同的传输实现（stdio, SSE, Streamable HTTP）
- **客户端层**: 统一的客户端接口
- **管理层**: 多服务器和工具管理
- **应用层**: 面向业务的 API

### 2. 环境分离
- **浏览器端**: 通过 API 代理访问 MCP 功能
- **服务器端**: 直接使用 MCP SDK
- **混合模式**: 支持多种传输方式的统一管理

### 3. 错误处理
- 统一的错误处理机制
- 重试和降级策略
- 详细的错误日志

### 4. 扩展性
- 插件化的传输方式
- 可配置的服务器类型
- 灵活的工具注册机制

## 使用场景

### 1. 本地开发
使用 `mcp-server.ts` 提供的内置工具进行开发和测试。

### 2. 远程服务集成
通过 SSE 或 Streamable HTTP 客户端连接到远程 MCP 服务器。

### 3. 多服务器环境
使用多服务器管理器同时连接多个不同类型的服务器。

### 4. 浏览器应用
通过客户端代理在浏览器中使用 MCP 功能。

## 配置示例

### 多服务器配置

```typescript
const config: McpServerConfig = {
  'local-tools': {
    type: 'stdio',
    command: 'node',
    args: ['mcp-server.js']
  },
  'remote-api': {
    type: 'sse',
    url: 'https://api.example.com/mcp',
    apiKey: 'your-api-key',
    timeout: 30000
  },
  'advanced-service': {
    type: 'streamable-http',
    url: 'https://advanced.example.com/mcp',
    vpnCompatible: true,
    retryAttempts: 5
  }
};
```

## 组件间关系分析

### 1. 数据流关系

```
前端配置界面 (mcp-config)
    ↓ HTTP 请求
API 层 (/api/mcp)
    ↓ 数据库操作
数据库层 (SQLite)
    ↓ 配置读取
MCP 核心库 (/lib/mcp)
    ↓ 协议通信
外部 MCP 服务器
```

### 2. 模块依赖关系

- **前端组件** 依赖 **useMcpConfig Hook** 进行状态管理
- **useMcpConfig Hook** 依赖 **API 层** 进行数据交互
- **API 层** 依赖 **数据库层** 进行数据持久化
- **API 层** 依赖 **MCP 核心库** 进行协议通信
- **MCP 核心库** 实现不同传输协议的客户端

### 3. 配置管理流程

1. **添加服务器**:
   ```
   用户填写表单 → 前端验证 → API 验证连接 → 保存到数据库 → 更新前端状态
   ```

2. **工具发现**:
   ```
   连接服务器 → 获取工具列表 → 保存工具信息 → 更新可用状态
   ```

3. **工具调用**:
   ```
   用户触发调用 → API 路由处理 → MCP 客户端执行 → 记录调用结果 → 返回响应
   ```

## 技术特性

### 1. 多协议支持

- **SSE (Server-Sent Events)**: 适用于实时数据推送场景
- **Streamable HTTP**: 官方推荐的传输方式，支持 VPN 兼容
- **STDIO**: 适用于本地进程通信

### 2. 安全机制

- **输入验证**: 所有 API 端点都进行严格的参数验证
- **认证支持**: 支持多种认证方式（Bearer、Basic、API Key）
- **错误隔离**: 工具调用错误不会影响其他组件
- **连接管理**: 自动处理连接超时和重试

### 3. 性能优化

- **连接复用**: 复用 MCP 客户端连接
- **批量操作**: 支持批量工具调用
- **缓存机制**: 缓存工具列表和服务器状态
- **异步处理**: 非阻塞的工具执行

### 4. 可扩展性

- **插件化架构**: 易于添加新的传输协议
- **模块化设计**: 组件间低耦合，高内聚
- **配置驱动**: 通过配置支持不同类型的服务器
- **类型安全**: 完整的 TypeScript 类型定义

## 最佳实践

### 1. 服务器配置

- **连接管理**: 实现连接池和自动重连机制
- **错误处理**: 提供详细的错误信息和恢复建议
- **性能优化**: 使用缓存减少重复请求
- **安全性**: 验证所有输入参数，防止注入攻击

### 2. 工具开发

- **参数验证**: 严格验证工具输入参数
- **错误边界**: 实现工具级别的错误隔离
- **日志记录**: 记录工具调用和执行结果
- **性能监控**: 监控工具执行时间和资源使用

### 3. 前端开发

- **状态管理**: 使用 React Hooks 进行状态管理
- **错误处理**: 提供用户友好的错误提示
- **加载状态**: 显示操作进度和加载状态
- **响应式设计**: 支持不同屏幕尺寸

### 4. API 设计

- **RESTful 原则**: 遵循 REST 架构设计
- **统一响应格式**: 标准化的 API 响应结构
- **版本控制**: 为 API 提供版本管理
- **文档完善**: 提供详细的 API 文档

## 依赖关系

### 核心依赖

- **@modelcontextprotocol/sdk**: MCP 官方 SDK
- **better-sqlite3**: SQLite 数据库驱动
- **next.js**: Web 框架和 API 路由
- **react**: 前端 UI 框架
- **typescript**: 类型系统
- **tailwindcss**: CSS 框架
- **lucide-react**: 图标库

### 开发依赖

- **@types/node**: Node.js 类型定义
- **@types/react**: React 类型定义
- **eslint**: 代码质量检查
- **prettier**: 代码格式化

## 总结

本 MCP 架构提供了一个完整、可扩展的解决方案，实现了从前端配置界面到后端协议通信的全栈 MCP 集成。系统具有以下核心优势：

### 架构优势

1. **分层架构**: 清晰的前端、API、数据库、协议层分离
2. **多协议支持**: SSE、Streamable HTTP、STDIO 三种传输方式
3. **统一接口**: 标准化的工具调用和管理接口
4. **数据持久化**: 完整的数据库设计和 ORM 支持
5. **类型安全**: 完整的 TypeScript 类型定义

### 功能特性

1. **可视化配置**: 直观的 Web 界面进行服务器和工具管理
2. **实时状态监控**: 服务器连接状态和工具可用性监控
3. **灵活认证**: 支持多种认证方式和自定义配置
4. **错误处理**: 完善的错误处理和用户反馈机制
5. **性能优化**: 连接复用、批量操作、缓存机制

### 扩展性

1. **插件化设计**: 易于添加新的传输协议和工具类型
2. **配置驱动**: 通过数据库配置支持动态服务器管理
3. **模块化组件**: 前端组件可复用，API 端点可独立扩展
4. **标准协议**: 基于 MCP 标准，兼容生态系统

该架构为 AI 应用提供了强大的工具集成能力，使得 AI 模型能够安全、高效地与外部系统进行交互，同时提供了完善的管理和监控功能。