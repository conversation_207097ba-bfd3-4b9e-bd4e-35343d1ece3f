/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.fade-in-char {
    position: relative;
    opacity: 0;
    animation: fadeInChar 0.1s ease-out forwards;
  }

@keyframes fadeInChar {
    to {
      opacity: 1;
    }
  }
.\!container{
  width: 100% !important;
}
.container{
  width: 100%;
}
@media (min-width: 640px){

  .\!container{
    max-width: 640px !important;
  }

  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){

  .\!container{
    max-width: 768px !important;
  }

  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){

  .\!container{
    max-width: 1024px !important;
  }

  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){

  .\!container{
    max-width: 1280px !important;
  }

  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){

  .\!container{
    max-width: 1536px !important;
  }

  .container{
    max-width: 1536px;
  }
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.inset-0{
  inset: 0px;
}
.-left-0\.5{
  left: -0.125rem;
}
.-left-1{
  left: -0.25rem;
}
.-right-0\.5{
  right: -0.125rem;
}
.-right-1{
  right: -0.25rem;
}
.-right-2{
  right: -0.5rem;
}
.-top-0\.5{
  top: -0.125rem;
}
.-top-1{
  top: -0.25rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-40{
  bottom: 10rem;
}
.bottom-full{
  bottom: 100%;
}
.left-0{
  left: 0px;
}
.left-1\/2{
  left: 50%;
}
.left-3{
  left: 0.75rem;
}
.left-full{
  left: 100%;
}
.right-0{
  right: 0px;
}
.right-12{
  right: 3rem;
}
.right-2{
  right: 0.5rem;
}
.right-3{
  right: 0.75rem;
}
.top-0{
  top: 0px;
}
.top-1\/2{
  top: 50%;
}
.top-2{
  top: 0.5rem;
}
.top-20{
  top: 5rem;
}
.top-3{
  top: 0.75rem;
}
.top-full{
  top: 100%;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-30{
  z-index: 30;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-\[9999\]{
  z-index: 9999;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.-mb-px{
  margin-bottom: -1px;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-20{
  margin-left: 5rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.\!inline{
  display: inline !important;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.h-1{
  height: 0.25rem;
}
.h-10{
  height: 2.5rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-32{
  height: 8rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-7{
  height: 1.75rem;
}
.h-8{
  height: 2rem;
}
.h-\[18px\]{
  height: 18px;
}
.h-\[calc\(90vh-120px\)\]{
  height: calc(90vh - 120px);
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.h-screen{
  height: 100vh;
}
.max-h-48{
  max-height: 12rem;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-64{
  max-height: 16rem;
}
.max-h-72{
  max-height: 18rem;
}
.max-h-80{
  max-height: 20rem;
}
.max-h-96{
  max-height: 24rem;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.max-h-full{
  max-height: 100%;
}
.min-h-\[180px\]{
  min-height: 180px;
}
.min-h-\[2\.5rem\]{
  min-height: 2.5rem;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-full{
  min-height: 100%;
}
.min-h-screen{
  min-height: 100vh;
}
.min-h-0{
  min-height: 0px;
}
.w-1\/2{
  width: 50%;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-28{
  width: 7rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-32{
  width: 8rem;
}
.w-4{
  width: 1rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-7{
  width: 1.75rem;
}
.w-8{
  width: 2rem;
}
.w-96{
  width: 24rem;
}
.w-\[500px\]{
  width: 500px;
}
.w-full{
  width: 100%;
}
.w-px{
  width: 1px;
}
.min-w-0{
  min-width: 0px;
}
.min-w-\[18px\]{
  min-width: 18px;
}
.min-w-full{
  min-width: 100%;
}
.min-w-max{
  min-width: -moz-max-content;
  min-width: max-content;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-6xl{
  max-width: 72rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-\[75\%\]{
  max-width: 75%;
}
.max-w-\[80\%\]{
  max-width: 80%;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-sm{
  max-width: 24rem;
}
.max-w-xl{
  max-width: 36rem;
}
.max-w-xs{
  max-width: 20rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.border-collapse{
  border-collapse: collapse;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/2{
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-4{
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-help{
  cursor: help;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none{
  resize: none;
}
.list-inside{
  list-style-position: inside;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-1{
  gap: 0.25rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-x-8{
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-y-10 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-line{
  white-space: pre-line;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.break-words{
  overflow-wrap: break-word;
}
.break-all{
  word-break: break-all;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: var(--radius-2xl);
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius-lg);
}
.rounded-md{
  border-radius: var(--radius-md);
}
.rounded-xl{
  border-radius: var(--radius-xl);
}
.rounded-t-lg{
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}
.rounded-bl-lg{
  border-bottom-left-radius: var(--radius-lg);
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r{
  border-right-width: 1px;
}
.border-t{
  border-top-width: 1px;
}
.border-\[var\(--color-border\)\]{
  border-color: var(--color-border);
}
.border-\[var\(--color-error\)\]{
  border-color: var(--color-error);
}
.border-\[var\(--color-info\)\]{
  border-color: var(--color-info);
}
.border-\[var\(--color-success\)\]{
  border-color: var(--color-success);
}
.border-\[var\(--color-warning\)\]{
  border-color: var(--color-warning);
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-current{
  border-color: currentColor;
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-green-300{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-purple-200{
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-theme-border{
  border-color: var(--color-border);
}
.border-theme-input-border{
  border-color: var(--color-input-border);
}
.border-theme-primary{
  border-color: var(--color-primary);
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/30{
  border-color: rgb(255 255 255 / 0.3);
}
.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-t-current{
  border-top-color: currentColor;
}
.border-t-theme-primary{
  border-top-color: var(--color-primary);
}
.border-t-transparent{
  border-top-color: transparent;
}
.border-t-white{
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.bg-\[var\(--color-background-secondary\)\]{
  background-color: var(--color-background-secondary);
}
.bg-\[var\(--color-background-tertiary\)\]{
  background-color: var(--color-background-tertiary);
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/40{
  background-color: rgb(0 0 0 / 0.4);
}
.bg-black\/5{
  background-color: rgb(0 0 0 / 0.05);
}
.bg-black\/50{
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/10{
  background-color: rgb(59 130 246 / 0.1);
}
.bg-current{
  background-color: currentColor;
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-600{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-theme-background{
  background-color: var(--color-background);
}
.bg-theme-background-secondary{
  background-color: var(--color-background-secondary);
}
.bg-theme-background-tertiary{
  background-color: var(--color-background-tertiary);
}
.bg-theme-border{
  background-color: var(--color-border);
}
.bg-theme-card{
  background-color: var(--color-card);
}
.bg-theme-card-hover{
  background-color: var(--color-card-hover);
}
.bg-theme-error{
  background-color: var(--color-error);
}
.bg-theme-foreground-muted{
  background-color: var(--color-foreground-muted);
}
.bg-theme-input{
  background-color: var(--color-input);
}
.bg-theme-primary{
  background-color: var(--color-primary);
}
.bg-theme-secondary{
  background-color: var(--color-secondary);
}
.bg-theme-success{
  background-color: var(--color-success);
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-500{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500{
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-theme-primary{
  --tw-gradient-from: var(--color-primary) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-blue-600{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-purple-600{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-theme-accent{
  --tw-gradient-to: var(--color-accent) var(--tw-gradient-to-position);
}
.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}
.p-0\.5{
  padding: 0.125rem;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-12{
  padding: 3rem;
}
.p-2{
  padding: 0.5rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pb-8{
  padding-bottom: 2rem;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-4{
  padding-left: 1rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-8{
  padding-top: 2rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl{
  font-size: var(--font-size-2xl);
}
.text-base{
  font-size: var(--font-size-base);
}
.text-lg{
  font-size: var(--font-size-lg);
}
.text-sm{
  font-size: var(--font-size-sm);
}
.text-xl{
  font-size: var(--font-size-xl);
}
.text-xs{
  font-size: var(--font-size-xs);
}
.font-bold{
  font-weight: 700;
}
.font-medium{
  font-weight: 500;
}
.font-semibold{
  font-weight: 600;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.leading-6{
  line-height: 1.5rem;
}
.leading-7{
  line-height: 1.75rem;
}
.leading-\[1\.4\]{
  line-height: 1.4;
}
.leading-relaxed{
  line-height: var(--line-height-relaxed);
}
.\!text-theme-foreground-muted{
  color: var(--color-foreground-muted) !important;
}
.\!text-theme-foreground-secondary{
  color: var(--color-foreground-secondary) !important;
}
.text-\[var\(--color-error\)\]{
  color: var(--color-error);
}
.text-\[var\(--color-foreground\)\]{
  color: var(--color-foreground);
}
.text-\[var\(--color-foreground-muted\)\]{
  color: var(--color-foreground-muted);
}
.text-\[var\(--color-info\)\]{
  color: var(--color-info);
}
.text-\[var\(--color-success\)\]{
  color: var(--color-success);
}
.text-\[var\(--color-warning\)\]{
  color: var(--color-warning);
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-theme-error{
  color: var(--color-error);
}
.text-theme-foreground{
  color: var(--color-foreground);
}
.text-theme-foreground-muted{
  color: var(--color-foreground-muted);
}
.text-theme-foreground-secondary{
  color: var(--color-foreground-secondary);
}
.text-theme-primary{
  color: var(--color-primary);
}
.text-theme-success{
  color: var(--color-success);
}
.text-theme-warning{
  color: var(--color-warning);
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/80{
  color: rgb(255 255 255 / 0.8);
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.underline{
  text-decoration-line: underline;
}
.line-through{
  text-decoration-line: line-through;
}
.underline-offset-2{
  text-underline-offset: 2px;
}
.placeholder-theme-foreground-muted::-moz-placeholder{
  color: var(--color-foreground-muted);
}
.placeholder-theme-foreground-muted::placeholder{
  color: var(--color-foreground-muted);
}
.opacity-0{
  opacity: 0;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-90{
  opacity: 0.9;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: var(--shadow-lg);
  --tw-shadow-colored: var(--shadow-lg);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: var(--shadow-md);
  --tw-shadow-colored: var(--shadow-md);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: var(--shadow-sm);
  --tw-shadow-colored: var(--shadow-sm);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: var(--shadow-xl);
  --tw-shadow-colored: var(--shadow-xl);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-theme-primary{
  --tw-ring-color: var(--color-primary);
}
.ring-yellow-500\/50{
  --tw-ring-color: rgb(234 179 8 / 0.5);
}
.ring-offset-2{
  --tw-ring-offset-width: 2px;
}
.ring-offset-theme-background-secondary{
  --tw-ring-offset-color: var(--color-background-secondary);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/* 主题变量定义 - 浅色主题（默认） */
:root {
  /* 主题变量系统 */
  --color-background: #ffffff;
  --color-background-secondary: #f7f7f7;
  --color-background-tertiary: #f0f0f0;

  --color-foreground: #111111;
  --color-foreground-secondary: #555555;
  --color-foreground-muted: #777777;

  --color-border: #e5e5e5;
  --color-border-secondary: #dcdcdc;

  --color-card: #ffffff;
  --color-card-hover: #f7f7f7;

  --color-input: #ffffff;
  --color-input-border: #cccccc;
  --color-input-focus: #3b82f6;

  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
  --color-secondary: #6b7280;
  --color-secondary-hover: #4b5563;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-accent: #4930D9;
  --color-accent-hover: #3f26cf;

  /* 字体大小系统 */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */

  /* 行高系统 */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* 标题层级系统 */
  --heading-h1-size: var(--font-size-4xl);
  --heading-h1-weight: 700;
  --heading-h1-line-height: var(--line-height-tight);

  --heading-h2-size: var(--font-size-3xl);
  --heading-h2-weight: 600;
  --heading-h2-line-height: var(--line-height-tight);

  --heading-h3-size: var(--font-size-2xl);
  --heading-h3-weight: 600;
  --heading-h3-line-height: var(--line-height-snug);

  --heading-h4-size: var(--font-size-xl);
  --heading-h4-weight: 600;
  --heading-h4-line-height: var(--line-height-snug);

  --heading-h5-size: var(--font-size-lg);
  --heading-h5-weight: 500;
  --heading-h5-line-height: var(--line-height-normal);

  --heading-h6-size: var(--font-size-base);
  --heading-h6-weight: 500;
  --heading-h6-line-height: var(--line-height-normal);

  /* 页面标题系统 */
  --page-title-size: var(--font-size-3xl);
  --page-title-weight: 700;
  --page-title-line-height: var(--line-height-tight);

  --page-subtitle-size: var(--font-size-base);
  --page-subtitle-weight: 400;
  --page-subtitle-line-height: var(--line-height-normal);

  --section-title-size: var(--font-size-xl);
  --section-title-weight: 600;
  --section-title-line-height: var(--line-height-snug);

  --card-title-size: var(--font-size-xl);
  --card-title-weight: 600;
  --card-title-line-height: var(--line-height-snug);

  /* 间距系统 */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */
  --spacing-3xl: 4rem;      /* 64px */

  /* 圆角系统 */
  --radius-sm: 0.25rem;     /* 4px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.75rem;     /* 12px */
  --radius-xl: 1rem;        /* 16px */
  --radius-2xl: 1.5rem;     /* 24px */

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 传统变量（兼容现有代码） */
  --background: var(--color-background);
  --foreground: var(--color-foreground);
}

/* 深色主题变量 */
.dark {
  /* 主题变量系统 - 深色主题 */
  --color-background: #121212;
  --color-background-secondary: #1e1e1e;
  --color-background-tertiary: #282828;

  --color-foreground: #f5f5f5;
  --color-foreground-secondary: #b3b3b3;
  --color-foreground-muted: #808080;

  --color-border: #333333;
  --color-border-secondary: #444444;

  --color-card: #1e1e1e;
  --color-card-hover: #282828;

  --color-input: #1e1e1e;
  --color-input-border: #444444;
  --color-input-focus: #6854da;

  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
  --color-secondary: #6b7280;
  --color-secondary-hover: #9ca3af;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-accent: #4930D9;
  --color-accent-hover: #3f26cf;

  /* 深色主题下的阴影调整 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);

  /* 传统变量（兼容现有代码） */
  --background: var(--color-background);
  --foreground: var(--color-foreground);
}

/* 主题色定义 */
/* 默认主题: Kun */
:root,
.dark,
html[data-color-theme="kun"] {
  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
}

html[data-color-theme="green"] {
  --color-primary: #4D564F;
  --color-primary-rgb: 79, 186, 174;
  --color-primary-hover: #3A413B;
}

html[data-color-theme="purple"] {
  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
}

html[data-color-theme="orange"] {
  --color-primary: #E07800;
  --color-primary-rgb: 224, 120, 0;
  --color-primary-hover: #CC6D00;
}

html[data-color-theme="blue"] {
  --color-primary: #284B7B;
  --color-primary-rgb: 30, 56, 92;
  --color-primary-hover: #1E385C;
}

html[data-color-theme="raspberry"] {
  --color-primary: #EC4680;
  --color-primary-rgb: 236, 70, 128;
  --color-primary-hover: #E82167;
}

html[data-color-theme="moonstone"] {
  --color-primary: #61A0AF;
  --color-primary-rgb: 97, 160, 175;
  --color-primary-hover: #5293A3;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 全局标题样式类 */
.heading-h1 {
  font-size: var(--heading-h1-size);
  font-weight: var(--heading-h1-weight);
  line-height: var(--heading-h1-line-height);
  color: var(--color-foreground);
}

.heading-h2 {
  font-size: var(--heading-h2-size);
  font-weight: var(--heading-h2-weight);
  line-height: var(--heading-h2-line-height);
  color: var(--color-foreground);
}

.heading-h3 {
  font-size: var(--heading-h3-size);
  font-weight: var(--heading-h3-weight);
  line-height: var(--heading-h3-line-height);
  color: var(--color-foreground);
}

.heading-h4 {
  font-size: var(--heading-h4-size);
  font-weight: var(--heading-h4-weight);
  line-height: var(--heading-h4-line-height);
  color: var(--color-foreground);
}

.heading-h5 {
  font-size: var(--heading-h5-size);
  font-weight: var(--heading-h5-weight);
  line-height: var(--heading-h5-line-height);
  color: var(--color-foreground);
}

.heading-h6 {
  font-size: var(--heading-h6-size);
  font-weight: var(--heading-h6-weight);
  line-height: var(--heading-h6-line-height);
  color: var(--color-foreground);
}

/* 页面专用标题样式类 */
.page-title {
  font-size: var(--page-title-size);
  font-weight: var(--page-title-weight);
  line-height: var(--page-title-line-height);
  color: var(--color-foreground);
}

.page-subtitle {
  font-size: var(--page-subtitle-size);
  font-weight: var(--page-subtitle-weight);
  line-height: var(--page-subtitle-line-height);
  color: var(--color-foreground-muted);
}

.section-title {
  font-size: var(--section-title-size);
  font-weight: var(--section-title-weight);
  line-height: var(--section-title-line-height);
  color: var(--color-foreground);
}

.card-title {
  font-size: var(--card-title-size);
  font-weight: var(--card-title-weight);
  line-height: var(--card-title-line-height);
  color: var(--color-foreground);
}

/* 字体样式类 */
.font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-feature-settings: 'liga' 1, 'calt' 1;
}

.font-sans {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* 字重工具类 */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 防止闪烁的预加载样式 */
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主题切换时的平滑过渡 */
html.theme-changing * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

/* 自定义滚动条样式 - 支持主题 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.8);
}

/* 聊天容器响应式宽度控制 */
.chat-container-responsive {
  width: 100% !important;
  max-width: 100% !important;
}

/* 全屏模式：始终占满全屏宽度 */
.chat-container-responsive.fullscreen {
  width: 100% !important;
  max-width: 100% !important;
}

/* 紧凑模式：1920px以下全屏，1920px以上80%宽度 */
.chat-container-responsive.compact {
  width: 100% !important;
  max-width: 100% !important;
}

@media (min-width: 1920px) {
  .chat-container-responsive.compact {
    width: 50% !important;
    max-width: 50% !important;
  }
}

/* 3D立方体加载动画样式 */
.spinner {
  width: 16px;
  height: 16px;
  animation: spinner-y0fdc1 2s infinite ease;
  transform-style: preserve-3d;
}

/* 小尺寸3D立方体加载动画样式 */
.spinner-small {
  width: 18px;
  height: 18px;
  animation: spinner-y0fdc1 2s infinite ease;
  transform-style: preserve-3d;
}

.spinner > div,
.spinner-small > div {
  background-color: rgba(59, 130, 246, 0.1);
  height: 100%;
  position: absolute;
  width: 100%;
  border: 1px solid var(--color-primary);
}

.spinner div:nth-of-type(1) {
  transform: translateZ(-20px) rotateY(180deg);
}

.spinner div:nth-of-type(2) {
  transform: rotateY(-270deg) translateX(50%);
  transform-origin: top right;
}

.spinner div:nth-of-type(3) {
  transform: rotateY(270deg) translateX(-50%);
  transform-origin: center left;
}

.spinner div:nth-of-type(4) {
  transform: rotateX(90deg) translateY(-50%);
  transform-origin: top center;
}

.spinner div:nth-of-type(5) {
  transform: rotateX(-90deg) translateY(50%);
  transform-origin: bottom center;
}

.spinner div:nth-of-type(6) {
  transform: translateZ(20px);
}

/* 小尺寸立方体的变换样式 */
.spinner-small div:nth-of-type(1) {
  transform: translateZ(-12px) rotateY(180deg);
}

.spinner-small div:nth-of-type(2) {
  transform: rotateY(-270deg) translateX(50%);
  transform-origin: top right;
}

.spinner-small div:nth-of-type(3) {
  transform: rotateY(270deg) translateX(-50%);
  transform-origin: center left;
}

.spinner-small div:nth-of-type(4) {
  transform: rotateX(90deg) translateY(-50%);
  transform-origin: top center;
}

.spinner-small div:nth-of-type(5) {
  transform: rotateX(-90deg) translateY(50%);
  transform-origin: bottom center;
}

.spinner-small div:nth-of-type(6) {
  transform: translateZ(-12px);
}

@keyframes spinner-y0fdc1 {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotateY(90deg);
  }
  50% {
    transform: rotateY(90deg) rotateZ(90deg);
  }
  75% {
    transform: rotateY(180deg) rotateZ(90deg);
  }
  100% {
    transform: rotateY(180deg) rotateZ(180deg);
  }
}

/* 侧边栏相关样式 */
html[data-sidebar-state="collapsed"] .sidebar-container {
  width: 64px;
}

html[data-sidebar-state="expanded"] .sidebar-container {
  width: 208px;
}

.sidebar-container {
  transition: width 0.3s ease;
}

/* 防止hydration闪烁 - 最高优先级规则，立即生效 */
html[data-sidebar-state="collapsed"] .sidebar-text {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  width: 0 !important;
  overflow: hidden !important;
}

html[data-sidebar-state="expanded"] .sidebar-text {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: auto !important;
  overflow: visible !important;
}

/* 收缩状态下显示的元素 */
html[data-sidebar-state="expanded"] .sidebar-collapsed-only {
  display: none !important;
}

html[data-sidebar-state="collapsed"] .sidebar-collapsed-only {
  display: flex !important;
}

/* 按钮布局控制 */
html[data-sidebar-state="expanded"] .sidebar-button {
  justify-content: flex-start !important;
  padding: 0.75rem 1rem 0.75rem 0.75rem !important;
}

html[data-sidebar-state="collapsed"] .sidebar-button {
  justify-content: center !important;
  padding: 0.75rem !important;
}

/* 图标容器样式 */
html[data-sidebar-state="collapsed"] .sidebar-icon-container {
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  margin: 0 auto !important;
}

html[data-sidebar-state="expanded"] .sidebar-icon-container {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  flex-shrink: 0 !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  margin: 0 !important;
}

/* 工具提示显示控制 */
html[data-sidebar-state="collapsed"] .sidebar-tooltip {
  display: flex !important;
}

html[data-sidebar-state="expanded"] .sidebar-tooltip {
  display: none !important;
}

/* 导航栏选中状态的右侧竖条样式 */
.sidebar-nav-item {
  position: relative;
}

.sidebar-nav-item.active {
  background-color: transparent !important;
}

.sidebar-nav-item.active::after {
  content: '';
  position: absolute;
  right: -1px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 32px;
  background: linear-gradient(135deg, 
    var(--color-primary) 0%, 
    var(--color-primary-hover) 100%);
  border-radius: 3px 0 0 3px;
  box-shadow: 0 2px 6px rgba(var(--color-primary-rgb), 0.25);
  transition: all 0.2s ease-in-out;
}

/* 收缩状态下的选中竖条调整 */
html[data-sidebar-state="collapsed"] .sidebar-nav-item.active::after {
  right: 0;
  height: 28px;
  width: 4px;
}

/* 悬停状态保持原有效果 */
.sidebar-nav-item:hover {
  background-color: var(--theme-card-hover) !important;
}

.sidebar-nav-item.active:hover {
  background-color: var(--theme-card-hover) !important;
}

.sidebar-nav-item.active:hover::after {
  height: 36px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

html[data-sidebar-state="collapsed"] .sidebar-nav-item.active:hover::after {
  height: 32px;
}

/* 收缩状态下主题切换区域的特殊处理 */
html[data-sidebar-state="collapsed"] .sidebar-button:has(.bg-gradient-to-br) {
  justify-content: center !important;
  padding: 0.75rem !important;
}

html[data-sidebar-state="collapsed"] .sidebar-button:has(.bg-gradient-to-br) .sidebar-icon-container {
  margin: 0 !important;
}

/* 主题切换图标容器特殊处理 */
html[data-sidebar-state="collapsed"] .theme-toggle-icon {
  margin: 0 auto !important;
  width: auto !important;
  height: auto !important;
}

/* 统一图标容器过渡效果，防止抖动 */
.sidebar-icon-container {
  transition: width 0.3s ease, height 0.3s ease, margin 0.3s ease !important;
}

/* 确保图标本身不会抖动 */
.sidebar-icon-container > * {
  transition: none !important;
}

/* 抽屉弹窗滑动动画 */
@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 菜单缩放动画 */
@keyframes scale-up {
  from {
    transform: scale(0.95) translateY(10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.animate-scale-up {
  animation: scale-up 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Markdown 渲染样式 */
.markdown-renderer {
  /* 确保代码块的容器正确显示 */
  .hljs {
    background: transparent !important;
  }
  
  /* 表格样式优化 */
  table {
    border-spacing: 0;
    border-collapse: collapse;
  }
  
  /* 列表样式优化 */
  ul, ol {
    padding-left: 1.5rem;
  }
  
  /* 代码块样式 */
  pre {
    overflow-x: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }
  
  /* 内联代码样式 */
  code {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Consolas', monospace;
  }
  
  /* 引用块样式 */
  blockquote {
    border-left: 4px solid var(--color-primary);
    margin: 1rem 0;
  }
  
  /* 链接样式 */
  a {
    color: var(--color-primary);
    text-decoration: underline;
    text-underline-offset: 2px;
  }
  
  a:hover {
    color: var(--color-primary-hover);
  }
}

/* 确保代码高亮组件在主题切换时正确显示 */
.markdown-renderer pre {
  background-color: var(--color-background-secondary) !important;
  border: 1px solid var(--color-border);
}

/* 深色主题下的代码高亮优化 */
.dark .markdown-renderer pre {
  background-color: #1e1e1e !important;
  border-color: var(--color-border);
}

/* 浅色主题下的代码高亮优化 */
html:not(.dark) .markdown-renderer pre {
  background-color: #f8f9fa !important;
  border-color: var(--color-border);
}

/* 表单输入框优化 */
.form-input-base {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  background-color: var(--color-background-secondary);
  color: var(--color-foreground);
  transition: all 0.2s ease-in-out;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.form-input-base:hover {
  border-color: var(--color-border-secondary);
}

.form-input-base:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
  background-color: var(--color-background);
}

.form-input-base::-moz-placeholder {
  color: var(--color-foreground-muted);
}

.form-input-base::placeholder {
  color: var(--color-foreground-muted);
}

/* 错误状态 */
.form-input-base.error {
  border-color: var(--color-error);
  background-color: rgba(var(--color-error), 0.05);
}

/* 按钮基础样式 */
.btn-base {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background-color: var(--color-background-secondary);
  color: var(--color-foreground);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-card-hover);
  border-color: var(--color-border-secondary);
}

/* 标签样式 */
.tag-base {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.tag-primary {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
}

/* Steam风格卡片样式 */
.steam-card {
  position: relative;
  background: linear-gradient(145deg, var(--color-card), var(--color-background-secondary));
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.steam-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    var(--color-primary) 0%, 
    var(--color-accent) 100%);
  opacity: 0.8;
}

.steam-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(var(--color-primary-rgb), 0.3);
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(var(--color-primary-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.steam-card-glow {
  position: absolute;
  inset: 0;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border: 1px solid rgba(var(--color-primary-rgb), 0.3);
  box-shadow: 
    0 0 20px rgba(var(--color-primary-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.steam-card:hover .steam-card-glow {
  opacity: 1;
}

/* Steam风格统计卡片 */
.steam-stat-card {
  background: var(--color-background-tertiary);
  border: 1px solid var(--color-border-secondary);
  border-radius: 0.75rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.steam-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(var(--color-primary-rgb), 0.3) 50%, 
    transparent 100%);
}

.steam-stat-card:hover {
  background: var(--color-card);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-1px);
}

/* Steam风格标签 */
.steam-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.steam-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    transparent 100%);
  transition: left 0.5s ease;
}

.steam-tag:hover::before {
  left: 100%;
}

.steam-tag-success {
  background-color: rgba(var(--color-success), 0.1);
  color: var(--color-success);
  border-color: rgba(var(--color-success), 0.2);
}

.steam-tag-warning {
  background-color: rgba(var(--color-warning), 0.1);
  color: var(--color-warning);
  border-color: rgba(var(--color-warning), 0.2);
}

.steam-tag-info {
  background-color: rgba(var(--color-info), 0.1);
  color: var(--color-info);
  border-color: rgba(var(--color-info), 0.2);
}

/* Steam风格按钮 */
.steam-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  border: none;
  background: transparent;
  color: var(--color-foreground-muted);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.steam-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.steam-button:hover {
  transform: scale(1.1);
}

.steam-button:hover::before {
  opacity: 0.1;
}

.steam-button-info:hover {
  color: var(--color-info);
}

.steam-button-warning:hover {
  color: var(--color-warning);
}

.steam-button-error:hover {
  color: var(--color-error);
}

/* Steam风格图标容器 */
.steam-icon-container {
  position: relative;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  box-shadow: 
    0 8px 16px rgba(var(--color-primary-rgb), 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.steam-icon-container:hover {
  box-shadow: 
    0 12px 24px rgba(var(--color-primary-rgb), 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.steam-icon-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  background: var(--color-success);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 2px solid var(--color-card);
}

/* Steam风格空状态 */
.steam-empty-state {
  background: linear-gradient(135deg, 
    var(--color-card) 0%, 
    var(--color-background-secondary) 100%);
  border: 1px solid var(--color-border);
  border-radius: 1.5rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.steam-empty-icon {
  background: linear-gradient(135deg, 
    rgba(var(--color-primary-rgb), 0.1) 0%, 
    rgba(var(--color-primary-rgb), 0.05) 100%);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
  border-radius: 1.5rem;
  box-shadow: 
    0 4px 12px rgba(var(--color-primary-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Steam风格加载动画增强 */
.steam-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 5rem 2rem;
}

.steam-loading-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-foreground-muted);
  text-align: center;
}

/* 响应式网格增强 */
.steam-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .steam-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  .steam-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1536px) {
  .steam-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Steam风格动画关键帧 */
@keyframes steam-glow {
  0%, 100% {
    box-shadow: 
      0 0 5px rgba(var(--color-primary-rgb), 0.3),
      0 0 10px rgba(var(--color-primary-rgb), 0.2),
      0 0 15px rgba(var(--color-primary-rgb), 0.1);
  }
  50% {
    box-shadow: 
      0 0 10px rgba(var(--color-primary-rgb), 0.4),
      0 0 20px rgba(var(--color-primary-rgb), 0.3),
      0 0 30px rgba(var(--color-primary-rgb), 0.2);
  }
}

@keyframes steam-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.steam-shimmer {
  position: relative;
  overflow: hidden;
}

.steam-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    transparent 100%);
  transform: translateX(-100%);
  animation: steam-shimmer 2s infinite;
}

/* 深色主题下的Steam风格优化 */
.dark .steam-card {
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dark .steam-card:hover {
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(var(--color-primary-rgb), 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}

.before\:inset-0::before{
  content: var(--tw-content);
  inset: 0px;
}

@keyframes spin{

  to{
    content: var(--tw-content);
    transform: rotate(360deg);
  }
}

.before\:animate-spin::before{
  content: var(--tw-content);
  animation: spin 1s linear infinite;
}

.before\:rounded-full::before{
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:border-2::before{
  content: var(--tw-content);
  border-width: 2px;
}

.before\:border-transparent::before{
  content: var(--tw-content);
  border-color: transparent;
}

.before\:border-r-white::before{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.before\:border-t-white::before{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.before\:content-\[\\\"\\\"\]::before{
  --tw-content: \"\";
  content: var(--tw-content);
}

.first\:mt-0:first-child{
  margin-top: 0px;
}

.last\:mb-0:last-child{
  margin-bottom: 0px;
}

.last\:border-r-0:last-child{
  border-right-width: 0px;
}

.focus-within\:border-theme-primary:focus-within{
  border-color: var(--color-primary);
}

.hover\:-translate-y-0\.5:hover{
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-red-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.hover\:border-theme-border:hover{
  border-color: var(--color-border);
}

.hover\:border-theme-border-secondary:hover{
  border-color: var(--color-border-secondary);
}

.hover\:bg-\[var\(--color-background-tertiary\)\]:hover{
  background-color: var(--color-background-tertiary);
}

.hover\:bg-\[var\(--color-card-hover\)\]:hover{
  background-color: var(--color-card-hover);
}

.hover\:bg-black\/5:hover{
  background-color: rgb(0 0 0 / 0.05);
}

.hover\:bg-blue-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-theme-background:hover{
  background-color: var(--color-background);
}

.hover\:bg-theme-background-secondary:hover{
  background-color: var(--color-background-secondary);
}

.hover\:bg-theme-background-tertiary:hover{
  background-color: var(--color-background-tertiary);
}

.hover\:bg-theme-card-hover:hover{
  background-color: var(--color-card-hover);
}

.hover\:bg-theme-primary:hover{
  background-color: var(--color-primary);
}

.hover\:bg-theme-primary-hover:hover{
  background-color: var(--color-primary-hover);
}

.hover\:bg-theme-secondary-hover:hover{
  background-color: var(--color-secondary-hover);
}

.hover\:bg-white\/20:hover{
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-yellow-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.hover\:from-blue-600:hover{
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-purple-700:hover{
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}

.hover\:text-gray-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-theme-error:hover{
  color: var(--color-error);
}

.hover\:text-theme-foreground:hover{
  color: var(--color-foreground);
}

.hover\:text-theme-primary:hover{
  color: var(--color-primary);
}

.hover\:text-theme-primary-hover:hover{
  color: var(--color-primary-hover);
}

.hover\:text-theme-success:hover{
  color: var(--color-success);
}

.hover\:text-theme-warning:hover{
  color: var(--color-warning);
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:shadow-lg:hover{
  --tw-shadow: var(--shadow-lg);
  --tw-shadow-colored: var(--shadow-lg);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: var(--shadow-xl);
  --tw-shadow-colored: var(--shadow-xl);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-theme-input-focus:focus{
  border-color: var(--color-input-focus);
}

.focus\:border-theme-primary:focus{
  border-color: var(--color-primary);
}

.focus\:border-transparent:focus{
  border-color: transparent;
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-theme-input-focus:focus{
  --tw-ring-color: var(--color-input-focus);
}

.focus\:ring-theme-primary:focus{
  --tw-ring-color: var(--color-primary);
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.group:hover .group-hover\:translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:text-theme-primary{
  color: var(--color-primary);
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group:hover .group-hover\:shadow-xl{
  --tw-shadow: var(--shadow-xl);
  --tw-shadow-colored: var(--shadow-xl);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:border-blue-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-900:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}

.dark\:border-green-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}

.dark\:border-purple-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}

.dark\:border-purple-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}

.dark\:border-red-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}

.dark\:border-red-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}

.dark\:border-yellow-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));
}

.dark\:bg-blue-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-900\/10:is(.dark *){
  background-color: rgb(30 58 138 / 0.1);
}

.dark\:bg-blue-900\/20:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:bg-gray-100:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800\/50:is(.dark *){
  background-color: rgb(31 41 55 / 0.5);
}

.dark\:bg-gray-900\/20:is(.dark *){
  background-color: rgb(17 24 39 / 0.2);
}

.dark\:bg-green-900\/20:is(.dark *){
  background-color: rgb(20 83 45 / 0.2);
}

.dark\:bg-purple-900\/10:is(.dark *){
  background-color: rgb(88 28 135 / 0.1);
}

.dark\:bg-red-900\/10:is(.dark *){
  background-color: rgb(127 29 29 / 0.1);
}

.dark\:bg-red-900\/20:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}

.dark\:bg-white\/5:is(.dark *){
  background-color: rgb(255 255 255 / 0.05);
}

.dark\:bg-yellow-900\/10:is(.dark *){
  background-color: rgb(113 63 18 / 0.1);
}

.dark\:text-blue-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-900:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-orange-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.dark\:text-red-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.dark\:text-red-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.dark\:hover\:border-red-400:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.dark\:hover\:bg-blue-900\/20:hover:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:hover\:bg-blue-900\/30:hover:is(.dark *){
  background-color: rgb(30 58 138 / 0.3);
}

.dark\:hover\:bg-gray-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-purple-800\/20:hover:is(.dark *){
  background-color: rgb(107 33 168 / 0.2);
}

.dark\:hover\:bg-red-900\/20:hover:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}

.dark\:hover\:bg-white\/5:hover:is(.dark *){
  background-color: rgb(255 255 255 / 0.05);
}

.dark\:hover\:text-gray-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-red-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px){

  .sm\:inline{
    display: inline;
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-between{
    justify-content: space-between;
  }

  .sm\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px){

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-center{
    align-items: center;
  }
}

@media (min-width: 1024px){

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px){

  .xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1536px){

  .\32xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}



/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_e8ce0c {font-family: 'Inter', 'Inter Fallback';font-style: normal
}

