# 模型管理模块使用指南

## 概述

模型管理模块为kunagent项目提供了完整的本地Ollama模型配置管理功能，支持中文模型名称、参数调优、标签管理等特性。

## 核心功能

### 1. 模型配置管理
- **创建自定义模型**：基于现有Ollama模型创建自定义配置
- **编辑模型参数**：支持温度、top_p、top_k、上下文窗口等参数调优
- **系统提示词**：为每个模型配置专属的系统提示词
- **参数验证**：自动验证参数范围，确保配置有效性

### 2. 中文模型名称支持
- **中文显示名称**：使用中文名称作为模型的显示名称
- **哈希映射机制**：自动生成唯一的内部标识符，解决Ollama不支持中文名称的问题
- **名称冲突检测**：防止重复名称，确保唯一性

### 3. 模型查看和管理
- **模型列表**：展示所有自定义模型配置
- **搜索和筛选**：支持按名称、标签、状态等条件筛选
- **排序功能**：按名称、创建时间、使用次数等排序
- **状态管理**：激活/禁用模型，收藏/取消收藏

### 4. 标签系统
- **多标签支持**：为模型添加多个标签进行分类
- **标签筛选**：根据标签快速找到相关模型
- **标签管理**：动态添加和删除标签

## 数据库结构

### custom_models 表
存储模型的基本信息和配置参数：

```sql
CREATE TABLE custom_models (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,           -- 中文显示名称
  model_hash TEXT NOT NULL UNIQUE,     -- 内部哈希名称
  base_model TEXT NOT NULL,            -- 基础模型名称
  description TEXT,                    -- 模型描述
  system_prompt TEXT,                  -- 系统提示词
  temperature REAL DEFAULT 0.7,        -- 温度参数
  top_p REAL DEFAULT 0.9,             -- Top-p参数
  top_k INTEGER DEFAULT 40,           -- Top-k参数
  repeat_penalty REAL DEFAULT 1.1,    -- 重复惩罚
  num_ctx INTEGER DEFAULT 4096,       -- 上下文窗口大小
  num_predict INTEGER DEFAULT 128,    -- 最大生成长度
  seed INTEGER,                       -- 随机种子
  num_thread INTEGER DEFAULT 4,       -- 线程数
  num_gpu INTEGER DEFAULT 1,          -- GPU数量
  use_mmap BOOLEAN DEFAULT 1,         -- 内存映射
  num_batch INTEGER DEFAULT 512,      -- 批处理大小
  num_keep INTEGER DEFAULT 0,         -- 保留token数
  stop TEXT,                          -- 停止词（JSON数组）
  template TEXT,                      -- 自定义模板
  license TEXT,                       -- 许可证信息
  is_active BOOLEAN DEFAULT 1,        -- 是否激活
  is_favorite BOOLEAN DEFAULT 0,      -- 是否收藏
  usage_count INTEGER DEFAULT 0,      -- 使用次数
  last_used_at DATETIME,              -- 最后使用时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### model_tags 表
存储模型标签关系：

```sql
CREATE TABLE model_tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  model_id INTEGER NOT NULL,          -- 模型ID
  tag TEXT NOT NULL,                  -- 标签名称
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES custom_models (id) ON DELETE CASCADE,
  UNIQUE(model_id, tag)
);
```

## API接口

### 1. 获取模型列表
```
GET /api/custom-models
```

查询参数：
- `search`: 搜索关键词
- `tags`: 标签筛选（逗号分隔）
- `isActive`: 是否只显示激活模型
- `isFavorite`: 是否只显示收藏模型
- `sortBy`: 排序字段（name, created_at, updated_at, usage_count）
- `sortOrder`: 排序方向（asc, desc）

### 2. 创建模型
```
POST /api/custom-models
```

请求体：
```json
{
  "name": "中文模型名称",
  "base_model": "llama3.2",
  "description": "模型描述",
  "system_prompt": "系统提示词",
  "temperature": 0.7,
  "top_p": 0.9,
  "top_k": 40,
  "tags": ["标签1", "标签2"]
}
```

### 3. 更新模型
```
PUT /api/custom-models/{id}
```

### 4. 删除模型
```
DELETE /api/custom-models/{id}
```

### 5. 切换收藏状态
```
PATCH /api/custom-models/{id}/favorite
```

### 6. 切换激活状态
```
PATCH /api/custom-models/{id}/active
```

### 7. 获取标签列表
```
GET /api/custom-models/tags
```

## 使用流程

### 1. 创建自定义模型
1. 访问模型管理页面（`/model-manager`）
2. 点击"新建模型"按钮
3. 填写基本信息：
   - 模型名称（中文）
   - 选择基础模型
   - 添加描述（可选）
4. 配置系统提示词（可选）
5. 调整生成参数：
   - 温度：控制输出的随机性（0-2）
   - Top-p：控制词汇选择的多样性（0-1）
   - Top-k：限制候选词汇数量（1-100）
   - 重复惩罚：避免重复内容（0.1-2.0）
   - 上下文窗口：处理长文本的能力（512-32768）
6. 添加停止词（可选）
7. 添加标签进行分类
8. 设置状态（激活/收藏）
9. 保存配置

### 2. 管理模型
- **搜索模型**：使用搜索框快速找到目标模型
- **筛选模型**：按标签、状态等条件筛选
- **排序模型**：按不同字段排序查看
- **编辑模型**：点击编辑按钮修改配置
- **删除模型**：删除不需要的配置
- **切换状态**：快速激活/禁用或收藏/取消收藏

### 3. 导入导出
- **导出配置**：将当前所有模型配置导出为JSON文件
- **导入配置**：从JSON文件导入模型配置（开发中）

## 技术特性

### 1. 中文名称映射
- 使用哈希算法生成唯一的内部标识符
- 格式：`custom_{hash}_{timestamp}`
- 确保与Ollama API的兼容性

### 2. 参数验证
- 使用Zod进行类型验证
- 自动设置合理的默认值
- 实时验证参数范围

### 3. 数据持久化
- 使用SQLite数据库存储
- 支持事务操作确保数据一致性
- 自动创建索引优化查询性能

### 4. 用户界面
- 响应式设计，支持移动端
- 现代化的UI组件
- 实时搜索和筛选
- 拖拽排序（计划中）

## 最佳实践

### 1. 模型命名
- 使用描述性的中文名称
- 避免过于复杂的名称
- 考虑添加版本号或日期

### 2. 参数调优
- 从默认值开始，逐步调整
- 记录不同参数组合的效果
- 为不同任务创建专门的配置

### 3. 标签管理
- 使用有意义的标签
- 建立标签体系
- 定期清理无用标签

### 4. 系统提示词
- 明确角色定位
- 设定行为规范
- 考虑上下文限制

## 故障排除

### 1. 常见问题
- **模型名称冲突**：系统会自动检测并提示
- **参数验证失败**：检查参数是否在有效范围内
- **数据库错误**：确保数据库文件权限正确

### 2. 性能优化
- 定期清理未使用的模型配置
- 合理设置上下文窗口大小
- 避免创建过多重复配置

## 未来计划

### 1. 功能增强
- 批量导入导出
- 模型配置模板
- 使用统计和分析
- 模型性能对比

### 2. 集成优化
- 与聊天界面深度集成
- 支持模型切换
- 自动参数调优
- 模型推荐系统

### 3. 用户体验
- 拖拽排序
- 批量操作
- 快捷键支持
- 主题定制

## 开发说明

### 1. 文件结构
```
frontend/src/
├── app/
│   └── model-manager/
│       ├── page.tsx                 # 主页面
│       └── components/
│           ├── ModelList.tsx        # 模型列表组件
│           ├── ModelForm.tsx        # 模型表单组件
│           └── SearchAndFilter.tsx  # 搜索筛选组件
├── lib/
│   └── database/
│       └── custom-models.ts         # 数据访问层
└── app/api/custom-models/           # API路由
```

### 2. 依赖关系
- `better-sqlite3`: 数据库操作
- `zod`: 数据验证
- `lucide-react`: 图标组件
- `tailwindcss`: 样式框架

### 3. 扩展开发
- 添加新的模型参数
- 实现新的筛选条件
- 集成第三方模型服务
- 添加模型性能监控 