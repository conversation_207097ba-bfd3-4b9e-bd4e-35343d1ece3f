# 统计信息和工具面板修复验证指南

## 验证步骤

### 1. 统计信息显示验证

**测试场景1：普通对话**
1. 发送一条普通消息给AI
2. 等待AI回复完成
3. 检查AI消息右侧的信息图标（ℹ️）
4. 悬停在图标上，应该显示详细的统计信息而不是"正在生成中..."

**预期结果**：
```
总时长: 8.36s
加载时长: 5.63s
提示词处理: 359 tokens
生成内容: 152 tokens
提示词速度: 1192.9 tokens/s
生成速度: 62.8 tokens/s
```

### 2. 工具调用面板验证

**测试场景2：工具调用**
1. 启用工具功能
2. 发送需要调用工具的消息（如"现在几点了？"）
3. 观察工具调用过程
4. 等待工具调用完成和AI回复完成
5. 刷新页面或重新进入对话

**预期结果**：
- 工具调用应该显示为橙色工具面板，包含工具名称、参数、执行结果
- 不应该显示为普通的文本气泡"工具调用: get_current_time"
- 面板应该可以展开/折叠查看详细信息

### 3. 重试场景验证

**测试场景3：不支持工具的模型**
1. 选择一个不支持工具的模型
2. 启用工具功能
3. 发送需要调用工具的消息
4. 观察系统自动重试不使用工具
5. 检查最终AI回复的统计信息

**预期结果**：
- 系统应该自动重试并成功生成回复
- AI回复应该包含完整的统计信息
- 控制台应该显示重试相关的日志

## 调试日志检查

### 浏览器控制台日志

**统计信息相关日志**：
```
🔧 收到统计信息: {total_duration: 8355132500, ...}
🔧 保存助手消息，统计信息: {total_duration: 8355132500, ...}
🔧 重新加载消息以获取统计信息: 5
🔧 重新加载的消息是否包含统计信息: true
```

**工具调用相关日志**：
```
🔧 后端发送 tool_call_start，toolCall.id: get_current_time-xxx
🔧 后端发送 tool_call_complete，toolCall.id: get_current_time-xxx
🔧 重新加载的工具调用数量: 1
```

### 数据库验证

**检查统计信息保存**：
```sql
SELECT id, role, total_duration, eval_count, model 
FROM messages 
WHERE role = 'assistant' 
ORDER BY created_at DESC 
LIMIT 3;
```

**检查工具调用保存**：
```sql
SELECT id, role, tool_name, tool_status, tool_execution_time 
FROM messages 
WHERE role = 'tool_call' 
ORDER BY created_at DESC 
LIMIT 3;
```

## 常见问题排查

### 问题1：统计信息仍显示"正在生成中"

**可能原因**：
- 数据库保存失败
- 前端重新加载逻辑有问题
- 模型返回的统计信息格式异常

**排查步骤**：
1. 检查浏览器控制台的统计信息日志
2. 检查数据库中是否保存了统计信息
3. 检查网络请求是否成功

### 问题2：工具面板变成普通文本

**可能原因**：
- `onStreamEnd`重新加载逻辑未正确处理工具调用消息
- 工具调用消息的数据格式有问题

**排查步骤**：
1. 检查控制台的工具调用重新加载日志
2. 检查数据库中工具调用消息的数据
3. 确认页面刷新后工具面板是否正常

### 问题3：重试场景统计信息丢失

**可能原因**：
- 重试逻辑中没有正确保存统计信息
- `assistantStats`变量没有正确累积

**排查步骤**：
1. 检查控制台的重试场景日志
2. 观察是否有"模型不支持工具调用，尝试不使用工具重新请求"消息
3. 检查重试后的统计信息保存日志

## 性能指标参考

### 正常统计信息范围

- **总时长**：通常在1-30秒之间
- **加载时长**：通常在0.1-10秒之间
- **生成速度**：通常在10-200 tokens/s之间
- **提示词处理速度**：通常在100-2000 tokens/s之间

### 异常指标

- 总时长为0或null：表示统计信息未正确获取
- 生成速度异常高（>1000 tokens/s）：可能是计算错误
- 加载时长过长（>30秒）：可能是模型加载问题

## 修复验证清单

- [ ] 普通对话显示完整统计信息
- [ ] 工具调用显示为工具面板而非文本气泡
- [ ] 页面刷新后工具面板保持正确样式
- [ ] 不支持工具的模型能正确重试并显示统计信息
- [ ] 控制台日志显示完整的数据流程
- [ ] 数据库正确保存统计信息和工具调用数据 