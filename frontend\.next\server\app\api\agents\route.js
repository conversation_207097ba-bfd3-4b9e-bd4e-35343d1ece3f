/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/agents/route";
exports.ids = ["app/api/agents/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_agents_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/agents/route.ts */ \"(rsc)/./src/app/api/agents/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/agents/route\",\n        pathname: \"/api/agents\",\n        filename: \"route\",\n        bundlePath: \"app/api/agents/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\agents\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_agents_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhZ2VudHMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFnZW50cyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFnZW50cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNaYWNrJTVDRGVza3RvcCU1Q1JQMzBfa3VuYWdlbnQlNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDWmFjayU1Q0Rlc2t0b3AlNUNSUDMwX2t1bmFnZW50JTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ2tDO0FBQy9HO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxaYWNrXFxcXERlc2t0b3BcXFxcUlAzMF9rdW5hZ2VudFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhZ2VudHNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FnZW50cy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FnZW50c1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYWdlbnRzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcWmFja1xcXFxEZXNrdG9wXFxcXFJQMzBfa3VuYWdlbnRcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYWdlbnRzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/agents/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/agents/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// GET /api/agents - 获取所有智能体\nasync function GET() {\n    try {\n        const agents = _lib_database__WEBPACK_IMPORTED_MODULE_1__.agentOperations.getAll();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(agents);\n    } catch (error) {\n        console.error('Failed to get agents:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to retrieve agents'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/agents - 创建新智能体\nconst createAgentSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, 'Name is required'),\n    description: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullable().optional().default(null),\n    model_id: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().positive('Model ID must be a positive integer'),\n    system_prompt: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullable().optional().default(null),\n    server_ids: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().positive()).optional().default([]),\n    tool_ids: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().positive()).optional().default([])\n});\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validation = createAgentSchema.safeParse(body);\n        if (!validation.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid input',\n                details: validation.error.format()\n            }, {\n                status: 400\n            });\n        }\n        const newAgent = _lib_database__WEBPACK_IMPORTED_MODULE_1__.agentOperations.create(validation.data);\n        if (!newAgent) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create agent'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newAgent, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Failed to create agent:', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid input',\n                details: error.format()\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create agent'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/agents/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.agentOperations),\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _database_connection__WEBPACK_IMPORTED_MODULE_1__.db),\n/* harmony export */   mcpDbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _database_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/index */ \"(rsc)/./src/lib/database/index.ts\");\n/* harmony import */ var _database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n// 重新导出模块化的数据库操作\n// 这个文件现在作为向后兼容的入口点，所有实际的实现都在 ./database/ 目录下\n\n// 重新导出数据库连接作为默认导出（保持向后兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjtBQUNoQiw2Q0FBNkM7QUFDWjtBQUVqQywyQkFBMkI7QUFDMkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcbGliXFxkYXRhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDph43mlrDlr7zlh7rmqKHlnZfljJbnmoTmlbDmja7lupPmk43kvZxcbi8vIOi/meS4quaWh+S7tueOsOWcqOS9nOS4uuWQkeWQjuWFvOWuueeahOWFpeWPo+eCue+8jOaJgOacieWunumZheeahOWunueOsOmDveWcqCAuL2RhdGFiYXNlLyDnm67lvZXkuItcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UvaW5kZXgnO1xuXG4vLyDph43mlrDlr7zlh7rmlbDmja7lupPov57mjqXkvZzkuLrpu5jorqTlr7zlh7rvvIjkv53mjIHlkJHlkI7lhbzlrrnmgKfvvIlcbmV4cG9ydCB7IGRiIGFzIGRlZmF1bHQgfSBmcm9tICcuL2RhdGFiYXNlL2Nvbm5lY3Rpb24nOyJdLCJuYW1lcyI6WyJkYiIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/agents.ts":
/*!************************************!*\
  !*** ./src/lib/database/agents.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentOperations: () => (/* binding */ agentOperations)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _custom_models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./custom-models */ \"(rsc)/./src/lib/database/custom-models.ts\");\n\n\nconst agentWithRelationsQuery = `\n  SELECT\n    a.*,\n    (\n      SELECT json_group_array(json_object('id', s.id, 'name', s.name, 'display_name', s.display_name))\n      FROM agent_mcp_servers ams\n      JOIN mcp_servers s ON ams.server_id = s.id\n      WHERE ams.agent_id = a.id\n    ) as servers,\n    (\n      SELECT json_group_array(json_object('id', t.id, 'name', t.name, 'description', t.description))\n      FROM agent_tools at\n      JOIN mcp_tools t ON at.tool_id = t.id\n      WHERE at.agent_id = a.id\n    ) as tools\n  FROM agents a\n`;\nfunction mapRowToAgent(row) {\n    const model = _custom_models__WEBPACK_IMPORTED_MODULE_1__.CustomModelService.getById(row.model_id);\n    if (!model) {\n        throw new Error(`Could not find model with id ${row.model_id} for agent ${row.id}`);\n    }\n    return {\n        ...row,\n        model,\n        servers: row.servers ? JSON.parse(row.servers) : [],\n        tools: row.tools ? JSON.parse(row.tools) : []\n    };\n}\nconst agentOperations = {\n    create (data) {\n        const createAgentStmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO agents (name, description, model_id, system_prompt) VALUES (?, ?, ?, ?)');\n        const linkServerStmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO agent_mcp_servers (agent_id, server_id) VALUES (?, ?)');\n        const linkToolStmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO agent_tools (agent_id, tool_id) VALUES (?, ?)');\n        const transaction = _connection__WEBPACK_IMPORTED_MODULE_0__.db.transaction((agentData)=>{\n            const { name, description, model_id, system_prompt, server_ids, tool_ids } = agentData;\n            const result = createAgentStmt.run(name, description || null, model_id, system_prompt || null);\n            const agentId = result.lastInsertRowid;\n            if (server_ids) {\n                for (const server_id of server_ids){\n                    linkServerStmt.run(agentId, server_id);\n                }\n            }\n            if (tool_ids) {\n                for (const tool_id of tool_ids){\n                    linkToolStmt.run(agentId, tool_id);\n                }\n            }\n            return agentId;\n        });\n        try {\n            const agentId = transaction(data);\n            return this.getById(agentId);\n        } catch (error) {\n            console.error('Failed to create agent:', error);\n            return null;\n        }\n    },\n    getById (id) {\n        const row = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`${agentWithRelationsQuery} WHERE a.id = ?`).get(id);\n        return row ? mapRowToAgent(row) : null;\n    },\n    getAll () {\n        const rows = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`${agentWithRelationsQuery} ORDER BY a.created_at DESC`).all();\n        return rows.map(mapRowToAgent);\n    },\n    update (data) {\n        const { id, server_ids, tool_ids, ...agentData } = data;\n        const fields = Object.keys(agentData).filter((k)=>agentData[k] !== undefined);\n        if (fields.length === 0 && server_ids === undefined && tool_ids === undefined) {\n            return false; // Nothing to update\n        }\n        const transaction = _connection__WEBPACK_IMPORTED_MODULE_0__.db.transaction(()=>{\n            // 1. Update agent's own fields if any\n            if (fields.length > 0) {\n                const setClause = fields.map((field)=>`${field} = ?`).join(', ');\n                const values = fields.map((field)=>agentData[field]);\n                const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`UPDATE agents SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`);\n                stmt.run(...values, id);\n            }\n            // 2. Update server associations if provided\n            if (server_ids !== undefined) {\n                _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('DELETE FROM agent_mcp_servers WHERE agent_id = ?').run(id);\n                const linkServerStmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO agent_mcp_servers (agent_id, server_id) VALUES (?, ?)');\n                for (const server_id of server_ids){\n                    linkServerStmt.run(id, server_id);\n                }\n            }\n            // 3. Update tool associations if provided\n            if (tool_ids !== undefined) {\n                _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('DELETE FROM agent_tools WHERE agent_id = ?').run(id);\n                const linkToolStmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO agent_tools (agent_id, tool_id) VALUES (?, ?)');\n                for (const tool_id of tool_ids){\n                    linkToolStmt.run(id, tool_id);\n                }\n            }\n        });\n        try {\n            transaction();\n            return true;\n        } catch (error) {\n            console.error(`Failed to update agent ${id}:`, error);\n            return false;\n        }\n    },\n    delete (id) {\n        // Foreign key constraints with ON DELETE CASCADE will handle associated records\n        const result = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('DELETE FROM agents WHERE id = ?').run(id);\n        return result.changes > 0;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// 数据库连接配置\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'chat.db');\nconst lockFilePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), '.db-initialized');\n// 初始化数据库表的函数\nconst executeInitialization = (db)=>{\n    const initSQL = `\n    CREATE TABLE IF NOT EXISTS conversations (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      model TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS messages (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      conversation_id INTEGER NOT NULL,\n      role TEXT NOT NULL,\n      content TEXT NOT NULL,\n      model TEXT,\n      sequence_number INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      timestamp INTEGER, -- 毫秒级时间戳，用于精确排序\n      -- 工具调用相关字段\n      tool_name TEXT, -- 工具名称\n      tool_args TEXT, -- 工具参数 (JSON)\n      tool_result TEXT, -- 工具结果 (JSON)\n      tool_status TEXT CHECK (tool_status IN ('executing', 'completed', 'error')), -- 工具状态\n      tool_execution_time INTEGER, -- 工具执行时间(毫秒)\n      tool_error TEXT, -- 工具错误信息\n      -- Ollama生成统计信息\n      total_duration INTEGER,\n      load_duration INTEGER,\n      prompt_eval_count INTEGER,\n      prompt_eval_duration INTEGER,\n      eval_count INTEGER,\n      eval_duration INTEGER,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE\n    );\n\n    -- 智能体表\n    CREATE TABLE IF NOT EXISTS agents (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      description TEXT,\n      model_id INTEGER NOT NULL, -- 基础模型\n      system_prompt TEXT, -- 系统提示词\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (model_id) REFERENCES custom_models (id) ON DELETE CASCADE\n    );\n    \n    -- 智能体与MCP服务器关联表\n    CREATE TABLE IF NOT EXISTS agent_mcp_servers (\n      agent_id INTEGER NOT NULL,\n      server_id INTEGER NOT NULL,\n      PRIMARY KEY (agent_id, server_id),\n      FOREIGN KEY (agent_id) REFERENCES agents (id) ON DELETE CASCADE,\n      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE\n    );\n\n    -- 智能体与工具关联表\n    CREATE TABLE IF NOT EXISTS agent_tools (\n      agent_id INTEGER NOT NULL,\n      tool_id INTEGER NOT NULL,\n      PRIMARY KEY (agent_id, tool_id),\n      FOREIGN KEY (agent_id) REFERENCES agents (id) ON DELETE CASCADE,\n      FOREIGN KEY (tool_id) REFERENCES mcp_tools (id) ON DELETE CASCADE\n    );\n\n    -- 自定义模型配置表（包含完整的Ollama API字段）\n    CREATE TABLE IF NOT EXISTS custom_models (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      base_model TEXT NOT NULL UNIQUE, -- 完整的基础模型名称\n      display_name TEXT NOT NULL, -- 用户可自定义的显示名称\n      model_hash TEXT NOT NULL UNIQUE, -- 内部使用的哈希名称\n      family TEXT NOT NULL, -- 模型家族信息\n      description TEXT,\n      system_prompt TEXT,\n      parameters TEXT, -- JSON格式存储所有参数\n      template TEXT, -- 自定义模板\n      license TEXT,\n      tags TEXT, -- JSON数组格式存储标签\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      size BIGINT,\n      digest TEXT,\n      ollama_modified_at TEXT,\n      -- Ollama API详细信息字段\n      architecture TEXT, -- 模型架构（llama、gemma等）\n      parameter_count INTEGER, -- 参数数量\n      context_length INTEGER, -- 上下文长度\n      embedding_length INTEGER, -- 嵌入维度\n      quantization_level TEXT, -- 量化级别（Q8_0、Q4_0等）\n      format TEXT, -- 文件格式（gguf等）\n      capabilities TEXT -- 模型能力（JSON数组格式：completion、vision等）\n    );\n\n    -- MCP服务器统一配置表\n    CREATE TABLE IF NOT EXISTS mcp_servers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      display_name TEXT NOT NULL,\n      description TEXT,\n      type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),\n      status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),\n      enabled BOOLEAN NOT NULL DEFAULT 1,\n      \n      -- STDIO配置\n      command TEXT,\n      args TEXT, -- JSON数组格式\n      working_directory TEXT,\n      \n      -- SSE/HTTP配置\n      url TEXT,\n      base_url TEXT,\n      port INTEGER,\n      path TEXT DEFAULT '/',\n      protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),\n      \n      -- 通用配置\n      headers TEXT, -- JSON对象格式\n      auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),\n      auth_config TEXT, -- JSON格式\n      timeout_ms INTEGER DEFAULT 30000,\n      retry_attempts INTEGER DEFAULT 3,\n      retry_delay_ms INTEGER DEFAULT 1000,\n      \n      -- 扩展配置\n      extra_config TEXT, -- JSON格式，存储其他特殊配置\n      \n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      last_connected_at DATETIME,\n      error_message TEXT\n    );\n\n    -- MCP工具表\n    CREATE TABLE IF NOT EXISTS mcp_tools (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      server_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      input_schema TEXT, -- JSON格式存储工具的输入参数模式\n      is_available BOOLEAN DEFAULT 1,\n      enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）\n      last_used_at DATETIME,\n      usage_count INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,\n      UNIQUE(server_id, name)\n    );\n\n    -- MCP工具配置表\n    CREATE TABLE IF NOT EXISTS mcp_tool_configs (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      tool_id INTEGER,\n      server_name TEXT NOT NULL,\n      tool_name TEXT NOT NULL,\n      config TEXT NOT NULL, -- JSON格式存储配置\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      UNIQUE(server_name, tool_name)\n    );\n\n    -- 基础表索引\n    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);\n    \n    -- 自定义模型相关索引\n    CREATE INDEX IF NOT EXISTS idx_custom_models_base_model ON custom_models(base_model);\n    CREATE INDEX IF NOT EXISTS idx_custom_models_hash ON custom_models(model_hash);\n    CREATE INDEX IF NOT EXISTS idx_custom_models_family ON custom_models(family);\n    \n    -- MCP相关索引\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);\n    -- 工具调用相关索引已迁移到messages表\n    CREATE INDEX IF NOT EXISTS idx_messages_tool_name ON messages(tool_name);\n    CREATE INDEX IF NOT EXISTS idx_messages_tool_status ON messages(tool_status);\n    CREATE INDEX IF NOT EXISTS idx_messages_conv_tool ON messages(conversation_id, tool_name);\n\n    -- 智能体相关索引\n    CREATE INDEX IF NOT EXISTS idx_agents_name ON agents(name);\n    CREATE INDEX IF NOT EXISTS idx_agent_mcp_servers_agent_id ON agent_mcp_servers(agent_id);\n    CREATE INDEX IF NOT EXISTS idx_agent_tools_agent_id ON agent_tools(agent_id);\n  `;\n    try {\n        db.exec(initSQL);\n        console.log('✅ 数据库结构已成功初始化。');\n        // 创建锁文件表示初始化完成\n        fs__WEBPACK_IMPORTED_MODULE_2___default().closeSync(fs__WEBPACK_IMPORTED_MODULE_2___default().openSync(lockFilePath, 'w'));\n    } catch (error) {\n        console.error('❌ 数据库初始化失败:', error);\n        throw error;\n    }\n};\n// 获取数据库实例的单例函数\nconst getDatabaseInstance = ()=>{\n    // 进程内缓存依然有效，优先使用\n    if (global.__db_instance) {\n        return global.__db_instance;\n    }\n    const db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n    // 跨进程检查：如果锁文件不存在，则执行初始化\n    if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lockFilePath)) {\n        console.log('正在创建新的数据库连接并初始化结构...');\n        executeInitialization(db);\n    } else {\n    // console.log('数据库结构已存在，直接连接。');\n    }\n    global.__db_instance = db;\n    return db;\n};\n// 导出的是数据库实例本身，而不是整个模块\nconst db = getDatabaseInstance();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/conversations.ts":
/*!*******************************************!*\
  !*** ./src/lib/database/conversations.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* binding */ conversationOperations),\n/* harmony export */   conversationQueries: () => (/* binding */ conversationQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// 对话相关查询语句\nconst conversationQueries = {\n    // 创建新对话\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO conversations (title, model)\n    VALUES (?, ?)\n  `),\n    // 获取所有对话\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    ORDER BY updated_at DESC\n  `),\n    // 根据ID获取对话\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    WHERE id = ?\n  `),\n    // 更新对话标题\n    updateTitle: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET title = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新对话的最后更新时间\n    updateTimestamp: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除对话\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM conversations\n    WHERE id = ?\n  `)\n};\n// 对话数据库操作函数\nconst conversationOperations = {\n    // 创建新对话\n    create (data) {\n        const result = conversationQueries.create.run(data.title, data.model);\n        return result.lastInsertRowid;\n    },\n    // 获取所有对话\n    getAll () {\n        return conversationQueries.getAll.all();\n    },\n    // 根据ID获取对话\n    getById (id) {\n        return conversationQueries.getById.get(id);\n    },\n    // 更新对话标题\n    updateTitle (id, title) {\n        conversationQueries.updateTitle.run(title, id);\n    },\n    // 更新对话时间戳\n    updateTimestamp (id) {\n        conversationQueries.updateTimestamp.run(id);\n    },\n    // 删除对话\n    delete (id) {\n        conversationQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/conversations.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/custom-models.ts":
/*!*******************************************!*\
  !*** ./src/lib/database/custom-models.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomModelSchema: () => (/* binding */ CustomModelSchema),\n/* harmony export */   CustomModelService: () => (/* binding */ CustomModelService),\n/* harmony export */   ModelParametersSchema: () => (/* binding */ ModelParametersSchema)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ollama__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ollama */ \"(rsc)/./src/lib/ollama.ts\");\n\n\n\n\n// 模型参数验证模式\nconst ModelParametersSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    temperature: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).max(2).default(0.7),\n    top_p: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).max(1).default(0.9),\n    top_k: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).max(100).default(40),\n    repeat_penalty: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).default(1.1),\n    seed: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    num_predict: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().default(-1),\n    num_ctx: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).default(4096),\n    num_thread: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(1).optional(),\n    num_gpu: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(1).optional(),\n    use_mmap: zod__WEBPACK_IMPORTED_MODULE_1__.z.boolean().optional(),\n    num_batch: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(1).optional(),\n    num_keep: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).optional(),\n    stop: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional()\n});\n// 模型配置验证模式\nconst CustomModelSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int(),\n    base_model: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    display_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    model_hash: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    family: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    system_prompt: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    parameters: zod__WEBPACK_IMPORTED_MODULE_1__.z.record(zod__WEBPACK_IMPORTED_MODULE_1__.z.any()),\n    template: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    license: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    updated_at: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    size: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    digest: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    ollama_modified_at: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    // 新增Ollama API信息\n    architecture: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    parameter_count: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    context_length: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    embedding_length: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    quantization_level: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    format: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    capabilities: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional()\n});\nconst defaultParameters = {};\n// 检查名称冲突\nfunction checkNameConflict(baseModel, excludeId) {\n    const stmt = excludeId ? _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE base_model = ? AND id != ?') : _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE base_model = ?');\n    const result = excludeId ? stmt.get(baseModel, excludeId) : stmt.get(baseModel);\n    return result.count > 0;\n}\n// 检查哈希冲突\nfunction checkHashConflict(modelHash, excludeId) {\n    const stmt = excludeId ? _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE model_hash = ? AND id != ?') : _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE model_hash = ?');\n    const result = excludeId ? stmt.get(modelHash, excludeId) : stmt.get(modelHash);\n    return result.count > 0;\n}\nclass CustomModelService {\n    /**\r\n   * 创建自定义模型\r\n   */ static create(data) {\n        const validatedData = CustomModelSchema.omit({\n            id: true,\n            created_at: true,\n            updated_at: true\n        }).partial().merge(zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            base_model: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            display_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            family: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            model_hash: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            parameters: zod__WEBPACK_IMPORTED_MODULE_1__.z.record(zod__WEBPACK_IMPORTED_MODULE_1__.z.any())\n        })).parse(data);\n        const checkHashConflict = (hash)=>{\n            const row = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT id FROM custom_models WHERE model_hash = ?').get(hash);\n            return !!row;\n        };\n        let modelHash = validatedData.model_hash;\n        if (checkHashConflict(modelHash)) {\n            let attempts = 0;\n            let uniqueHashFound = false;\n            while(attempts < 10 && !uniqueHashFound){\n                const newHash = this.generateModelHash(`${validatedData.base_model}-${attempts}`);\n                if (!checkHashConflict(newHash)) {\n                    modelHash = newHash;\n                    uniqueHashFound = true;\n                }\n                attempts++;\n            }\n            if (!uniqueHashFound) {\n                throw new Error(\"Failed to generate a unique model hash after 10 attempts.\");\n            }\n        }\n        const parameters = JSON.stringify(validatedData.parameters);\n        const tags = JSON.stringify(validatedData.tags || []);\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO custom_models (base_model, display_name, model_hash, description, family, system_prompt, parameters, tags, template, license, size, digest, ollama_modified_at, architecture, parameter_count, context_length, embedding_length, quantization_level, format, capabilities) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');\n        const capabilities = JSON.stringify(validatedData.capabilities || []);\n        const result = stmt.run(validatedData.base_model, validatedData.display_name, modelHash, validatedData.description, validatedData.family, validatedData.system_prompt, parameters, tags, validatedData.template, validatedData.license, validatedData.size, validatedData.digest, validatedData.ollama_modified_at, validatedData.architecture, validatedData.parameter_count, validatedData.context_length, validatedData.embedding_length, validatedData.quantization_level, validatedData.format, capabilities);\n        const newModelId = result.lastInsertRowid;\n        const newModel = this.getById(Number(newModelId));\n        if (!newModel) {\n            throw new Error('Failed to create or retrieve the new model.');\n        }\n        return newModel;\n    }\n    /**\r\n   * 更新自定义模型\r\n   */ static update(id, data) {\n        const updateData = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            display_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, '显示名称不能为空'),\n            description: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n            tags: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional()\n        }).partial().parse(data);\n        const fields = Object.keys(updateData);\n        if (fields.length === 0) return false;\n        const updateFields = fields.map((field)=>`${field} = ?`);\n        const values = fields.map((field)=>{\n            const value = updateData[field];\n            return field === 'tags' ? JSON.stringify(value) : value;\n        });\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`UPDATE custom_models SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    }\n    /**\r\n   * 删除自定义模型\r\n   */ static delete(id) {\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('DELETE FROM custom_models WHERE id = ?');\n        const result = stmt.run(id);\n        return result.changes > 0;\n    }\n    /**\r\n   * 根据ID获取模型\r\n   */ static getById(id) {\n        const row = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT * FROM custom_models WHERE id = ?').get(id);\n        if (row) {\n            return this.mapRowToModel(row);\n        }\n        return null;\n    }\n    /**\r\n   * 获取所有模型\r\n   */ static getAll({ search, tags, sortBy, sortOrder }) {\n        let query = 'SELECT * FROM custom_models WHERE 1=1';\n        const params = [];\n        if (search) {\n            query += ' AND (base_model LIKE ? OR display_name LIKE ? OR description LIKE ?)';\n            params.push(`%${search}%`, `%${search}%`, `%${search}%`);\n        }\n        if (tags && tags.length > 0) {\n            query += ` AND (${tags.map(()=>\"json_each.value = ?\").join(' OR ')})`;\n            query = `SELECT t1.* FROM custom_models t1, json_each(t1.tags) WHERE t1.id IN (SELECT t1.id FROM custom_models t1, json_each(t1.tags) WHERE ${tags.map(()=>`json_each.value LIKE ?`).join(' OR ')})`;\n            params.push(...tags.map((t)=>`%${t}%`));\n        }\n        if (sortBy && sortOrder) {\n            const sortColumn = [\n                'base_model',\n                'created_at',\n                'updated_at',\n                'ollama_modified_at'\n            ].includes(sortBy) ? sortBy : 'base_model';\n            const order = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';\n            query += ` ORDER BY ${sortColumn} ${order}`;\n        }\n        const rows = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(query).all(params);\n        return rows.map(this.mapRowToModel);\n    }\n    static getTags() {\n        const rows = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(\"SELECT DISTINCT value FROM custom_models, json_each(tags)\").all();\n        return rows.map((row)=>row.value);\n    }\n    static async syncWithOllama(ollamaModels) {\n        const dbModels = this.getAll({});\n        const dbModelMap = new Map(dbModels.map((m)=>[\n                m.base_model,\n                m\n            ]));\n        const ollamaModelMap = new Map(ollamaModels.map((m)=>[\n                m.name,\n                m\n            ]));\n        const ollamaClient = new _ollama__WEBPACK_IMPORTED_MODULE_3__.OllamaClient();\n        // 1. 在事务之外，首先获取所有模型的详细信息\n        const detailedOllamaModels = await Promise.all(ollamaModels.map(async (model)=>{\n            try {\n                const showInfo = await ollamaClient.getModelDetails(model.name);\n                return {\n                    ...model,\n                    showInfo,\n                    success: true\n                };\n            } catch (error) {\n                console.error(`获取模型 ${model.name} 的详细信息失败:`, error);\n                return {\n                    ...model,\n                    showInfo: null,\n                    success: false\n                };\n            }\n        }));\n        const syncTransaction = _connection__WEBPACK_IMPORTED_MODULE_0__.db.transaction(()=>{\n            // 2. 在同步事务中处理数据库操作\n            for (const detailedModel of detailedOllamaModels){\n                if (!detailedModel.success || !detailedModel.showInfo) {\n                    continue; // 如果获取详情失败则跳过\n                }\n                const existingDbModel = dbModelMap.get(detailedModel.name);\n                const modelData = {\n                    base_model: detailedModel.name,\n                    family: detailedModel.showInfo.details.family || 'unknown',\n                    size: detailedModel.size,\n                    digest: detailedModel.digest,\n                    ollama_modified_at: detailedModel.modified_at,\n                    template: detailedModel.showInfo.template,\n                    system_prompt: detailedModel.showInfo.system,\n                    license: detailedModel.showInfo.license\n                };\n                // 解析和设置参数\n                if (detailedModel.showInfo.parameters) {\n                    try {\n                        const params = this.parseOllamaParameters(detailedModel.showInfo.parameters);\n                        modelData.parameters = params;\n                    } catch (e) {\n                        console.warn(`无法解析模型 '${detailedModel.name}' 的参数:`, e);\n                    }\n                }\n                // 解析模型架构和详细信息\n                if (detailedModel.showInfo.model_info) {\n                    const modelInfo = detailedModel.showInfo.model_info;\n                    modelData.architecture = modelInfo['general.architecture'];\n                    modelData.parameter_count = modelInfo['general.parameter_count'];\n                    // 动态查找上下文长度（支持不同架构）\n                    modelData.context_length = this.findModelInfoValue(modelInfo, 'context_length');\n                    // 动态查找嵌入长度\n                    modelData.embedding_length = this.findModelInfoValue(modelInfo, 'embedding_length');\n                }\n                // 设置格式和量化信息\n                if (detailedModel.showInfo.details) {\n                    modelData.format = detailedModel.showInfo.details.format;\n                    modelData.quantization_level = detailedModel.showInfo.details.quantization_level;\n                }\n                // 设置能力信息\n                if (detailedModel.showInfo.capabilities) {\n                    modelData.capabilities = detailedModel.showInfo.capabilities;\n                }\n                if (existingDbModel) {\n                    const updatedParameters = {\n                        ...existingDbModel.parameters,\n                        ...modelData.parameters || {}\n                    };\n                    const { display_name, description, tags, ...ollamaData } = modelData;\n                    this._updateOllamaData(existingDbModel.id, {\n                        ...ollamaData,\n                        parameters: updatedParameters\n                    });\n                } else {\n                    // Create new model\n                    const modelHash = this.generateModelHash(detailedModel.name);\n                    const displayName = detailedModel.name.split(':')[0];\n                    const parameters = modelData.parameters || defaultParameters;\n                    const fullModelData = {\n                        base_model: detailedModel.name,\n                        display_name: displayName,\n                        model_hash: modelHash,\n                        family: detailedModel.showInfo.details.family || 'unknown',\n                        description: '',\n                        system_prompt: modelData.system_prompt || '',\n                        parameters,\n                        tags: [],\n                        template: modelData.template,\n                        license: modelData.license,\n                        size: detailedModel.size,\n                        digest: detailedModel.digest,\n                        ollama_modified_at: detailedModel.modified_at,\n                        // 新增的Ollama API字段\n                        architecture: modelData.architecture,\n                        parameter_count: modelData.parameter_count,\n                        context_length: modelData.context_length,\n                        embedding_length: modelData.embedding_length,\n                        quantization_level: modelData.quantization_level,\n                        format: modelData.format,\n                        capabilities: modelData.capabilities || []\n                    };\n                    this.create(fullModelData);\n                }\n            }\n            // 3. 如果模型在Ollama中不再存在，则从我们的数据库中删除\n            for (const dbModel of dbModels){\n                if (!ollamaModelMap.has(dbModel.base_model)) {\n                    this.delete(dbModel.id);\n                }\n            }\n        });\n        syncTransaction();\n    }\n    /**\r\n   * 内部方法：只更新从Ollama同步的数据\r\n   */ static _updateOllamaData(id, data) {\n        const fields = Object.keys(data);\n        if (fields.length === 0) return false;\n        const updateFields = fields.map((field)=>`${field} = ?`);\n        const values = fields.map((field)=>{\n            const value = data[field];\n            if (field === 'parameters' || field === 'capabilities') return JSON.stringify(value);\n            return value;\n        });\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`UPDATE custom_models SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    }\n    static mapRowToModel(row) {\n        const model = {\n            id: row.id,\n            base_model: row.base_model,\n            display_name: row.display_name,\n            model_hash: row.model_hash,\n            description: row.description,\n            family: row.family,\n            system_prompt: row.system_prompt,\n            parameters: row.parameters ? JSON.parse(row.parameters) : {},\n            tags: row.tags ? JSON.parse(row.tags) : [],\n            created_at: row.created_at,\n            updated_at: row.updated_at,\n            size: row.size,\n            digest: row.digest,\n            ollama_modified_at: row.ollama_modified_at,\n            template: row.template,\n            license: row.license,\n            // 新增的Ollama API字段\n            architecture: row.architecture,\n            parameter_count: row.parameter_count,\n            context_length: row.context_length,\n            embedding_length: row.embedding_length,\n            quantization_level: row.quantization_level,\n            format: row.format,\n            capabilities: row.capabilities ? JSON.parse(row.capabilities) : []\n        };\n        return model;\n    }\n    static generateModelHash(name) {\n        return crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash('sha256').update(name).digest('hex').substring(0, 16);\n    }\n    /**\r\n   * 动态查找model_info中的字段值\r\n   * 支持不同架构的字段命名格式\r\n   */ static findModelInfoValue(modelInfo, fieldSuffix) {\n        // 查找所有包含指定后缀的字段\n        const matchingKeys = Object.keys(modelInfo).filter((key)=>key.endsWith('.' + fieldSuffix) || key.endsWith('_' + fieldSuffix));\n        if (matchingKeys.length === 0) return undefined;\n        // 优先返回第一个匹配的值\n        const firstKey = matchingKeys[0];\n        const value = modelInfo[firstKey];\n        return typeof value === 'number' ? value : undefined;\n    }\n    /**\r\n   * 解析Ollama parameters字符串为对象\r\n   */ static parseOllamaParameters(parametersStr) {\n        const params = {};\n        if (!parametersStr) return params;\n        const lines = parametersStr.split('\\n');\n        lines.forEach((line)=>{\n            const trimmedLine = line.trim();\n            if (!trimmedLine) return;\n            // 解析格式: \"param_name    value\"\n            const match = trimmedLine.match(/^(\\w+)\\s+(.+)$/);\n            if (!match) return;\n            const [, key, valueStr] = match;\n            // 解析值\n            let value = valueStr.trim();\n            // 去除引号\n            if (value.startsWith('\"') && value.endsWith('\"')) {\n                value = value.slice(1, -1);\n            }\n            // 特殊处理stop参数（可能有多个）\n            if (key === 'stop') {\n                if (!params.stop) {\n                    params.stop = [];\n                }\n                params.stop.push(value);\n            } else {\n                // 类型转换\n                if (value.toLowerCase() === 'true') {\n                    params[key] = true;\n                } else if (value.toLowerCase() === 'false') {\n                    params[key] = false;\n                } else if (!isNaN(Number(value)) && value.trim() !== '') {\n                    params[key] = Number(value);\n                } else {\n                    params[key] = value;\n                }\n            }\n        });\n        return params;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/custom-models.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/index.ts":
/*!***********************************!*\
  !*** ./src/lib/database/index.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentOperations: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_6__.agentOperations),\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* binding */ dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   mcpDbOperations: () => (/* binding */ mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/database/types.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n/* harmony import */ var _messages__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messages */ \"(rsc)/./src/lib/database/messages.ts\");\n/* harmony import */ var _mcp_servers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mcp-servers */ \"(rsc)/./src/lib/database/mcp-servers.ts\");\n/* harmony import */ var _mcp_tools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mcp-tools */ \"(rsc)/./src/lib/database/mcp-tools.ts\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/database/agents.ts\");\n// 导出数据库连接\n\n// 导出所有类型定义\n\n// 导出各模块的操作函数\n\n\n\n\n\n// MCP工具调用功能已迁移到messages表\n// 导出新的模块\n// export * from './agents'; // This was wrong\n\n\n\n\n\nconst dbOperations = {\n    // 对话相关操作\n    createConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.create,\n    getAllConversations: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getAll,\n    getConversationById: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getById,\n    updateConversationTitle: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTitle,\n    updateConversationTimestamp: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTimestamp,\n    deleteConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.delete,\n    // 消息相关操作\n    createMessage: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.create,\n    getMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getByConversationId,\n    deleteMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.deleteByConversationId,\n    getLastModelByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getLastModelByConversationId,\n    // MCP工具调用相关操作\n    getToolCallsByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getToolCallsByConversationId,\n    // Agent相关操作\n    createAgent: _agents__WEBPACK_IMPORTED_MODULE_6__.agentOperations.create,\n    getAllAgents: _agents__WEBPACK_IMPORTED_MODULE_6__.agentOperations.getAll,\n    getAgentById: _agents__WEBPACK_IMPORTED_MODULE_6__.agentOperations.getById,\n    updateAgent: _agents__WEBPACK_IMPORTED_MODULE_6__.agentOperations.update,\n    deleteAgent: _agents__WEBPACK_IMPORTED_MODULE_6__.agentOperations.delete\n};\n// 兼容原有的 mcpDbOperations 对象\nconst mcpDbOperations = {\n    // MCP服务器相关操作\n    createMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.create,\n    getAllMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getAll,\n    getMcpServerById: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getById,\n    getMcpServerByName: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getByName,\n    getEnabledMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getEnabled,\n    updateMcpServerStatus: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.updateStatus,\n    deleteMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.delete,\n    // MCP工具相关操作\n    createMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.create,\n    getMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerId,\n    getMcpToolById: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getById,\n    getMcpToolByServerIdAndName: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerIdAndName,\n    getAvailableMcpTools: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getAvailable,\n    updateMcpToolUsage: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateUsage,\n    updateMcpToolAvailability: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateAvailability,\n    updateMcpToolEnabled: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateEnabled,\n    deleteMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.deleteByServerId,\n    deleteMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.delete\n};\n// 默认导出数据库连接（保持兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsVUFBVTtBQUN3QjtBQUVsQyxXQUFXO0FBQ2E7QUFFeEIsYUFBYTtBQUNpRTtBQUNmO0FBQ087QUFDTjtBQUN2QztBQUN6Qix5QkFBeUI7QUFFekIsU0FBUztBQUNULDhDQUE4QztBQUVXO0FBQ1Y7QUFDSztBQUNKO0FBQ0w7QUFJcEMsTUFBTVUsZUFBZTtJQUMxQixTQUFTO0lBQ1RDLG9CQUFvQlQsa0VBQXNCQSxDQUFDVSxNQUFNO0lBQ2pEQyxxQkFBcUJYLGtFQUFzQkEsQ0FBQ1ksTUFBTTtJQUNsREMscUJBQXFCYixrRUFBc0JBLENBQUNjLE9BQU87SUFDbkRDLHlCQUF5QmYsa0VBQXNCQSxDQUFDZ0IsV0FBVztJQUMzREMsNkJBQTZCakIsa0VBQXNCQSxDQUFDa0IsZUFBZTtJQUNuRUMsb0JBQW9CbkIsa0VBQXNCQSxDQUFDb0IsTUFBTTtJQUVqRCxTQUFTO0lBQ1RDLGVBQWVuQix3REFBaUJBLENBQUNRLE1BQU07SUFDdkNZLDZCQUE2QnBCLHdEQUFpQkEsQ0FBQ3FCLG1CQUFtQjtJQUNsRUMsZ0NBQWdDdEIsd0RBQWlCQSxDQUFDdUIsc0JBQXNCO0lBQ3hFQyw4QkFBOEJ4Qix3REFBaUJBLENBQUN3Qiw0QkFBNEI7SUFFNUUsY0FBYztJQUNkQyw4QkFBOEJ6Qix3REFBaUJBLENBQUN5Qiw0QkFBNEI7SUFFNUUsWUFBWTtJQUNaQyxhQUFhckIsb0RBQWVBLENBQUNHLE1BQU07SUFDbkNtQixjQUFjdEIsb0RBQWVBLENBQUNLLE1BQU07SUFDcENrQixjQUFjdkIsb0RBQWVBLENBQUNPLE9BQU87SUFDckNpQixhQUFheEIsb0RBQWVBLENBQUN5QixNQUFNO0lBQ25DQyxhQUFhMUIsb0RBQWVBLENBQUNhLE1BQU07QUFDckMsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNYyxrQkFBa0I7SUFDN0IsYUFBYTtJQUNiQyxpQkFBaUIvQiw2REFBbUJBLENBQUNNLE1BQU07SUFDM0MwQixrQkFBa0JoQyw2REFBbUJBLENBQUNRLE1BQU07SUFDNUN5QixrQkFBa0JqQyw2REFBbUJBLENBQUNVLE9BQU87SUFDN0N3QixvQkFBb0JsQyw2REFBbUJBLENBQUNtQyxTQUFTO0lBQ2pEQyxzQkFBc0JwQyw2REFBbUJBLENBQUNxQyxVQUFVO0lBQ3BEQyx1QkFBdUJ0Qyw2REFBbUJBLENBQUN1QyxZQUFZO0lBQ3ZEQyxpQkFBaUJ4Qyw2REFBbUJBLENBQUNnQixNQUFNO0lBRTNDLFlBQVk7SUFDWnlCLGVBQWV2Qyx5REFBaUJBLENBQUNJLE1BQU07SUFDdkNvQyx1QkFBdUJ4Qyx5REFBaUJBLENBQUN5QyxhQUFhO0lBQ3REQyxnQkFBZ0IxQyx5REFBaUJBLENBQUNRLE9BQU87SUFDekNtQyw2QkFBNkIzQyx5REFBaUJBLENBQUM0QyxvQkFBb0I7SUFDbkVDLHNCQUFzQjdDLHlEQUFpQkEsQ0FBQzhDLFlBQVk7SUFDcERDLG9CQUFvQi9DLHlEQUFpQkEsQ0FBQ2dELFdBQVc7SUFDakRDLDJCQUEyQmpELHlEQUFpQkEsQ0FBQ2tELGtCQUFrQjtJQUMvREMsc0JBQXNCbkQseURBQWlCQSxDQUFDb0QsYUFBYTtJQUNyREMsMEJBQTBCckQseURBQWlCQSxDQUFDc0QsZ0JBQWdCO0lBQzVEQyxlQUFldkQseURBQWlCQSxDQUFDYyxNQUFNO0FBR3pDLEVBQUU7QUFFRixtQkFBbUI7QUFDMEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcbGliXFxkYXRhYmFzZVxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5a+85Ye65pWw5o2u5bqT6L+e5o6lXG5leHBvcnQgeyBkYiB9IGZyb20gJy4vY29ubmVjdGlvbic7XG5cbi8vIOWvvOWHuuaJgOacieexu+Wei+WumuS5iVxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG5cbi8vIOWvvOWHuuWQhOaooeWdl+eahOaTjeS9nOWHveaVsFxuZXhwb3J0IHsgY29udmVyc2F0aW9uUXVlcmllcywgY29udmVyc2F0aW9uT3BlcmF0aW9ucyB9IGZyb20gJy4vY29udmVyc2F0aW9ucyc7XG5leHBvcnQgeyBtZXNzYWdlUXVlcmllcywgbWVzc2FnZU9wZXJhdGlvbnMgfSBmcm9tICcuL21lc3NhZ2VzJztcbmV4cG9ydCB7IG1jcFNlcnZlclF1ZXJpZXMsIG1jcFNlcnZlck9wZXJhdGlvbnMgfSBmcm9tICcuL21jcC1zZXJ2ZXJzJztcbmV4cG9ydCB7IG1jcFRvb2xRdWVyaWVzLCBtY3BUb29sT3BlcmF0aW9ucyB9IGZyb20gJy4vbWNwLXRvb2xzJztcbmV4cG9ydCAqIGZyb20gJy4vYWdlbnRzJztcbi8vIE1DUOW3peWFt+iwg+eUqOWKn+iDveW3sui/geenu+WIsG1lc3NhZ2Vz6KGoXG5cbi8vIOWvvOWHuuaWsOeahOaooeWdl1xuLy8gZXhwb3J0ICogZnJvbSAnLi9hZ2VudHMnOyAvLyBUaGlzIHdhcyB3cm9uZ1xuXG5pbXBvcnQgeyBjb252ZXJzYXRpb25PcGVyYXRpb25zIH0gZnJvbSAnLi9jb252ZXJzYXRpb25zJztcbmltcG9ydCB7IG1lc3NhZ2VPcGVyYXRpb25zIH0gZnJvbSAnLi9tZXNzYWdlcyc7XG5pbXBvcnQgeyBtY3BTZXJ2ZXJPcGVyYXRpb25zIH0gZnJvbSAnLi9tY3Atc2VydmVycyc7XG5pbXBvcnQgeyBtY3BUb29sT3BlcmF0aW9ucyB9IGZyb20gJy4vbWNwLXRvb2xzJztcbmltcG9ydCB7IGFnZW50T3BlcmF0aW9ucyB9IGZyb20gJy4vYWdlbnRzJztcblxuXG5cbmV4cG9ydCBjb25zdCBkYk9wZXJhdGlvbnMgPSB7XG4gIC8vIOWvueivneebuOWFs+aTjeS9nFxuICBjcmVhdGVDb252ZXJzYXRpb246IGNvbnZlcnNhdGlvbk9wZXJhdGlvbnMuY3JlYXRlLFxuICBnZXRBbGxDb252ZXJzYXRpb25zOiBjb252ZXJzYXRpb25PcGVyYXRpb25zLmdldEFsbCxcbiAgZ2V0Q29udmVyc2F0aW9uQnlJZDogY29udmVyc2F0aW9uT3BlcmF0aW9ucy5nZXRCeUlkLFxuICB1cGRhdGVDb252ZXJzYXRpb25UaXRsZTogY29udmVyc2F0aW9uT3BlcmF0aW9ucy51cGRhdGVUaXRsZSxcbiAgdXBkYXRlQ29udmVyc2F0aW9uVGltZXN0YW1wOiBjb252ZXJzYXRpb25PcGVyYXRpb25zLnVwZGF0ZVRpbWVzdGFtcCxcbiAgZGVsZXRlQ29udmVyc2F0aW9uOiBjb252ZXJzYXRpb25PcGVyYXRpb25zLmRlbGV0ZSxcblxuICAvLyDmtojmga/nm7jlhbPmk43kvZxcbiAgY3JlYXRlTWVzc2FnZTogbWVzc2FnZU9wZXJhdGlvbnMuY3JlYXRlLFxuICBnZXRNZXNzYWdlc0J5Q29udmVyc2F0aW9uSWQ6IG1lc3NhZ2VPcGVyYXRpb25zLmdldEJ5Q29udmVyc2F0aW9uSWQsXG4gIGRlbGV0ZU1lc3NhZ2VzQnlDb252ZXJzYXRpb25JZDogbWVzc2FnZU9wZXJhdGlvbnMuZGVsZXRlQnlDb252ZXJzYXRpb25JZCxcbiAgZ2V0TGFzdE1vZGVsQnlDb252ZXJzYXRpb25JZDogbWVzc2FnZU9wZXJhdGlvbnMuZ2V0TGFzdE1vZGVsQnlDb252ZXJzYXRpb25JZCxcblxuICAvLyBNQ1Dlt6XlhbfosIPnlKjnm7jlhbPmk43kvZxcbiAgZ2V0VG9vbENhbGxzQnlDb252ZXJzYXRpb25JZDogbWVzc2FnZU9wZXJhdGlvbnMuZ2V0VG9vbENhbGxzQnlDb252ZXJzYXRpb25JZCxcblxuICAvLyBBZ2VudOebuOWFs+aTjeS9nFxuICBjcmVhdGVBZ2VudDogYWdlbnRPcGVyYXRpb25zLmNyZWF0ZSxcbiAgZ2V0QWxsQWdlbnRzOiBhZ2VudE9wZXJhdGlvbnMuZ2V0QWxsLFxuICBnZXRBZ2VudEJ5SWQ6IGFnZW50T3BlcmF0aW9ucy5nZXRCeUlkLFxuICB1cGRhdGVBZ2VudDogYWdlbnRPcGVyYXRpb25zLnVwZGF0ZSxcbiAgZGVsZXRlQWdlbnQ6IGFnZW50T3BlcmF0aW9ucy5kZWxldGUsXG59O1xuXG4vLyDlhbzlrrnljp/mnInnmoQgbWNwRGJPcGVyYXRpb25zIOWvueixoVxuZXhwb3J0IGNvbnN0IG1jcERiT3BlcmF0aW9ucyA9IHtcbiAgLy8gTUNQ5pyN5Yqh5Zmo55u45YWz5pON5L2cXG4gIGNyZWF0ZU1jcFNlcnZlcjogbWNwU2VydmVyT3BlcmF0aW9ucy5jcmVhdGUsXG4gIGdldEFsbE1jcFNlcnZlcnM6IG1jcFNlcnZlck9wZXJhdGlvbnMuZ2V0QWxsLFxuICBnZXRNY3BTZXJ2ZXJCeUlkOiBtY3BTZXJ2ZXJPcGVyYXRpb25zLmdldEJ5SWQsXG4gIGdldE1jcFNlcnZlckJ5TmFtZTogbWNwU2VydmVyT3BlcmF0aW9ucy5nZXRCeU5hbWUsXG4gIGdldEVuYWJsZWRNY3BTZXJ2ZXJzOiBtY3BTZXJ2ZXJPcGVyYXRpb25zLmdldEVuYWJsZWQsXG4gIHVwZGF0ZU1jcFNlcnZlclN0YXR1czogbWNwU2VydmVyT3BlcmF0aW9ucy51cGRhdGVTdGF0dXMsXG4gIGRlbGV0ZU1jcFNlcnZlcjogbWNwU2VydmVyT3BlcmF0aW9ucy5kZWxldGUsXG5cbiAgLy8gTUNQ5bel5YW355u45YWz5pON5L2cXG4gIGNyZWF0ZU1jcFRvb2w6IG1jcFRvb2xPcGVyYXRpb25zLmNyZWF0ZSxcbiAgZ2V0TWNwVG9vbHNCeVNlcnZlcklkOiBtY3BUb29sT3BlcmF0aW9ucy5nZXRCeVNlcnZlcklkLFxuICBnZXRNY3BUb29sQnlJZDogbWNwVG9vbE9wZXJhdGlvbnMuZ2V0QnlJZCxcbiAgZ2V0TWNwVG9vbEJ5U2VydmVySWRBbmROYW1lOiBtY3BUb29sT3BlcmF0aW9ucy5nZXRCeVNlcnZlcklkQW5kTmFtZSxcbiAgZ2V0QXZhaWxhYmxlTWNwVG9vbHM6IG1jcFRvb2xPcGVyYXRpb25zLmdldEF2YWlsYWJsZSxcbiAgdXBkYXRlTWNwVG9vbFVzYWdlOiBtY3BUb29sT3BlcmF0aW9ucy51cGRhdGVVc2FnZSxcbiAgdXBkYXRlTWNwVG9vbEF2YWlsYWJpbGl0eTogbWNwVG9vbE9wZXJhdGlvbnMudXBkYXRlQXZhaWxhYmlsaXR5LFxuICB1cGRhdGVNY3BUb29sRW5hYmxlZDogbWNwVG9vbE9wZXJhdGlvbnMudXBkYXRlRW5hYmxlZCxcbiAgZGVsZXRlTWNwVG9vbHNCeVNlcnZlcklkOiBtY3BUb29sT3BlcmF0aW9ucy5kZWxldGVCeVNlcnZlcklkLFxuICBkZWxldGVNY3BUb29sOiBtY3BUb29sT3BlcmF0aW9ucy5kZWxldGUsXG5cbiAgLy8gTUNQ5bel5YW36LCD55So55u45YWz5pON5L2c5bey6L+B56e75YiwbWVzc2FnZXPooahcbn07XG5cbi8vIOm7mOiupOWvvOWHuuaVsOaNruW6k+i/nuaOpe+8iOS/neaMgeWFvOWuueaAp++8iVxuZXhwb3J0IHsgZGIgYXMgZGVmYXVsdCB9IGZyb20gJy4vY29ubmVjdGlvbic7Il0sIm5hbWVzIjpbImRiIiwiY29udmVyc2F0aW9uUXVlcmllcyIsImNvbnZlcnNhdGlvbk9wZXJhdGlvbnMiLCJtZXNzYWdlUXVlcmllcyIsIm1lc3NhZ2VPcGVyYXRpb25zIiwibWNwU2VydmVyUXVlcmllcyIsIm1jcFNlcnZlck9wZXJhdGlvbnMiLCJtY3BUb29sUXVlcmllcyIsIm1jcFRvb2xPcGVyYXRpb25zIiwiYWdlbnRPcGVyYXRpb25zIiwiZGJPcGVyYXRpb25zIiwiY3JlYXRlQ29udmVyc2F0aW9uIiwiY3JlYXRlIiwiZ2V0QWxsQ29udmVyc2F0aW9ucyIsImdldEFsbCIsImdldENvbnZlcnNhdGlvbkJ5SWQiLCJnZXRCeUlkIiwidXBkYXRlQ29udmVyc2F0aW9uVGl0bGUiLCJ1cGRhdGVUaXRsZSIsInVwZGF0ZUNvbnZlcnNhdGlvblRpbWVzdGFtcCIsInVwZGF0ZVRpbWVzdGFtcCIsImRlbGV0ZUNvbnZlcnNhdGlvbiIsImRlbGV0ZSIsImNyZWF0ZU1lc3NhZ2UiLCJnZXRNZXNzYWdlc0J5Q29udmVyc2F0aW9uSWQiLCJnZXRCeUNvbnZlcnNhdGlvbklkIiwiZGVsZXRlTWVzc2FnZXNCeUNvbnZlcnNhdGlvbklkIiwiZGVsZXRlQnlDb252ZXJzYXRpb25JZCIsImdldExhc3RNb2RlbEJ5Q29udmVyc2F0aW9uSWQiLCJnZXRUb29sQ2FsbHNCeUNvbnZlcnNhdGlvbklkIiwiY3JlYXRlQWdlbnQiLCJnZXRBbGxBZ2VudHMiLCJnZXRBZ2VudEJ5SWQiLCJ1cGRhdGVBZ2VudCIsInVwZGF0ZSIsImRlbGV0ZUFnZW50IiwibWNwRGJPcGVyYXRpb25zIiwiY3JlYXRlTWNwU2VydmVyIiwiZ2V0QWxsTWNwU2VydmVycyIsImdldE1jcFNlcnZlckJ5SWQiLCJnZXRNY3BTZXJ2ZXJCeU5hbWUiLCJnZXRCeU5hbWUiLCJnZXRFbmFibGVkTWNwU2VydmVycyIsImdldEVuYWJsZWQiLCJ1cGRhdGVNY3BTZXJ2ZXJTdGF0dXMiLCJ1cGRhdGVTdGF0dXMiLCJkZWxldGVNY3BTZXJ2ZXIiLCJjcmVhdGVNY3BUb29sIiwiZ2V0TWNwVG9vbHNCeVNlcnZlcklkIiwiZ2V0QnlTZXJ2ZXJJZCIsImdldE1jcFRvb2xCeUlkIiwiZ2V0TWNwVG9vbEJ5U2VydmVySWRBbmROYW1lIiwiZ2V0QnlTZXJ2ZXJJZEFuZE5hbWUiLCJnZXRBdmFpbGFibGVNY3BUb29scyIsImdldEF2YWlsYWJsZSIsInVwZGF0ZU1jcFRvb2xVc2FnZSIsInVwZGF0ZVVzYWdlIiwidXBkYXRlTWNwVG9vbEF2YWlsYWJpbGl0eSIsInVwZGF0ZUF2YWlsYWJpbGl0eSIsInVwZGF0ZU1jcFRvb2xFbmFibGVkIiwidXBkYXRlRW5hYmxlZCIsImRlbGV0ZU1jcFRvb2xzQnlTZXJ2ZXJJZCIsImRlbGV0ZUJ5U2VydmVySWQiLCJkZWxldGVNY3BUb29sIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-servers.ts":
/*!*****************************************!*\
  !*** ./src/lib/database/mcp-servers.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpServerOperations: () => (/* binding */ mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* binding */ mcpServerQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP服务器相关查询语句\nconst mcpServerQueries = {\n    // 创建MCP服务器\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_servers (\n      name, display_name, description, type, enabled,\n      command, args, working_directory,\n      url, base_url, port, path, protocol,\n      headers, auth_type, auth_config, timeout_ms, retry_attempts, retry_delay_ms,\n      extra_config\n    )\n    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取所有MCP服务器\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    ORDER BY created_at DESC\n  `),\n    // 根据ID获取MCP服务器\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE id = ?\n  `),\n    // 根据名称获取MCP服务器\n    getByName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE name = ?\n  `),\n    // 获取启用的MCP服务器\n    getEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE enabled = 1\n    ORDER BY created_at DESC\n  `),\n    // 更新MCP服务器状态\n    updateStatus: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP,\n        last_connected_at = CASE WHEN ? = 'connected' THEN CURRENT_TIMESTAMP ELSE last_connected_at END\n    WHERE id = ?\n  `),\n    // 更新MCP服务器配置\n    update: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET display_name = ?, description = ?, type = ?, enabled = ?,\n        command = ?, args = ?, working_directory = ?,\n        url = ?, base_url = ?, port = ?, path = ?, protocol = ?,\n        headers = ?, auth_type = ?, auth_config = ?, timeout_ms = ?, retry_attempts = ?, retry_delay_ms = ?,\n        extra_config = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除MCP服务器\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_servers\n    WHERE id = ?\n  `)\n};\n// MCP服务器数据库操作函数\nconst mcpServerOperations = {\n    // 创建MCP服务器\n    create (data) {\n        const result = mcpServerQueries.create.run(data.name, data.display_name, data.description || null, data.type, Boolean(data.enabled ?? true) ? 1 : 0, data.command || null, data.args ? JSON.stringify(data.args) : null, data.working_directory || null, data.url || null, data.base_url || null, data.port ? Number(data.port) : null, data.path || null, data.protocol || null, data.headers ? JSON.stringify(data.headers) : null, data.auth_type || null, data.auth_config ? JSON.stringify(data.auth_config) : null, data.timeout_ms ? Number(data.timeout_ms) : null, data.retry_attempts ? Number(data.retry_attempts) : null, data.retry_delay_ms ? Number(data.retry_delay_ms) : null, data.extra_config ? JSON.stringify(data.extra_config) : null);\n        return result.lastInsertRowid;\n    },\n    // 获取所有MCP服务器\n    getAll () {\n        return mcpServerQueries.getAll.all();\n    },\n    // 根据ID获取MCP服务器\n    getById (id) {\n        return mcpServerQueries.getById.get(id);\n    },\n    // 根据名称获取MCP服务器\n    getByName (name) {\n        return mcpServerQueries.getByName.get(name);\n    },\n    // 获取启用的MCP服务器\n    getEnabled () {\n        return mcpServerQueries.getEnabled.all();\n    },\n    // 更新MCP服务器状态\n    updateStatus (id, status, errorMessage) {\n        mcpServerQueries.updateStatus.run(status, errorMessage || null, status, id);\n    },\n    // 删除MCP服务器\n    delete (id) {\n        mcpServerQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-servers.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-tools.ts":
/*!***************************************!*\
  !*** ./src/lib/database/mcp-tools.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpToolOperations: () => (/* binding */ mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* binding */ mcpToolQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP工具相关查询语句\nconst mcpToolQueries = {\n    // 创建MCP工具\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_tools (server_id, name, description, input_schema, is_available, enabled)\n    VALUES (?, ?, ?, ?, ?, ?)\n  `),\n    // 获取服务器的所有工具\n    getByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ?\n    ORDER BY name ASC\n  `),\n    // 根据ID获取工具\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE id = ?\n  `),\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ? AND name = ?\n  `),\n    // 获取可用的工具\n    getAvailable: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT t.*, s.name as server_name, s.display_name as server_display_name, s.status as server_status\n    FROM mcp_tools t\n    JOIN mcp_servers s ON t.server_id = s.id\n    WHERE t.is_available = 1 AND t.enabled = 1 AND s.enabled = 1\n    ORDER BY t.name ASC\n  `),\n    // 更新工具使用统计\n    updateUsage: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具可用性\n    updateAvailability: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET is_available = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具启用状态\n    updateEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET enabled = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除服务器的所有工具\n    deleteByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE server_id = ?\n  `),\n    // 删除工具\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE id = ?\n  `)\n};\n// MCP工具数据库操作函数\nconst mcpToolOperations = {\n    // 创建MCP工具\n    create (data) {\n        const result = mcpToolQueries.create.run(data.server_id, data.name, data.description || null, data.input_schema ? JSON.stringify(data.input_schema) : null, Boolean(data.is_available ?? true) ? 1 : 0, Boolean(data.enabled ?? true) ? 1 : 0 // 确保布尔值转换为数字\n        );\n        return result.lastInsertRowid;\n    },\n    // 获取服务器的所有工具\n    getByServerId (serverId) {\n        return mcpToolQueries.getByServerId.all(serverId);\n    },\n    // 根据ID获取工具\n    getById (id) {\n        return mcpToolQueries.getById.get(id);\n    },\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName (serverId, name) {\n        return mcpToolQueries.getByServerIdAndName.get(serverId, name);\n    },\n    // 获取可用的工具\n    getAvailable () {\n        return mcpToolQueries.getAvailable.all();\n    },\n    // 更新工具使用统计\n    updateUsage (toolId) {\n        mcpToolQueries.updateUsage.run(toolId);\n    },\n    // 更新工具可用性\n    updateAvailability (id, isAvailable) {\n        mcpToolQueries.updateAvailability.run(isAvailable ? 1 : 0, id);\n    },\n    // 更新工具启用状态\n    updateEnabled (id, enabled) {\n        mcpToolQueries.updateEnabled.run(enabled ? 1 : 0, id);\n    },\n    // 删除服务器的所有工具\n    deleteByServerId (serverId) {\n        mcpToolQueries.deleteByServerId.run(serverId);\n    },\n    // 删除工具\n    delete (id) {\n        mcpToolQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/messages.ts":
/*!**************************************!*\
  !*** ./src/lib/database/messages.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messageOperations: () => (/* binding */ messageOperations),\n/* harmony export */   messageQueries: () => (/* binding */ messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n\n\n// 消息相关查询语句\nconst messageQueries = {\n    // 创建新消息\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO messages (\n      conversation_id, role, content, model, sequence_number, timestamp,\n      total_duration, load_duration, prompt_eval_count, prompt_eval_duration,\n      eval_count, eval_duration\n    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取对话的所有消息\n    getByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM messages\n    WHERE conversation_id = ?\n    ORDER BY timestamp ASC, id ASC\n  `),\n    // 删除对话的所有消息\n    deleteByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM messages WHERE conversation_id = ?\n  `),\n    // 获取对话的工具调用记录（从messages表）\n    getToolCallsByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT \n      id,\n      conversation_id,\n      tool_name,\n      tool_args,\n      tool_result,\n      tool_status,\n      tool_execution_time,\n      tool_error,\n      created_at,\n      timestamp\n    FROM messages \n    WHERE conversation_id = ? AND tool_name IS NOT NULL\n    ORDER BY id ASC\n  `),\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT model FROM messages\n    WHERE conversation_id = ? AND model IS NOT NULL\n    ORDER BY created_at DESC\n    LIMIT 1\n  `),\n    // 获取对话中下一个可用的序列号\n    getNextSequenceNumber: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT COALESCE(MAX(sequence_number), 0) + 1 as next_sequence\n    FROM messages\n    WHERE conversation_id = ?\n  `)\n};\n// 消息数据库操作函数\nconst messageOperations = {\n    // 创建新消息\n    create (data) {\n        // 简化：不再使用sequence_number，只依赖自增ID\n        // 生成时间戳（毫秒级）\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, data.role, data.content, data.model || null, 0, timestamp, data.total_duration || null, data.load_duration || null, data.prompt_eval_count || null, data.prompt_eval_duration || null, data.eval_count || null, data.eval_duration || null);\n        // 更新对话的时间戳\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    },\n    // 获取对话的所有消息\n    getByConversationId (conversationId) {\n        return messageQueries.getByConversationId.all(conversationId);\n    },\n    // 删除对话的所有消息\n    deleteByConversationId (conversationId) {\n        messageQueries.deleteByConversationId.run(conversationId);\n    },\n    // 获取对话的工具调用记录\n    getToolCallsByConversationId (conversationId) {\n        return messageQueries.getToolCallsByConversationId.all(conversationId);\n    },\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId (conversationId) {\n        const result = messageQueries.getLastModelByConversationId.get(conversationId);\n        return result?.model || null;\n    },\n    // 获取对话中下一个可用的序列号（已废弃，保留兼容性）\n    getNextSequenceNumber (conversationId) {\n        const result = messageQueries.getNextSequenceNumber.get(conversationId);\n        return result?.next_sequence || 1;\n    },\n    // 创建工具调用消息\n    createToolCall (data) {\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, 'tool_call', `工具调用: ${data.tool_name}`, null, 0, timestamp, null, null, null, null, null, null // 统计信息字段\n        );\n        // 更新工具相关字段\n        if (result.lastInsertRowid) {\n            const updateToolFields = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n        UPDATE messages SET\n          tool_name = ?,\n          tool_args = ?,\n          tool_result = ?,\n          tool_status = ?,\n          tool_execution_time = ?,\n          tool_error = ?\n        WHERE id = ?\n      `);\n            updateToolFields.run(data.tool_name, JSON.stringify(data.tool_args), data.tool_result ? JSON.stringify(data.tool_result) : null, data.tool_status, data.tool_execution_time || null, data.tool_error || null, result.lastInsertRowid);\n        }\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/messages.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/types.ts":
/*!***********************************!*\
  !*** ./src/lib/database/types.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// 对话相关接口\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaClient: () => (/* binding */ OllamaClient),\n/* harmony export */   ollamaClient: () => (/* binding */ ollamaClient)\n/* harmony export */ });\n// Ollama API 客户端\nconst OLLAMA_BASE_URL = 'http://localhost:11434';\nclass OllamaClient {\n    constructor(baseUrl = OLLAMA_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    /**\n   * 获取本地可用的模型列表\n   */ async getModels() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const data = await response.json();\n            return data.models || [];\n        } catch (error) {\n            console.error('获取模型列表失败:', error);\n            throw new Error('无法连接到Ollama服务，请确保Ollama正在运行');\n        }\n    }\n    /**\n   * 获取指定模型的详细信息\n   */ async getModelDetails(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/show`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: modelName\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`获取模型 '${modelName}' 详细信息失败:`, response.status, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const details = await response.json();\n            // 改进的系统提示词提取逻辑\n            let systemPrompt = '';\n            if (details.modelfile) {\n                // 尝试多种 SYSTEM 指令格式（不区分大小写）\n                const patterns = [\n                    // 三引号格式：SYSTEM \"\"\"content\"\"\"\n                    /(?:SYSTEM|system)\\s+\"\"\"([\\s\\S]*?)\"\"\"/i,\n                    // 双引号格式：SYSTEM \"content\"\n                    /(?:SYSTEM|system)\\s+\"([^\"]*?)\"/i,\n                    // 单引号格式：SYSTEM 'content'\n                    /(?:SYSTEM|system)\\s+'([^']*?)'/i,\n                    // 无引号格式（到行尾）：SYSTEM content\n                    /(?:SYSTEM|system)\\s+([^\\n\\r]*)/i\n                ];\n                for (const pattern of patterns){\n                    const match = details.modelfile.match(pattern);\n                    if (match && match[1].trim()) {\n                        systemPrompt = match[1].trim();\n                        break;\n                    }\n                }\n            }\n            details.system = systemPrompt;\n            return details;\n        } catch (error) {\n            console.error(`请求模型 '${modelName}' 详细信息时出错:`, error);\n            throw new Error(`无法获取模型 '${modelName}' 的详细信息`);\n        }\n    }\n    /**\n   * 发送聊天请求（非流式）\n   */ async chat(request) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('聊天请求失败:', error);\n            throw new Error('聊天请求失败，请检查网络连接和Ollama服务状态');\n        }\n    }\n    /**\n   * 发送流式聊天请求\n   */ async *chatStream(request) {\n        try {\n            // console.log('Ollama chatStream 请求:', JSON.stringify(request, null, 2));\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: true\n                })\n            });\n            // console.log('Ollama 响应状态:', response.status, response.statusText);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            if (!response.body) {\n                throw new Error('响应体为空');\n            }\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let buffer = '';\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    buffer += decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = buffer.split('\\n');\n                    // 保留最后一行（可能不完整）\n                    buffer = lines.pop() || '';\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine) {\n                            try {\n                                const data = JSON.parse(trimmedLine);\n                                yield data;\n                                // 如果收到完成标志，结束生成\n                                if (data.done) {\n                                    return;\n                                }\n                            } catch (parseError) {\n                                console.warn('解析JSON失败:', parseError, '原始数据:', trimmedLine);\n                            }\n                        }\n                    }\n                }\n                // 处理缓冲区中剩余的数据\n                if (buffer.trim()) {\n                    try {\n                        const data = JSON.parse(buffer.trim());\n                        yield data;\n                    } catch (parseError) {\n                        console.warn('解析最后的JSON失败:', parseError);\n                    }\n                }\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.error('流式聊天请求失败:', error);\n            if (error instanceof Error) {\n                throw error; // 保持原始错误信息\n            } else {\n                throw new Error('流式聊天请求失败，请检查网络连接和Ollama服务状态');\n            }\n        }\n    }\n    /**\n   * 检查Ollama服务是否可用\n   */ async isAvailable() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                signal: AbortSignal.timeout(5000)\n            });\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    }\n    /**\n   * 检查指定模型是否已加载到内存中\n   */ async isModelLoaded(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/ps`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                console.error('获取模型状态失败:', response.status, response.statusText);\n                return false;\n            }\n            const data = await response.json();\n            const loadedModels = data.models || [];\n            // 检查指定模型是否在已加载的模型列表中\n            return loadedModels.some((model)=>model.name === modelName);\n        } catch (error) {\n            console.error('检查模型加载状态失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 格式化模型大小\n   */ static formatModelSize(bytes) {\n        const units = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        let size = bytes;\n        let unitIndex = 0;\n        while(size >= 1024 && unitIndex < units.length - 1){\n            size /= 1024;\n            unitIndex++;\n        }\n        return `${size.toFixed(1)} ${units[unitIndex]}`;\n    }\n    /**\n   * 格式化模型名称（移除标签）\n   */ static formatModelName(name) {\n        return name.split(':')[0];\n    }\n}\n// 默认客户端实例\nconst ollamaClient = new OllamaClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ollama.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();