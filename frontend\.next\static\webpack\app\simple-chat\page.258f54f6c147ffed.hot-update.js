"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/hooks/chat/useChatMessages.ts":
/*!***********************************************************!*\
  !*** ./src/app/simple-chat/hooks/chat/useChatMessages.ts ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChatMessages: () => (/* binding */ useChatMessages)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useChatMessages auto */ \n// 本地存储键名\nconst SELECTED_MODEL_KEY = 'chat_selected_model';\nconst CONVERSATION_MODEL_KEY_PREFIX = 'chat_conversation_model_';\nfunction useChatMessages() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [expandedThinkingMessages, setExpandedThinkingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Set());\n    // Agent-related state\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [systemPrompt, setSystemPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // 工具相关状态\n    const [enableTools, setEnableTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedTools, setSelectedTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [toolCalls, setToolCalls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [currentAssistantMessageId, setCurrentAssistantMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // 添加AbortController来控制请求中断\n    const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // --- New function to handle agent selection ---\n    const selectAgent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[selectAgent]\": async (agentId)=>{\n            if (agentId === null) {\n                setSelectedAgent(null);\n                setSystemPrompt(null);\n                // Optionally reset other settings or leave them as they were\n                return;\n            }\n            try {\n                const response = await fetch(\"/api/agents/\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to fetch agent details');\n                }\n                const agent = await response.json();\n                setSelectedAgent(agent);\n                // Override chat settings with agent's configuration\n                setSelectedModel(agent.model.base_model);\n                setEnableTools(agent.tools.length > 0);\n                setSelectedTools(agent.tools.map({\n                    \"useChatMessages.useCallback[selectAgent]\": (t)=>t.name\n                }[\"useChatMessages.useCallback[selectAgent]\"]));\n                if (agent.system_prompt) {\n                    setSystemPrompt(agent.system_prompt);\n                } else {\n                    setSystemPrompt(null);\n                }\n                console.log('Agent \"'.concat(agent.name, '\" selected. Model set to \"').concat(agent.model.base_model, '\".'));\n            } catch (error) {\n                console.error('Error selecting agent:', error);\n                // Handle error, maybe show a notification to the user\n                setSelectedAgent(null); // Reset on error\n            }\n        }\n    }[\"useChatMessages.useCallback[selectAgent]\"], []);\n    // 从本地存储加载已保存的模型选择\n    const loadSavedModel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[loadSavedModel]\": ()=>{\n            try {\n                const savedModel = localStorage.getItem(SELECTED_MODEL_KEY);\n                return savedModel;\n            } catch (error) {\n                console.warn('无法从localStorage读取保存的模型:', error);\n                return null;\n            }\n        }\n    }[\"useChatMessages.useCallback[loadSavedModel]\"], []);\n    // 从本地存储加载特定对话的模型选择\n    const loadConversationModel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[loadConversationModel]\": (conversationId)=>{\n            try {\n                const key = \"\".concat(CONVERSATION_MODEL_KEY_PREFIX).concat(conversationId);\n                const savedModel = localStorage.getItem(key);\n                return savedModel;\n            } catch (error) {\n                console.warn('无法从localStorage读取对话模型:', error);\n                return null;\n            }\n        }\n    }[\"useChatMessages.useCallback[loadConversationModel]\"], []);\n    // 保存模型选择到本地存储 - 修复：移除useCallback依赖，使其稳定\n    const saveModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[saveModelSelection]\": (modelName, conversationId)=>{\n            try {\n                // 保存全局模型选择\n                localStorage.setItem(SELECTED_MODEL_KEY, modelName);\n                // 如果有对话ID，也保存对话特定的模型选择\n                if (conversationId) {\n                    const key = \"\".concat(CONVERSATION_MODEL_KEY_PREFIX).concat(conversationId);\n                    localStorage.setItem(key, modelName);\n                    console.log(\"保存对话 \".concat(conversationId, \" 的模型选择: \").concat(modelName));\n                }\n            } catch (error) {\n                console.warn('无法保存模型选择到localStorage:', error);\n            }\n        }\n    }[\"useChatMessages.useCallback[saveModelSelection]\"], []); // 空依赖数组，函数体内不依赖任何外部变量\n    // 包装setSelectedModel以添加持久化 - 修复：现在依赖稳定了\n    const setSelectedModelWithPersistence = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[setSelectedModelWithPersistence]\": (modelName, conversationId)=>{\n            setSelectedModel(modelName);\n            saveModelSelection(modelName, conversationId);\n        }\n    }[\"useChatMessages.useCallback[setSelectedModelWithPersistence]\"], [\n        saveModelSelection\n    ]); // saveModelSelection现在是稳定的\n    // 智能模型选择函数 - 修复：所有依赖现在都是稳定的\n    const selectBestModel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[selectBestModel]\": (availableModels, conversationId, lastUsedModel, conversationModel)=>{\n            var // 5. 第一个可用模型\n            _availableModels_;\n            if (!availableModels.length) return;\n            // 按优先级尝试不同的模型选择策略\n            const strategies = [\n                // 1. 对话中最后使用的模型\n                lastUsedModel,\n                // 2. 对话特定保存的模型\n                conversationId ? loadConversationModel(conversationId) : null,\n                // 3. 对话创建时的模型\n                conversationModel,\n                // 4. 全局保存的模型\n                loadSavedModel(),\n                (_availableModels_ = availableModels[0]) === null || _availableModels_ === void 0 ? void 0 : _availableModels_.name\n            ];\n            for (const candidateModel of strategies){\n                if (candidateModel && availableModels.some({\n                    \"useChatMessages.useCallback[selectBestModel]\": (model)=>model.name === candidateModel\n                }[\"useChatMessages.useCallback[selectBestModel]\"])) {\n                    console.log(\"选择模型: \".concat(candidateModel, \" (策略: \").concat(strategies.indexOf(candidateModel) + 1, \")\"));\n                    setSelectedModel(candidateModel);\n                    // 保存选择\n                    saveModelSelection(candidateModel, conversationId);\n                    return candidateModel;\n                }\n            }\n        }\n    }[\"useChatMessages.useCallback[selectBestModel]\"], [\n        loadSavedModel,\n        loadConversationModel,\n        saveModelSelection\n    ]); // 现在所有依赖都是稳定的\n    // 获取模型列表 - 优化：直接使用custom-models API数据，无需转换\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useChatMessages.useEffect\": ()=>{\n            const fetchModels = {\n                \"useChatMessages.useEffect.fetchModels\": async ()=>{\n                    try {\n                        console.log('🔄 开始加载模型列表');\n                        const response = await fetch('/api/custom-models');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.models) {\n                                // 直接使用数据库数据，保持完整的模型信息\n                                setModels(data.models);\n                                console.log(\"✅ 成功加载 \".concat(data.models.length, \" 个模型\"));\n                                if (data.models.length > 0 && !selectedModel) {\n                                    // 智能选择第一个可用模型\n                                    selectBestModel(data.models);\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        console.error('❌ 获取模型失败:', err);\n                    }\n                }\n            }[\"useChatMessages.useEffect.fetchModels\"];\n            fetchModels();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"useChatMessages.useEffect\"], []);\n    // 思考面板切换\n    const toggleThinkingExpand = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[toggleThinkingExpand]\": (messageId)=>{\n            setExpandedThinkingMessages({\n                \"useChatMessages.useCallback[toggleThinkingExpand]\": (prev)=>{\n                    const newExpanded = new Set(prev);\n                    if (newExpanded.has(messageId)) {\n                        newExpanded.delete(messageId);\n                    } else {\n                        newExpanded.add(messageId);\n                    }\n                    return newExpanded;\n                }\n            }[\"useChatMessages.useCallback[toggleThinkingExpand]\"]);\n        }\n    }[\"useChatMessages.useCallback[toggleThinkingExpand]\"], []);\n    // 停止生成\n    const stopGeneration = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[stopGeneration]\": ()=>{\n            // 中断正在进行的请求\n            if (abortController) {\n                console.log('🛑 中断正在进行的请求');\n                abortController.abort();\n                setAbortController(null);\n            }\n            // 重置流式状态\n            setIsStreaming(false);\n        }\n    }[\"useChatMessages.useCallback[stopGeneration]\"], [\n        abortController\n    ]);\n    return {\n        // 消息状态\n        messages,\n        setMessages,\n        inputMessage,\n        setInputMessage,\n        isStreaming,\n        setIsStreaming,\n        selectedModel,\n        setSelectedModel: setSelectedModelWithPersistence,\n        models,\n        expandedThinkingMessages,\n        setExpandedThinkingMessages,\n        // Agent state\n        selectedAgent,\n        systemPrompt,\n        // 工具状态\n        enableTools,\n        setEnableTools,\n        selectedTools,\n        setSelectedTools,\n        activeTool,\n        setActiveTool,\n        toolCalls,\n        setToolCalls,\n        currentAssistantMessageId,\n        setCurrentAssistantMessageId,\n        // 方法\n        toggleThinkingExpand,\n        stopGeneration,\n        selectAgent,\n        // AbortController\n        abortController,\n        setAbortController,\n        // 新增的智能模型选择方法\n        selectBestModel\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/hooks/chat/useChatMessages.ts\n"));

/***/ })

});