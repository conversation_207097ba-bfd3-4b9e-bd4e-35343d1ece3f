# 弹窗设计系统规范

## 概述

本文档定义了应用中所有弹窗组件的统一设计规范，确保一致的用户体验和视觉效果。

## 字体系统

### 字体大小

- **超大标题**: `text-3xl` (30px) - 页面主标题
- **大标题**: `text-2xl` (24px) - 弹窗标题
- **中标题**: `text-xl` (20px) - 页面标题
- **小标题**: `text-lg` (18px) - 区域标题
- **正文**: `text-sm` (14px) - 按钮文字、输入框文字
- **辅助文字**: `text-xs` (12px) - 提示文字、标签
- **说明文字**: `text-xs` (12px) - 错误信息、帮助文字

### 字重

- **粗体**: `font-bold` (700) - 页面主标题
- **半粗体**: `font-semibold` (600) - 弹窗标题、区域标题
- **中等**: `font-medium` (500) - 标签文字、重要提示
- **正常**: `font-normal` (400) - 正文、按钮文字

### 行高

- **紧凑**: `leading-tight` - 标题
- **正常**: `leading-normal` - 正文
- **松散**: `leading-relaxed` - 长文本

## 色彩系统

### 主要颜色

- **主色**: `theme-primary`
- **前景色**: `theme-foreground`
- **次要前景色**: `theme-foreground-muted`
- **背景色**: `theme-background`
- **次要背景色**: `theme-background-secondary`
- **边框色**: `theme-border`
- **错误色**: `theme-error`

### 边框使用原则

**简洁设计原则**: 最小化分隔线使用，仅在必要的结构分离处使用边框。

**保留的边框：**
- **弹窗头部分隔**: `border-b border-theme-border` - 分离标题和内容
- **板块标题分隔**: `border-t border-theme-border` - 分离标题和内容
- **输入框边框**: `border border-theme-border` - 功能性边框
- **卡片边框**: `border border-theme-border` - 内容区域边框

**移除的边框：**
- ❌ 信息行之间的分隔线 - 改用空白间距
- ❌ 参数列表中的分隔线 - 改用空白间距
- ❌ 过度的视觉分割 - 保持简洁

### 交互状态

- **悬停**: 颜色透明度降低或使用 `hover:` 变体
- **焦点**: `focus:border-theme-primary`
- **激活**: 轻微缩放动画

## 核心组件

### ModalWrapper

主要的弹窗容器组件，提供统一的布局、动画和交互体验。

#### 动画规范

- **背景遮罩**: `bg-black/40 backdrop-blur-sm`
- **入场动画**: `scale: 0.9 → 1.0`, `opacity: 0 → 1`, `y: 20 → 0`
- **退场动画**: `scale: 1.0 → 0.95`, `opacity: 1 → 0`, `y: 0 → -10`
- **动画时长**: 0.4s，使用缓动函数 `[0.16, 1, 0.3, 1]`

#### 布局规范

- **容器圆角**: `rounded-3xl` (24px)
- **最大高度**: `max-h-[90vh]`
- **内边距**: 头部和内容区域 `p-8`
- **背景色**: `bg-theme-background`
- **阴影**: `shadow-2xl`

#### 头部结构

```jsx
<div className="p-8 pb-6 border-b border-theme-border">
  <div className="flex items-start justify-between">
    {/* 图标 + 标题区域 */}
    <div className="flex items-center gap-4">
      {icon}
      <div>
        <h2 className="text-2xl font-semibold text-theme-foreground">{title}</h2>
        {subtitle && <p className="text-sm text-theme-foreground-muted">{subtitle}</p>}
      </div>
    </div>
    {/* 关闭按钮 */}
    <button className="w-10 h-10 rounded-full bg-theme-background-secondary hover:bg-theme-card-hover">
      <X className="w-5 h-5" />
    </button>
  </div>
</div>
```

### FormComponents

统一的表单元素组件集合。

#### 输入框 (TextInput)

- **内边距**: `px-4 py-3`
- **圆角**: `rounded-xl` (12px)
- **边框**: `border border-theme-border`
- **背景**: `bg-theme-background-secondary`
- **字体**: `text-sm font-normal`
- **焦点状态**: `focus:border-theme-primary`
- **错误状态**: `border-theme-error bg-theme-error/5`
- **过渡动画**: `transition-colors`

#### 文本域 (TextArea)

- 与输入框相同的样式规范
- **禁用调整大小**: `resize-none`

#### 选择框 (Select)

- 与输入框相同的样式规范

#### 按钮 (Button)

- **圆角**: `rounded-xl` (12px)
- **字体**: `text-sm font-medium`
- **内边距**: 
  - `sm`: `px-3 py-2`
  - `md`: `px-6 py-3`
  - `lg`: `px-8 py-4`
- **变体样式**:
  - `primary`: `bg-theme-primary text-white hover:bg-theme-primary/90`
  - `secondary`: `bg-theme-background-secondary border border-theme-border`
  - `outline`: `border border-theme-primary text-theme-primary`
  - `ghost`: `text-theme-foreground-muted hover:bg-theme-background-secondary`
- **动画**: `whileHover: scale: 1.02`, `whileTap: scale: 0.98`

#### 表单标签 (FormInput Label)

- **字体**: `text-sm font-medium text-theme-foreground`
- **必填标记**: `text-theme-error` 红色星号

#### 表单区域 (FormSection)

- **标题**: `text-lg font-semibold text-theme-foreground`
- **动画**: 渐入动画，带延迟
- **间距**: `space-y-4`

#### 提示文字

- **帮助文字**: `text-xs text-theme-foreground-muted`
- **错误文字**: `text-sm text-theme-error`

#### 信息展示区域

**Section 组件结构：**
```jsx
<div className="bg-theme-background-secondary rounded-2xl p-6">
  <h3 className="text-lg font-semibold text-theme-foreground mb-4 flex items-center gap-3">
    <Icon className="w-5 h-5 text-theme-primary" />
    {title}
  </h3>
  <div className="border-t border-theme-border pt-4">
    {children}
  </div>
</div>
```

**InfoRow 组件（简化版）：**
- **内边距**: `py-3` - 使用空白间距代替分隔线
- **标签**: `text-sm font-medium text-theme-foreground-muted mb-2`
- **值**: `text-theme-foreground`
- **间距**: `space-y-4` - 行与行之间使用空白分隔

## 间距系统

### 垂直间距

- **板块间距**: `space-y-6` - 不同板块之间
- **信息行间距**: `space-y-4` - 同一板块内的信息行
- **标签与值间距**: `mb-2` - 标签和对应值之间
- **组件内边距**: `py-3` - 信息行的上下边距

### 水平间距

- **图标与文字**: `gap-3` - 图标和标题之间
- **按钮间距**: `gap-3` - 多个按钮之间
- **标签间距**: `gap-2` - 标签组之间

## 动画规范

### 入场动画

所有弹窗元素使用分层动画：

1. **遮罩层**: 淡入 (0.3s)
2. **容器**: 缩放 + 位移 + 淡入 (0.4s)
3. **头部元素**: 渐入 + 位移 (延迟 0.1-0.25s)
4. **内容区域**: 渐入 (延迟 0.2s)
5. **表单元素**: 渐入 + 位移 (延迟 0.3s+)

### 退场动画

- **容器**: 缩放到 0.95 + 上移 10px + 淡出
- **时长**: 比入场稍快，增强响应感

### 微交互

- **按钮**: 悬停时轻微放大 (scale: 1.02)，点击时缩小 (scale: 0.98)
- **关闭按钮**: 悬停时放大 (scale: 1.05)
- **表单元素**: 焦点时边框颜色变化，smooth 过渡

## 响应式设计

- **移动端**: 弹窗适配屏幕宽度，保持适当间距
- **桌面端**: 限制最大宽度，居中显示
- **支持的断点**: sm, md, lg, xl, 2xl, 4xl, 6xl

## 可访问性

- **键盘导航**: 支持 Tab 键导航
- **焦点管理**: 弹窗打开时焦点锁定
- **屏幕阅读器**: 适当的 ARIA 标签
- **颜色对比**: 满足 WCAG 标准

## 使用示例

### 基础弹窗

```jsx
<ModalWrapper
  isOpen={true}
  onClose={onClose}
  title="弹窗标题"
  subtitle="可选的副标题"
  icon={<Icon />}
  maxWidth="2xl"
>
  {/* 内容 */}
</ModalWrapper>
```

### 表单弹窗

```jsx
<ModalWrapper title="编辑表单" icon={<EditIcon />}>
  <div className="px-8 pb-8">
    <FormSection title="基本信息">
      <FormInput label="名称" required>
        <TextInput value={name} onChange={setName} />
      </FormInput>
    </FormSection>
  </div>
  
  <div className="p-8 pt-6 border-t border-theme-border">
    <div className="flex justify-end gap-3">
      <Button variant="secondary" onClick={onCancel}>取消</Button>
      <Button variant="primary" onClick={onSave}>保存</Button>
    </div>
  </div>
</ModalWrapper>
```

### 信息展示区域

```jsx
const Section = ({ title, children, icon: Icon }) => (
  <div className="bg-theme-background-secondary rounded-2xl p-6">
    <h3 className="text-lg font-semibold text-theme-foreground mb-4 flex items-center gap-3">
      <Icon className="w-5 h-5 text-theme-primary" />
      {title}
    </h3>
    <div className="border-t border-theme-border pt-4">
      <div className="space-y-4">
        {children}
      </div>
    </div>
  </div>
);

const InfoRow = ({ label, value }) => (
  <div className="py-3">
    <div className="text-sm font-medium text-theme-foreground-muted mb-2">
      {label}
    </div>
    <div className="text-theme-foreground">
      {value || <span className="text-theme-foreground-muted italic">未设置</span>}
    </div>
  </div>
);
```

### 页面按钮

```jsx
<Button
  variant="primary"
  className="flex items-center gap-2"
>
  <Icon className="w-4 h-4" />
  <span>按钮文字</span>
</Button>
```

## 注意事项

1. **一致性**: 所有弹窗必须使用 ModalWrapper 作为基础
2. **字体统一**: 严格按照字体系统规范使用字体大小和字重
3. **简洁原则**: 优先使用空白间距，避免过度使用分隔线
4. **性能**: 使用 AnimatePresence 确保退场动画完整播放
5. **可维护性**: 所有样式通过统一组件管理，避免内联样式
6. **可扩展性**: 设计支持未来添加新的变体和功能

## 设计理念

### 简洁优先

- 使用空白间距代替分隔线，减少视觉噪音
- 仅在必要的结构分离处使用边框
- 保持界面的呼吸感和层次感

### 一致性

- 所有组件遵循统一的间距系统
- 颜色使用遵循主题规范
- 动画效果保持一致的时长和缓动

## 更新记录

- **2024-01**: 初始版本，统一所有弹窗组件样式
- **2024-01-fix**: 添加字体系统规范，修复按钮样式不一致问题
- **2024-01-border**: 修复边框透明度问题，添加正确的透明度使用指南
- **2024-01-simplify**: 简化设计，移除过多分隔线，采用空白间距优先的设计理念
- 参考实现: ModelfileForm.tsx, ModelDetailsModal.tsx, ModelForm.tsx 