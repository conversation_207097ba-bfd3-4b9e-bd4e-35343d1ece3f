"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/model-manager/page",{

/***/ "(app-pages-browser)/./src/app/model-manager/components/ModelDetailsModal.tsx":
/*!****************************************************************!*\
  !*** ./src/app/model-manager/components/ModelDetailsModal.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelDetailsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/model-manager/components/ModelLogo */ \"(app-pages-browser)/./src/app/model-manager/components/ModelLogo.tsx\");\n/* harmony import */ var _ModalWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModalWrapper */ \"(app-pages-browser)/./src/app/model-manager/components/ModalWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// 格式化文件大小\nconst formatFileSize = (bytes)=>{\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\nconst InfoRow = (param)=>{\n    let { label, value, mono = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm font-medium text-theme-foreground-muted mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-theme-foreground \".concat(mono ? 'font-mono text-sm' : '', \" \").concat(mono ? 'bg-theme-background-secondary px-4 py-3 rounded-xl text-xs break-all' : ''),\n                children: value\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-theme-foreground-muted text-sm italic\",\n                children: \"未设置\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n};\n_c = InfoRow;\nfunction ModelDetailsModal(param) {\n    let { model, onClose } = param;\n    if (!model) return null;\n    const modalIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        modelName: model.family || model.base_model,\n        containerSize: 56,\n        imageSize: 32,\n        className: \"bg-theme-background-secondary rounded-2xl\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n    const headerContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-2 mt-3\",\n        children: model.tags && model.tags.length > 0 && model.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-3 py-1 bg-theme-primary/10 text-theme-primary text-xs rounded-full border border-theme-primary/20\",\n                children: tag\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModalWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        isOpen: true,\n        onClose: onClose,\n        title: model.display_name,\n        subtitle: model.base_model,\n        maxWidth: \"4xl\",\n        icon: modalIcon,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full flex-col min-h-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-8 pb-4\",\n                    children: headerContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto scrollbar-thin px-8 pb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"section-title !text-theme-foreground-muted mb-6\",\n                                        children: \"基本信息\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"模型别名\",\n                                                        value: model.display_name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"基础模型\",\n                                                        value: model.base_model\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"模型家族\",\n                                                        value: model.family\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"架构\",\n                                                        value: model.architecture || '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"参数规模\",\n                                                        value: model.parameter_count ? \"\".concat((model.parameter_count / 1e9).toFixed(1), \"B\") : '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"文件大小\",\n                                                        value: model.size ? formatFileSize(model.size) : '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"上下文长度\",\n                                                        value: model.context_length ? model.context_length.toLocaleString() : '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"嵌入长度\",\n                                                        value: model.embedding_length ? model.embedding_length.toLocaleString() : '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"量化级别\",\n                                                        value: model.quantization_level || '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"文件格式\",\n                                                        value: model.format || '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"模型能力\",\n                                                        value: model.capabilities && model.capabilities.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: model.capabilities.map((capability, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-theme-primary/10 text-theme-primary text-xs rounded-full border border-theme-primary/20\",\n                                                                    children: capability\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 27\n                                                                }, void 0))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, void 0) : '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"更新时间\",\n                                                        value: model.updated_at ? new Date(model.updated_at).toLocaleString('zh-CN') : new Date(model.created_at).toLocaleString('zh-CN')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                        label: \"Ollama修改时间\",\n                                                        value: model.ollama_modified_at ? new Date(model.ollama_modified_at).toLocaleString('zh-CN') : '未知'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                            label: \"模型描述\",\n                                            value: model.description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-theme-foreground leading-relaxed text-sm\",\n                                                children: model.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, void 0) : null\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"section-title !text-theme-foreground-muted mb-6\",\n                                        children: \"高级配置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                label: \"系统提示\",\n                                                value: model.system_prompt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-sm bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border overflow-x-auto scrollbar-thin\",\n                                                    children: model.system_prompt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, void 0) : null\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                label: \"模型参数\",\n                                                value: model.parameters && Object.keys(model.parameters).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border space-y-2\",\n                                                    children: Object.entries(model.parameters).map((param)=>{\n                                                        let [key, value] = param;\n                                                        const displayValue = Array.isArray(value) ? value.join(', ') : String(value);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-theme-foreground-muted\",\n                                                                    children: [\n                                                                        key,\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-mono text-theme-foreground\",\n                                                                    children: displayValue\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 27\n                                                        }, void 0);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, void 0) : null\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                label: \"模板\",\n                                                value: model.template ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-sm bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border overflow-x-auto scrollbar-thin font-mono\",\n                                                    children: model.template\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, void 0) : null\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"section-title !text-theme-foreground-muted mb-6\",\n                                        children: \"许可证\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                        label: \"许可证信息\",\n                                        value: model.license ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"whitespace-pre-wrap text-sm bg-theme-background-secondary px-4 py-3 rounded-xl border border-theme-border overflow-x-auto scrollbar-thin\",\n                                            children: model.license\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, void 0) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ModelDetailsModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"InfoRow\");\n$RefreshReg$(_c1, \"ModelDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/model-manager/components/ModelDetailsModal.tsx\n"));

/***/ })

});