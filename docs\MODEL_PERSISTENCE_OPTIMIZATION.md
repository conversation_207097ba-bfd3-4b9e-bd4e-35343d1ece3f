# 模型选择持久化优化

## 功能概述

实现了智能的模型选择持久化系统，确保用户的模型选择在页面刷新和对话切换时能够正确保存和恢复。

## 主要功能

### 1. 全局模型选择持久化
- 用户在主页或聊天页面选择的模型会保存到 `localStorage`
- 页面刷新时自动恢复用户的模型选择
- 键名：`chat_selected_model`

### 2. 对话特定模型持久化
- 每个对话的模型选择单独保存
- 键名格式：`chat_conversation_model_{conversationId}`
- 进入历史对话时优先使用该对话最后使用的模型

### 3. 智能模型选择策略

按优先级顺序选择模型：

1. **对话中最后使用的模型**（从数据库获取）
2. **对话特定保存的模型**（从localStorage获取）
3. **对话创建时的模型**（从对话记录获取）
4. **全局保存的模型**（从localStorage获取）
5. **第一个可用模型**（fallback）

### 4. 模型可用性检查
- 自动检查保存的模型是否在当前可用模型列表中
- 如果保存的模型不可用，自动选择下一个优先级的模型
- 避免选择不存在的模型导致的错误

## 技术实现

### 核心文件修改

1. **useChatMessages.ts** - 核心持久化逻辑
   - 添加了 `selectBestModel` 智能选择函数
   - 实现了全局和对话特定的模型保存
   - 提供了模型选择的完整生命周期管理

2. **useMessageLoader.ts** - 对话加载优化
   - 集成智能模型选择
   - 从API获取对话的最后使用模型
   - 简化了模型选择逻辑

3. **page.tsx** (主页) - 全局模型持久化
   - 页面加载时恢复保存的模型选择
   - 模型切换时自动保存

4. **page.tsx** (聊天页) - 对话模型持久化
   - 模型切换时保存对话特定的选择
   - 集成智能模型选择功能

### 数据库支持

- 利用现有的 `getLastModelByConversationId` 函数
- 从消息记录中获取对话最后使用的模型
- API 返回 `lastModel` 字段供前端使用

## 用户体验改进

### 1. 无缝的模型切换体验
- 用户在不同对话间切换时，自动恢复该对话的模型选择
- 页面刷新不会丢失模型选择状态

### 2. 智能的默认选择
- 新用户首次使用时选择第一个可用模型
- 老用户自动恢复之前的选择偏好

### 3. 容错机制
- 模型不可用时自动fallback到可用模型
- 避免因模型选择错误导致的功能异常

## 使用场景

### 场景1：页面刷新
1. 用户在聊天页面选择了模型A
2. 刷新页面
3. 系统自动恢复模型A的选择

### 场景2：对话切换
1. 用户在对话1中使用模型A
2. 切换到对话2，选择模型B
3. 再次回到对话1时，自动恢复模型A

### 场景3：历史对话恢复
1. 用户进入一个历史对话
2. 系统检查该对话最后使用的模型
3. 自动选择该模型（如果可用）

## 调试信息

系统会在控制台输出详细的模型选择日志：
- `选择模型: {modelName} (策略: {strategyNumber})`
- `保存对话 {conversationId} 的模型选择: {modelName}`
- `为对话 {conversationId} 选择模型: {modelName}`

## 兼容性

- 向后兼容现有的对话和模型选择
- 不影响现有的数据库结构
- 渐进式增强，不会破坏现有功能 