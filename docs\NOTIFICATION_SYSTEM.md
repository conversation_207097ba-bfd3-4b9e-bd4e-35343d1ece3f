# 全局通知系统

## 概述

全局通知系统是一个统一的消息通知解决方案，用于替代浏览器原生的 `alert`、`confirm` 等弹窗，提供更加现代化和一致的用户体验。

## 特性

- 🎨 **多种通知类型**：支持成功、错误、警告、信息四种基本类型
- 🎯 **智能定位**：支持四个角落位置显示
- ⚡ **动画效果**：使用 Framer Motion 提供流畅的进入和退出动画
- 🎮 **交互操作**：支持操作按钮和手动/自动关闭
- 🎨 **主题适配**：完全适配项目的主题系统
- 📱 **响应式设计**：在不同设备上都有良好的显示效果
- 🔧 **高度可定制**：支持自定义图标、持续时间、操作按钮等

## 架构设计

```
通知系统架构
├── NotificationProvider     # 上下文提供者
├── NotificationManager      # 通知管理器
├── NotificationContainer    # 通知容器
├── NotificationItem         # 单个通知组件
└── useNotification Hook     # 使用通知的 Hook
```

## 安装和配置

### 1. 应用级别集成

已在 `app/layout.tsx` 中集成：

```tsx
import { NotificationProvider } from '@/components/notification'
import { NotificationManager } from '@/components/notification/NotificationManager'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <ThemeProvider>
          <NotificationProvider>
            <div className="min-h-screen">
              {children}
            </div>
            <NotificationManager />
          </NotificationProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
```

### 2. 组件中使用

```tsx
import { useNotification } from '@/components/notification';

function MyComponent() {
  const notification = useNotification();

  const handleSuccess = () => {
    notification.success('操作成功', '数据已保存');
  };

  // ... 其他代码
}
```

## API 参考

### useNotification Hook

```tsx
const notification = useNotification();
```

返回的对象包含以下方法：

#### 基础方法

```tsx
// 成功通知
notification.success(title: string, message?: string, options?: Partial<NotificationConfig>)

// 错误通知  
notification.error(title: string, message?: string, options?: Partial<NotificationConfig>)

// 警告通知
notification.warning(title: string, message?: string, options?: Partial<NotificationConfig>)

// 信息通知
notification.info(title: string, message?: string, options?: Partial<NotificationConfig>)

// 自定义通知
notification.show(config: NotificationConfig)

// 关闭指定通知
notification.dismiss(id: string)

// 清除所有通知
notification.dismissAll()
```

#### 配置选项

```tsx
interface NotificationConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;        // 持续时间（毫秒），0 表示不自动关闭
  dismissible?: boolean;    // 是否可手动关闭
  actions?: NotificationAction[];  // 操作按钮
  icon?: React.ReactNode;   // 自定义图标
}

interface NotificationAction {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}
```

## 使用示例

### 基础用法

```tsx
import { useNotification } from '@/components/notification';

function UserProfile() {
  const notification = useNotification();

  const saveProfile = async () => {
    try {
      await saveUserData();
      notification.success('保存成功', '用户资料已更新');
    } catch (error) {
      notification.error('保存失败', '请检查网络连接后重试');
    }
  };

  const showWarning = () => {
    notification.warning('数据未保存', '您有未保存的更改');
  };

  const showInfo = () => {
    notification.info('提示', '系统将在10分钟后维护');
  };

  return (
    <div>
      <button onClick={saveProfile}>保存</button>
      <button onClick={showWarning}>显示警告</button>
      <button onClick={showInfo}>显示信息</button>
    </div>
  );
}
```

### 高级用法

```tsx
// 带操作按钮的通知
const showConfirmation = () => {
  notification.show({
    type: 'warning',
    title: '确认删除',
    message: '此操作无法撤销，确定要删除吗？',
    duration: 0,  // 不自动关闭
    actions: [
      {
        label: '确认',
        variant: 'primary',
        onClick: () => {
          deleteItem();
          notification.success('删除成功', '项目已删除');
        }
      },
      {
        label: '取消',
        variant: 'secondary',
        onClick: () => {
          notification.info('已取消', '删除操作已取消');
        }
      }
    ]
  });
};

// 自定义图标的通知
const showCustomIcon = () => {
  notification.show({
    type: 'info',
    title: '新消息',
    message: '您有一条新的私信',
    icon: <MessageCircle className="w-5 h-5" />
  });
};

// 持久通知（不自动消失）
const showPersistent = () => {
  notification.show({
    type: 'error',
    title: '连接错误',
    message: '无法连接到服务器，请检查网络',
    duration: 0,
    dismissible: true
  });
};
```

### 替换现有的 Toast 组件

如果项目中已有 Toast 组件，可以这样迁移：

```tsx
// 旧代码
const [toast, setToast] = useState(null);
const showToast = (message, type) => setToast({ message, type });

// 新代码
const notification = useNotification();
const showToast = (message, type) => {
  switch(type) {
    case 'success':
      notification.success(message);
      break;
    case 'error':
      notification.error(message);
      break;
    case 'warning':
      notification.warning(message);
      break;
    default:
      notification.info(message);
  }
};
```

## 配置选项

### NotificationProvider 配置

```tsx
<NotificationProvider
  maxNotifications={10}      // 最大通知数量
  defaultDuration={4000}     // 默认持续时间
>
  {children}
</NotificationProvider>
```

### NotificationManager 配置

```tsx
<NotificationManager
  position="top-right"       // 显示位置
  maxNotifications={5}       // 同时显示的最大数量
/>
```

支持的位置：
- `top-right` (默认)
- `top-left`
- `bottom-right`
- `bottom-left`

## 样式定制

通知系统完全使用项目的主题系统，会自动适配明暗主题：

- 成功通知：绿色调
- 错误通知：红色调  
- 警告通知：黄色调
- 信息通知：蓝色调

如需自定义样式，可以通过 CSS 变量或 Tailwind 类名进行调整。

## 最佳实践

### 1. 通知类型选择

- **成功 (Success)**：操作成功完成时使用
- **错误 (Error)**：操作失败或出现错误时使用
- **警告 (Warning)**：需要用户注意的重要信息
- **信息 (Info)**：一般性提示信息

### 2. 消息内容

- 标题应简洁明了，描述核心信息
- 消息内容提供详细说明或操作指导
- 避免过长的文本，保持可读性

### 3. 持续时间

- 成功通知：4秒（默认）
- 错误通知：6秒（默认，稍长）
- 警告通知：5秒
- 重要通知：设为0（不自动关闭）

### 4. 操作按钮

- 最多不超过2个操作按钮
- 主要操作使用 primary 样式
- 次要操作使用 secondary 样式

## 示例页面

可以访问演示组件查看各种通知效果：

```tsx
import { NotificationDemo } from '@/components/notification';

export default function DemoPage() {
  return <NotificationDemo />;
}
```

## 故障排除

### 通知不显示

1. 确认已在应用根部包装 `NotificationProvider`
2. 确认已添加 `NotificationManager` 组件
3. 检查是否在 Provider 内部使用 `useNotification`

### 样式问题

1. 确认主题系统正常工作
2. 检查 CSS 变量是否正确加载
3. 确认 Tailwind CSS 配置包含必要的类名

### 动画问题

1. 确认已安装 `framer-motion`
2. 检查是否与其他动画库冲突

## 更新日志

### v1.0.0
- 初始版本发布
- 支持四种基本通知类型
- 集成主题系统
- 支持操作按钮和自定义图标 