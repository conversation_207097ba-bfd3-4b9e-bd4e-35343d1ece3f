"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx":
/*!*************************************************************!*\
  !*** ./src/app/simple-chat/components/chat/MessageList.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageList: () => (/* binding */ MessageList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/ThinkingMode */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/ThinkingMode.tsx\");\n/* harmony import */ var _ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/StreamedContent */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/StreamedContent.tsx\");\n/* harmony import */ var _tools_ToolCallMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tools/ToolCallMessage */ \"(app-pages-browser)/./src/app/simple-chat/components/tools/ToolCallMessage.tsx\");\n/* harmony import */ var _app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/model-manager/components/ModelLogo */ \"(app-pages-browser)/./src/app/model-manager/components/ModelLogo.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MessageList(param) {\n    let { messages, isStreaming, expandedThinkingMessages, onToggleThinkingExpand, chatStyle, selectedModel } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isNearBottom, setIsNearBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isNearTop, setIsNearTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollButtons, setShowScrollButtons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageCount, setMessageCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(messages.length);\n    const [userScrolled, setUserScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastScrollTime, setLastScrollTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const scrollTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 获取模型的显示信息\n    const getModelDisplayInfo = (modelName)=>{\n        if (!modelName) return {\n            displayName: 'AI助手',\n            family: 'default'\n        };\n        const customModel = customModels.find((m)=>m.base_model === modelName);\n        return {\n            displayName: (customModel === null || customModel === void 0 ? void 0 : customModel.display_name) || modelName,\n            family: (customModel === null || customModel === void 0 ? void 0 : customModel.family) || modelName\n        };\n    };\n    // 检查滚动位置 - 寻找真正的滚动容器\n    const checkScrollPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[checkScrollPosition]\": ()=>{\n            // 先尝试当前组件的滚动容器\n            let container = scrollContainerRef.current;\n            // 如果当前容器没有滚动条，查找父级的滚动容器\n            if (container && container.scrollHeight <= container.clientHeight) {\n                // 查找最近的可滚动父元素\n                let parent = container.parentElement;\n                while(parent){\n                    if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                        container = parent;\n                        break;\n                    }\n                    parent = parent.parentElement;\n                }\n            }\n            if (!container) return {\n                nearBottom: true,\n                nearTop: true\n            };\n            const { scrollTop, scrollHeight, clientHeight } = container;\n            const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);\n            const distanceFromTop = scrollTop;\n            // 检测是否接近顶部和底部 - 考虑段落间距影响\n            // space-y-4 = 16px，加上padding和其他间距，使用更宽松的阈值\n            const nearBottom = distanceFromBottom <= 50; // 放宽底部检测，应对段落间距\n            const nearTop = distanceFromTop <= 50;\n            // 智能显示按钮：当有足够内容可以滚动时就显示\n            const hasEnoughContentToScroll = scrollHeight > clientHeight + 100; // 内容高度超过容器高度100px以上\n            const showButtons = messages.length > 0 && hasEnoughContentToScroll;\n            setIsNearBottom(nearBottom);\n            setIsNearTop(nearTop);\n            setShowScrollButtons(showButtons);\n            return {\n                nearBottom,\n                nearTop\n            };\n        }\n    }[\"MessageList.useCallback[checkScrollPosition]\"], [\n        messages.length\n    ]);\n    // 滚动到底部 - 优化定位精度\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[scrollToBottom]\": function() {\n            let behavior = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'smooth';\n            var _scrollContainerRef_current;\n            // 方法1：使用 scrollIntoView，但加上 block: 'end' 确保精确定位\n            if (messagesEndRef.current) {\n                messagesEndRef.current.scrollIntoView({\n                    behavior,\n                    block: 'end',\n                    inline: 'nearest'\n                });\n                return;\n            }\n            // 方法2：备用方案，直接滚动到容器底部\n            const container = (_scrollContainerRef_current = scrollContainerRef.current) === null || _scrollContainerRef_current === void 0 ? void 0 : _scrollContainerRef_current.parentElement;\n            if (container && container.scrollHeight > container.clientHeight) {\n                container.scrollTo({\n                    top: container.scrollHeight,\n                    behavior\n                });\n            }\n        }\n    }[\"MessageList.useCallback[scrollToBottom]\"], []);\n    // 滚动到顶部\n    const scrollToTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[scrollToTop]\": function() {\n            let behavior = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'smooth';\n            // 寻找可滚动的容器\n            let container = scrollContainerRef.current;\n            // 如果当前容器不可滚动，查找父级滚动容器\n            if (container && container.scrollHeight <= container.clientHeight) {\n                let parent = container.parentElement;\n                while(parent){\n                    if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                        container = parent;\n                        break;\n                    }\n                    parent = parent.parentElement;\n                }\n            }\n            if (container) {\n                container.scrollTo({\n                    top: 0,\n                    behavior\n                });\n            }\n        }\n    }[\"MessageList.useCallback[scrollToTop]\"], []);\n    // 处理用户滚动 - 增加用户意图检测\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[handleScroll]\": ()=>{\n            const now = Date.now();\n            const timeSinceLastScroll = now - lastScrollTime;\n            // 如果距离上次滚动时间很短，认为是用户主动滚动\n            if (timeSinceLastScroll < 1000) {\n                setUserScrolled(true);\n                // 清除之前的定时器\n                if (scrollTimeoutRef.current) {\n                    clearTimeout(scrollTimeoutRef.current);\n                }\n                // 3秒后重置用户滚动状态\n                scrollTimeoutRef.current = setTimeout({\n                    \"MessageList.useCallback[handleScroll]\": ()=>{\n                        setUserScrolled(false);\n                    }\n                }[\"MessageList.useCallback[handleScroll]\"], 3000);\n            }\n            setLastScrollTime(now);\n            // 直接内联检查滚动位置，避免循环依赖\n            requestAnimationFrame({\n                \"MessageList.useCallback[handleScroll]\": ()=>{\n                    checkScrollPosition();\n                }\n            }[\"MessageList.useCallback[handleScroll]\"]);\n        }\n    }[\"MessageList.useCallback[handleScroll]\"], [\n        lastScrollTime\n    ]); // 移除checkScrollPosition依赖\n    // 监听外层滚动容器的滚动事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            // 找到外层的滚动容器并绑定事件\n            const findScrollContainer = {\n                \"MessageList.useEffect.findScrollContainer\": ()=>{\n                    let current = scrollContainerRef.current;\n                    if (!current) return null;\n                    // 向上找到真正的滚动容器\n                    let parent = current.parentElement;\n                    while(parent){\n                        if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                            return parent;\n                        }\n                        parent = parent.parentElement;\n                    }\n                    return current;\n                }\n            }[\"MessageList.useEffect.findScrollContainer\"];\n            const scrollContainer = findScrollContainer();\n            if (scrollContainer) {\n                scrollContainer.addEventListener('scroll', handleScroll, {\n                    passive: true\n                });\n                return ({\n                    \"MessageList.useEffect\": ()=>{\n                        scrollContainer.removeEventListener('scroll', handleScroll);\n                    }\n                })[\"MessageList.useEffect\"];\n            }\n        }\n    }[\"MessageList.useEffect\"], [\n        handleScroll\n    ]);\n    // 当消息发生变化时的智能滚动逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            const wasNearBottom = isNearBottom;\n            const newMessageCount = messages.length;\n            const hasNewMessages = newMessageCount > messageCount;\n            // 更新消息计数\n            setMessageCount(newMessageCount);\n            // 优化的智能滚动逻辑：\n            // 1. 用户主动滚动时，暂停自动滚动\n            // 2. 只有在接近底部且没有用户干预时才自动滚动\n            // 3. 流式更新使用防抖机制，减少频繁滚动\n            if (!userScrolled && wasNearBottom && (hasNewMessages || isStreaming)) {\n                // 清除之前的滚动定时器，实现防抖\n                if (scrollTimeoutRef.current) {\n                    clearTimeout(scrollTimeoutRef.current);\n                }\n                // 使用防抖延迟，避免频繁滚动导致的抖动\n                const scrollDelay = isStreaming ? 150 : 50; // 流式时更长延迟\n                scrollTimeoutRef.current = setTimeout({\n                    \"MessageList.useEffect\": ()=>{\n                        // 再次检查用户是否在此期间滚动了\n                        if (!userScrolled && isNearBottom) {\n                            // 流式更新时使用 'auto'，新消息时使用 'smooth'\n                            const behavior = isStreaming ? 'auto' : 'smooth';\n                            scrollToBottom(behavior);\n                        }\n                    }\n                }[\"MessageList.useEffect\"], scrollDelay);\n            }\n            // 延迟重新检查位置，避免与滚动冲突\n            setTimeout({\n                \"MessageList.useEffect\": ()=>{\n                    requestAnimationFrame({\n                        \"MessageList.useEffect\": ()=>{\n                            checkScrollPosition();\n                        }\n                    }[\"MessageList.useEffect\"]);\n                }\n            }[\"MessageList.useEffect\"], 200);\n        }\n    }[\"MessageList.useEffect\"], [\n        messages,\n        isStreaming,\n        isNearBottom,\n        messageCount,\n        userScrolled\n    ]);\n    // 初始化时滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            if (messages.length > 0) {\n                // 首次加载时直接滚动到底部\n                requestAnimationFrame({\n                    \"MessageList.useEffect\": ()=>{\n                        scrollToBottom('auto');\n                    }\n                }[\"MessageList.useEffect\"]);\n            }\n        }\n    }[\"MessageList.useEffect\"], []); // 只在组件首次挂载时执行\n    // 组件挂载后立即检查滚动位置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            // 延迟检查确保DOM完全渲染\n            const timer = setTimeout({\n                \"MessageList.useEffect.timer\": ()=>{\n                    // 内联检查逻辑，避免函数依赖\n                    let container = scrollContainerRef.current;\n                    if (container && container.scrollHeight <= container.clientHeight) {\n                        let parent = container.parentElement;\n                        while(parent){\n                            if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                                container = parent;\n                                break;\n                            }\n                            parent = parent.parentElement;\n                        }\n                    }\n                    if (!container) return;\n                    const { scrollTop, scrollHeight, clientHeight } = container;\n                    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);\n                    const distanceFromTop = scrollTop;\n                    const nearBottom = distanceFromBottom <= 50;\n                    const nearTop = distanceFromTop <= 50;\n                    const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;\n                    const showButtons = messages.length > 0 && hasEnoughContentToScroll;\n                    setIsNearBottom(nearBottom);\n                    setIsNearTop(nearTop);\n                    setShowScrollButtons(showButtons);\n                }\n            }[\"MessageList.useEffect.timer\"], 300);\n            return ({\n                \"MessageList.useEffect\": ()=>clearTimeout(timer)\n            })[\"MessageList.useEffect\"];\n        }\n    }[\"MessageList.useEffect\"], [\n        messages.length\n    ]); // 只依赖消息长度\n    // 清理定时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            return ({\n                \"MessageList.useEffect\": ()=>{\n                    if (scrollTimeoutRef.current) {\n                        clearTimeout(scrollTimeoutRef.current);\n                    }\n                }\n            })[\"MessageList.useEffect\"];\n        }\n    }[\"MessageList.useEffect\"], []);\n    // 格式化时间（纳秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    // 渲染生成统计信息图标\n    const renderGenerationStatsIcon = (message)=>{\n        // 检查是否为当前生成中的消息\n        const isCurrentlyGenerating = isStreaming && message.role === 'assistant' && messages.indexOf(message) === messages.length - 1;\n        // 如果有完整的统计数据（至少有总时长或生成token数量），显示详细信息\n        const hasCompleteStats = message.total_duration || message.eval_count;\n        const statsText = hasCompleteStats ? \"总时长: \".concat(formatDuration(message.total_duration), \"\\n\") + \"加载时长: \".concat(formatDuration(message.load_duration), \"\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : isCurrentlyGenerating ? '正在生成中，统计信息将在完成后显示...' : '统计信息不可用';\n        // 获取消息创建时间\n        const messageTime = message.timestamp ? new Date(message.timestamp).toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n        }) : new Date().toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative inline-block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group inline-block\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-theme-foreground-muted hover:text-theme-foreground cursor-help transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-0 bottom-full mb-1 bg-gray-800 text-white text-xs rounded px-3 py-2 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 min-w-max shadow-lg pointer-events-none\",\n                                children: statsText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-theme-foreground-muted\",\n                    children: messageTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n            lineNumber: 330,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                className: \"p-4 space-y-4\",\n                children: [\n                    messages.map((message, index)=>{\n                        // 如果是工具调用占位符消息，渲染工具调用组件\n                        if (message.role === 'tool_call' && message.toolCall) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_ToolCallMessage__WEBPACK_IMPORTED_MODULE_4__.ToolCallMessage, {\n                                toolCall: message.toolCall\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        // 检查消息是否包含思考内容\n                        const hasThinking = message.role === 'assistant' && (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.hasThinkingContent)(message.content);\n                        const contentWithoutThinking = hasThinking ? (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.removeThinkingContent)(message.content) : message.content;\n                        const isCurrentlyThinking = isStreaming && message.role === 'assistant' && index === messages.length - 1 && (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.hasThinkingContent)(message.content) && !(0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.removeThinkingContent)(message.content).trim();\n                        // 🔧 修复：检查是否应该显示消息气泡\n                        // 对于 assistant 消息，如果只有思考内容而没有实际内容，且不是正在生成状态，则不显示消息气泡\n                        const isLastMessage = index === messages.length - 1;\n                        const isGenerating = isStreaming && message.role === 'assistant' && isLastMessage;\n                        const hasActualContent = contentWithoutThinking.trim().length > 0;\n                        const shouldShowBubble = message.role === 'user' || hasActualContent || isGenerating && !isCurrentlyThinking;\n                        // 获取模型显示信息\n                        const modelDisplayInfo = getModelDisplayInfo(message.model || selectedModel);\n                        // 根据聊天样式决定布局\n                        if (chatStyle === 'conversation') {\n                            // 对话模式：用户右侧，AI左侧\n                            const isUser = message.role === 'user';\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 \".concat(isUser ? 'flex-row-reverse' : ''),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isUser ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            modelName: modelDisplayInfo.family,\n                                            size: \"lg\",\n                                            containerSize: 40,\n                                            imageSize: 32,\n                                            className: \"bg-transparent border-0 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] space-y-2 \".concat(isUser ? 'flex flex-col items-end' : ''),\n                                        children: [\n                                            (shouldShowBubble || hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 \".concat(isUser ? 'justify-end' : 'justify-start'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-theme-foreground-muted \".concat(isUser ? 'text-right' : 'text-left'),\n                                                        children: isUser ? '你' : modelDisplayInfo.displayName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !isUser && message.role === 'assistant' && renderGenerationStatsIcon(message)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.ThinkingMode, {\n                                                content: message.content,\n                                                isExpanded: expandedThinkingMessages.has(message.id),\n                                                onToggleExpand: ()=>onToggleThinkingExpand(message.id),\n                                                defaultHidden: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 21\n                                            }, this),\n                                            shouldShowBubble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-block p-3 rounded-lg \".concat(isUser ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    content: contentWithoutThinking || (isGenerating && !isCurrentlyThinking ? '正在生成回复...' : ''),\n                                                    isStreaming: isGenerating,\n                                                    enableMarkdown: !isUser,\n                                                    className: !isUser ? \"break-words leading-[1.4]\" : \"break-words whitespace-pre-wrap leading-[1.4]\",\n                                                    style: {\n                                                        minWidth: 0,\n                                                        maxWidth: '100%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, message.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this);\n                        } else {\n                            // 助手模式：所有消息都在左侧\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.role === 'user' ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                        children: message.role === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            modelName: modelDisplayInfo.family,\n                                            size: \"lg\",\n                                            containerSize: 40,\n                                            imageSize: 32,\n                                            className: \"bg-transparent border-0 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 space-y-2\",\n                                        children: [\n                                            (shouldShowBubble || hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-theme-foreground-muted\",\n                                                        children: message.role === 'user' ? '你' : modelDisplayInfo.displayName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    message.role === 'assistant' && renderGenerationStatsIcon(message)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.ThinkingMode, {\n                                                content: message.content,\n                                                isExpanded: expandedThinkingMessages.has(message.id),\n                                                onToggleExpand: ()=>onToggleThinkingExpand(message.id),\n                                                defaultHidden: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 21\n                                            }, this),\n                                            shouldShowBubble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none text-theme-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    content: contentWithoutThinking || (isGenerating && !isCurrentlyThinking ? '正在生成回复...' : ''),\n                                                    isStreaming: isGenerating,\n                                                    enableMarkdown: message.role === 'assistant',\n                                                    className: message.role === 'assistant' ? \"break-words leading-[1.4]\" : \"break-words whitespace-pre-wrap leading-[1.4]\",\n                                                    style: {\n                                                        minWidth: 0,\n                                                        maxWidth: '100%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, message.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 15\n                            }, this);\n                        }\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            showScrollButtons && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group fixed bottom-40 right-12 z-50 flex flex-col gap-1 p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-16 h-full -right-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-1 opacity-0 translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300 ease-out\",\n                        children: [\n                            !isNearTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToTop('smooth'),\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"回到顶部\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const { nearBottom, nearTop } = checkScrollPosition();\n                                    console.log('当前状态:', {\n                                        isNearTop,\n                                        isNearBottom,\n                                        nearTop,\n                                        nearBottom,\n                                        showScrollButtons\n                                    });\n                                },\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"调试信息\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this),\n                            !isNearBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToBottom('smooth'),\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"回到底部\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 514,\n                columnNumber: 10\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageList, \"kAtPFrOasN3r8MRT6w+/YHio3xY=\");\n_c = MessageList;\nvar _c;\n$RefreshReg$(_c, \"MessageList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx\n"));

/***/ })

});