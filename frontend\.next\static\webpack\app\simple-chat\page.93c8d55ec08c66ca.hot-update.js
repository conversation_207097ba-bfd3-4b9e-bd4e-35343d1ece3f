"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/hooks/useMessageLoader.ts":
/*!*******************************************************!*\
  !*** ./src/app/simple-chat/hooks/useMessageLoader.ts ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMessageLoader: () => (/* binding */ useMessageLoader)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMessageLoader auto */ \nfunction useMessageLoader(param) {\n    let { currentConversation, setSelectedModel, setMessages, setToolCalls, selectedModel, models, selectBestModel } = param;\n    // 加载对话消息历史 - 优化：添加缓存和重试机制\n    const loadConversationMessages = async function(conversationId) {\n        let retryCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            console.log(\"\\uD83D\\uDD04 加载对话 \".concat(conversationId, \" 的消息 (尝试 \").concat(retryCount + 1, \")\"));\n            const response = await fetch(\"/api/conversations/\".concat(conversationId));\n            if (!response.ok) {\n                if (response.status === 404) {\n                    throw new Error('对话不存在 (404)');\n                }\n                throw new Error(\"加载对话失败: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data.success && data.messages) {\n                // 处理消息格式转换逻辑\n                const allMessages = data.messages.sort((a, b)=>{\n                    if (a.timestamp !== b.timestamp) {\n                        return a.timestamp - b.timestamp;\n                    }\n                    return a.id - b.id;\n                });\n                const formattedMessages = [];\n                const toolCallMessages = [];\n                for (const msg of allMessages){\n                    if (msg.role === 'tool_call' && msg.tool_name) {\n                        // 处理工具调用消息\n                        let args = {};\n                        let result = '';\n                        try {\n                            args = msg.tool_args ? JSON.parse(msg.tool_args) : {};\n                        } catch (e) {\n                            args = {};\n                        }\n                        try {\n                            result = msg.tool_result ? typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result) : '';\n                        } catch (e) {\n                            result = msg.tool_result || '';\n                        }\n                        const toolCall = {\n                            id: \"tool-\".concat(msg.id),\n                            toolName: msg.tool_name,\n                            args: args,\n                            status: msg.tool_status || 'completed',\n                            result: result,\n                            error: msg.tool_error || undefined,\n                            startTime: msg.timestamp || new Date(msg.created_at).getTime(),\n                            executionTime: msg.tool_execution_time || 0\n                        };\n                        toolCallMessages.push(toolCall);\n                        formattedMessages.push({\n                            id: \"tool-placeholder-\".concat(msg.id),\n                            role: 'tool_call',\n                            content: '',\n                            timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                            toolCall: toolCall\n                        });\n                    } else {\n                        formattedMessages.push({\n                            id: \"msg-\".concat(msg.id),\n                            role: msg.role,\n                            content: msg.content,\n                            timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                            model: msg.model,\n                            // 添加统计字段\n                            total_duration: msg.total_duration,\n                            load_duration: msg.load_duration,\n                            prompt_eval_count: msg.prompt_eval_count,\n                            prompt_eval_duration: msg.prompt_eval_duration,\n                            eval_count: msg.eval_count,\n                            eval_duration: msg.eval_duration\n                        });\n                    }\n                }\n                console.log(\"✅ 成功加载对话 \".concat(conversationId, \" 的 \").concat(formattedMessages.length, \" 条消息\"));\n                setMessages(formattedMessages);\n                setToolCalls(toolCallMessages);\n                // 返回对话中最后使用的模型\n                return data.lastModel;\n            } else {\n                console.log(\"⚠️ 对话 \".concat(conversationId, \" 没有消息数据\"));\n                setMessages([]);\n                setToolCalls([]);\n                return null;\n            }\n        } catch (err) {\n            console.error('加载对话消息失败:', err);\n            throw new Error('加载对话消息失败');\n        }\n    };\n    // 当切换对话时，加载对话的消息历史\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMessageLoader.useEffect\": ()=>{\n            if (currentConversation) {\n                // 清空当前消息和工具调用\n                setMessages([]);\n                setToolCalls([]);\n                // 加载对话消息和最后使用的模型\n                loadConversationMessages(currentConversation.id).then({\n                    \"useMessageLoader.useEffect\": (lastUsedModel)=>{\n                        // 使用智能模型选择功能\n                        if (selectBestModel && models) {\n                            const selectedModelName = selectBestModel(models, currentConversation.id, lastUsedModel, currentConversation.model);\n                            if (selectedModelName) {\n                                console.log(\"为对话 \".concat(currentConversation.id, \" 选择模型: \").concat(selectedModelName));\n                            }\n                        }\n                    }\n                }[\"useMessageLoader.useEffect\"]).catch({\n                    \"useMessageLoader.useEffect\": (error)=>{\n                        console.error('加载消息失败:', error);\n                    }\n                }[\"useMessageLoader.useEffect\"]);\n            } else {\n                setMessages([]);\n                setToolCalls([]);\n            }\n        }\n    }[\"useMessageLoader.useEffect\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id,\n        models,\n        selectBestModel\n    ]); // 添加selectBestModel依赖\n    return {\n        loadConversationMessages\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/hooks/useMessageLoader.ts\n"));

/***/ })

});