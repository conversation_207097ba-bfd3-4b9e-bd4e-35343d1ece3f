[{"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\chat\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\clear\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\custom-models\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\custom-models\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\call-tool\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\config\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-list\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-status\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\connect\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\status\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tool-config\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\validate\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\create-modelfile\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\create-modelfile-from-path\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\prompt-optimize\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\layout.tsx": "23", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\AddServerModal.tsx": "24", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\LoadingSpinner.tsx": "25", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\PageHeader.tsx": "26", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\ToolsModal.tsx": "27", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\hooks\\useMcpConfig.ts": "28", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\types.ts": "30", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\FileUploadModelForm.tsx": "31", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\FormComponents.tsx": "32", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModalWrapper.tsx": "33", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelDetailsModal.tsx": "34", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelfileForm.tsx": "35", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelForm.tsx": "36", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelList.tsx": "37", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelLogo.tsx": "38", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\PageHeader.tsx": "39", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\page.tsx": "40", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page.tsx": "41", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\hooks\\index.ts": "42", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\hooks\\useAvailableModels.ts": "43", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\hooks\\usePromptOptimizeSettings.ts": "44", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\page.tsx": "45", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\Sidebar.tsx": "46", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\ChatContainer.tsx": "47", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\EmptyState.tsx": "48", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\MessageInput.tsx": "49", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\MessageList.tsx": "50", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\conversation\\ChatHeader.tsx": "51", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\index.ts": "52", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\BaseControlButton.tsx": "53", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\ChatActionsControl.tsx": "54", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\ChatStyleControl.tsx": "55", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\index.ts": "56", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\InputControlsGroup.tsx": "57", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\PromptOptimizeControl.tsx": "58", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\ToolControl.tsx": "59", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\tools\\ToolCallMessage.tsx": "60", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\tools\\ToolPanel.tsx": "61", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\tools\\ToolSettings.tsx": "62", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\ui\\ErrorDisplay.tsx": "63", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\ui\\StreamedContent.tsx": "64", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\ui\\ThinkingMode.tsx": "65", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\chat\\useChatMessages.ts": "66", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\index.ts": "67", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useChatStyle.ts": "68", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useConversationEventHandlers.ts": "69", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useConversationManager.ts": "70", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useMessageLoader.ts": "71", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useToolSettings.ts": "72", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useUrlHandler.ts": "73", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\page.tsx": "74", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\services\\streamingChatService.ts": "75", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\types.ts": "76", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\WelcomePage.tsx": "77", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\Modal.tsx": "78", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\examples\\NotificationDemo.tsx": "79", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\index.ts": "80", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContainer.tsx": "81", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx": "82", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationItem.tsx": "83", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationManager.tsx": "84", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\types.ts": "85", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\connection.ts": "86", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\conversations.ts": "87", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\custom-models.ts": "88", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\index.ts": "89", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-servers.ts": "90", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-tools.ts": "91", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\messages.ts": "92", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\types.ts": "93", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database.ts": "94", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-server.ts": "95", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-sse.ts": "96", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-streamable-http.ts": "97", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client.ts": "98", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-multi-server-client.ts": "99", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-server.ts": "100", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-tools.ts": "101", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\modelLogo.ts": "102", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\ollama.ts": "103", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\theme.ts": "104", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\tools.ts": "105", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ThemeScript.tsx": "106", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ThemeToggle.tsx": "107", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx": "108", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\hooks\\useThemePersistence.ts": "109", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\components\\AgentFormModal.tsx": "110", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\components\\AgentList.tsx": "111", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\page.tsx": "112", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\types.ts": "113", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\agents\\route.ts": "114", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\agents\\[id]\\route.ts": "115", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\AssistantModelTab.tsx": "116", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\index.ts": "117", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\MemorySection.tsx": "118", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\PromptOptimizeSection.tsx": "119", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\SettingsTabs.tsx": "120", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\TitleSummarySection.tsx": "121", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\PageHeader.tsx": "122", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\agents.ts": "123"}, {"size": 34557, "mtime": 1750912235013, "results": "124", "hashOfConfig": "125"}, {"size": 1593, "mtime": 1750912235202, "results": "126", "hashOfConfig": "125"}, {"size": 1319, "mtime": 1750657908920, "results": "127", "hashOfConfig": "125"}, {"size": 4035, "mtime": 1750323379458, "results": "128", "hashOfConfig": "125"}, {"size": 1811, "mtime": 1750907380748, "results": "129", "hashOfConfig": "125"}, {"size": 4292, "mtime": 1750907375884, "results": "130", "hashOfConfig": "125"}, {"size": 4737, "mtime": 1749954389275, "results": "131", "hashOfConfig": "125"}, {"size": 5272, "mtime": 1749971498866, "results": "132", "hashOfConfig": "125"}, {"size": 4298, "mtime": 1749976290268, "results": "133", "hashOfConfig": "125"}, {"size": 5451, "mtime": 1750424693503, "results": "134", "hashOfConfig": "125"}, {"size": 12633, "mtime": 1750897502977, "results": "135", "hashOfConfig": "125"}, {"size": 6009, "mtime": 1750316267294, "results": "136", "hashOfConfig": "125"}, {"size": 4109, "mtime": 1749724888781, "results": "137", "hashOfConfig": "125"}, {"size": 1276, "mtime": 1749691944366, "results": "138", "hashOfConfig": "125"}, {"size": 3146, "mtime": 1750912235260, "results": "139", "hashOfConfig": "125"}, {"size": 9047, "mtime": 1750896892160, "results": "140", "hashOfConfig": "125"}, {"size": 1650, "mtime": 1750424177275, "results": "141", "hashOfConfig": "125"}, {"size": 5493, "mtime": 1749782782039, "results": "142", "hashOfConfig": "125"}, {"size": 10038, "mtime": 1750912235241, "results": "143", "hashOfConfig": "125"}, {"size": 8559, "mtime": 1750591121346, "results": "144", "hashOfConfig": "125"}, {"size": 1699, "mtime": 1749564036885, "results": "145", "hashOfConfig": "125"}, {"size": 2715, "mtime": 1750840042030, "results": "146", "hashOfConfig": "125"}, {"size": 1130, "mtime": 1750821875508, "results": "147", "hashOfConfig": "125"}, {"size": 18420, "mtime": 1750580757263, "results": "148", "hashOfConfig": "125"}, {"size": 450, "mtime": 1750403265199, "results": "149", "hashOfConfig": "125"}, {"size": 1691, "mtime": 1750579569092, "results": "150", "hashOfConfig": "125"}, {"size": 16025, "mtime": 1750425691524, "results": "151", "hashOfConfig": "125"}, {"size": 11272, "mtime": 1750828260523, "results": "152", "hashOfConfig": "125"}, {"size": 14993, "mtime": 1750825843977, "results": "153", "hashOfConfig": "125"}, {"size": 2594, "mtime": 1749969651363, "results": "154", "hashOfConfig": "125"}, {"size": 12727, "mtime": 1750587937328, "results": "155", "hashOfConfig": "125"}, {"size": 7160, "mtime": 1750897028032, "results": "156", "hashOfConfig": "125"}, {"size": 2883, "mtime": 1750580099919, "results": "157", "hashOfConfig": "125"}, {"size": 9009, "mtime": 1750591496755, "results": "158", "hashOfConfig": "125"}, {"size": 25660, "mtime": 1750579571446, "results": "159", "hashOfConfig": "125"}, {"size": 5959, "mtime": 1750578091540, "results": "160", "hashOfConfig": "125"}, {"size": 6261, "mtime": 1750555946428, "results": "161", "hashOfConfig": "125"}, {"size": 3086, "mtime": 1750556262633, "results": "162", "hashOfConfig": "125"}, {"size": 1276, "mtime": 1750895993179, "results": "163", "hashOfConfig": "125"}, {"size": 14763, "mtime": 1750828260523, "results": "164", "hashOfConfig": "125"}, {"size": 5058, "mtime": 1750731776369, "results": "165", "hashOfConfig": "125"}, {"size": 272, "mtime": 1750841099665, "results": "166", "hashOfConfig": "125"}, {"size": 3178, "mtime": 1750841099665, "results": "167", "hashOfConfig": "125"}, {"size": 2470, "mtime": 1750841099665, "results": "168", "hashOfConfig": "125"}, {"size": 5509, "mtime": 1750844913714, "results": "169", "hashOfConfig": "125"}, {"size": 16116, "mtime": 1750904363074, "results": "170", "hashOfConfig": "125"}, {"size": 5511, "mtime": 1750895993236, "results": "171", "hashOfConfig": "125"}, {"size": 864, "mtime": 1750821620647, "results": "172", "hashOfConfig": "125"}, {"size": 3982, "mtime": 1750913881661, "results": "173", "hashOfConfig": "125"}, {"size": 21446, "mtime": 1750915557694, "results": "174", "hashOfConfig": "125"}, {"size": 3297, "mtime": 1750895993212, "results": "175", "hashOfConfig": "125"}, {"size": 866, "mtime": 1750734206539, "results": "176", "hashOfConfig": "125"}, {"size": 4630, "mtime": 1750733401465, "results": "177", "hashOfConfig": "125"}, {"size": 499, "mtime": 1750733030323, "results": "178", "hashOfConfig": "125"}, {"size": 5545, "mtime": 1750743066504, "results": "179", "hashOfConfig": "125"}, {"size": 455, "mtime": 1750841099657, "results": "180", "hashOfConfig": "125"}, {"size": 2258, "mtime": 1750841099657, "results": "181", "hashOfConfig": "125"}, {"size": 8266, "mtime": 1750841099657, "results": "182", "hashOfConfig": "125"}, {"size": 2627, "mtime": 1750733030323, "results": "183", "hashOfConfig": "125"}, {"size": 7485, "mtime": 1750674779187, "results": "184", "hashOfConfig": "125"}, {"size": 7701, "mtime": 1750674954738, "results": "185", "hashOfConfig": "125"}, {"size": 2388, "mtime": 1750841099657, "results": "186", "hashOfConfig": "125"}, {"size": 1676, "mtime": 1750675523931, "results": "187", "hashOfConfig": "125"}, {"size": 1218, "mtime": 1750914282963, "results": "188", "hashOfConfig": "125"}, {"size": 4068, "mtime": 1750749933921, "results": "189", "hashOfConfig": "125"}, {"size": 8421, "mtime": 1750898690632, "results": "190", "hashOfConfig": "125"}, {"size": 524, "mtime": 1750691114843, "results": "191", "hashOfConfig": "125"}, {"size": 1871, "mtime": 1750742748951, "results": "192", "hashOfConfig": "125"}, {"size": 4791, "mtime": 1750690535621, "results": "193", "hashOfConfig": "125"}, {"size": 6499, "mtime": 1750821543840, "results": "194", "hashOfConfig": "125"}, {"size": 5364, "mtime": 1750815596442, "results": "195", "hashOfConfig": "125"}, {"size": 4413, "mtime": 1750661519660, "results": "196", "hashOfConfig": "125"}, {"size": 3030, "mtime": 1750821584212, "results": "197", "hashOfConfig": "125"}, {"size": 22284, "mtime": 1750913542679, "results": "198", "hashOfConfig": "125"}, {"size": 6084, "mtime": 1750814671917, "results": "199", "hashOfConfig": "125"}, {"size": 1505, "mtime": 1750765789588, "results": "200", "hashOfConfig": "125"}, {"size": 2491, "mtime": 1750667376444, "results": "201", "hashOfConfig": "125"}, {"size": 4831, "mtime": 1750895993364, "results": "202", "hashOfConfig": "125"}, {"size": 5877, "mtime": 1750823882100, "results": "203", "hashOfConfig": "125"}, {"size": 401, "mtime": 1750822108723, "results": "204", "hashOfConfig": "125"}, {"size": 3722, "mtime": 1750826029730, "results": "205", "hashOfConfig": "125"}, {"size": 3504, "mtime": 1750821842676, "results": "206", "hashOfConfig": "125"}, {"size": 6269, "mtime": 1750826071178, "results": "207", "hashOfConfig": "125"}, {"size": 690, "mtime": 1750821853689, "results": "208", "hashOfConfig": "125"}, {"size": 1275, "mtime": 1750823882100, "results": "209", "hashOfConfig": "125"}, {"size": 8720, "mtime": 1750907367670, "results": "210", "hashOfConfig": "125"}, {"size": 1805, "mtime": 1749976751706, "results": "211", "hashOfConfig": "125"}, {"size": 18044, "mtime": 1750560540656, "results": "212", "hashOfConfig": "125"}, {"size": 3092, "mtime": 1750895993135, "results": "213", "hashOfConfig": "125"}, {"size": 3863, "mtime": 1749976787277, "results": "214", "hashOfConfig": "125"}, {"size": 3638, "mtime": 1750897224456, "results": "215", "hashOfConfig": "125"}, {"size": 4980, "mtime": 1750661519660, "results": "216", "hashOfConfig": "125"}, {"size": 4247, "mtime": 1750321626212, "results": "217", "hashOfConfig": "125"}, {"size": 312, "mtime": 1749977103967, "results": "218", "hashOfConfig": "125"}, {"size": 5039, "mtime": 1749725998327, "results": "219", "hashOfConfig": "125"}, {"size": 11394, "mtime": 1749779080651, "results": "220", "hashOfConfig": "125"}, {"size": 15167, "mtime": 1749784407894, "results": "221", "hashOfConfig": "125"}, {"size": 4792, "mtime": 1750055875481, "results": "222", "hashOfConfig": "125"}, {"size": 18856, "mtime": 1749971621651, "results": "223", "hashOfConfig": "125"}, {"size": 9457, "mtime": 1750060506753, "results": "224", "hashOfConfig": "125"}, {"size": 6634, "mtime": 1749726048431, "results": "225", "hashOfConfig": "125"}, {"size": 2746, "mtime": 1750554948700, "results": "226", "hashOfConfig": "125"}, {"size": 10322, "mtime": 1750572496340, "results": "227", "hashOfConfig": "125"}, {"size": 1846, "mtime": 1750418829057, "results": "228", "hashOfConfig": "125"}, {"size": 17986, "mtime": 1750055759029, "results": "229", "hashOfConfig": "125"}, {"size": 2866, "mtime": 1750904178591, "results": "230", "hashOfConfig": "125"}, {"size": 4906, "mtime": 1750417775699, "results": "231", "hashOfConfig": "125"}, {"size": 2435, "mtime": 1750401884669, "results": "232", "hashOfConfig": "125"}, {"size": 3343, "mtime": 1750402025632, "results": "233", "hashOfConfig": "125"}, {"size": 7477, "mtime": 1750897289696, "results": "234", "hashOfConfig": "125"}, {"size": 2317, "mtime": 1750895993195, "results": "235", "hashOfConfig": "125"}, {"size": 5038, "mtime": 1750896868976, "results": "236", "hashOfConfig": "125"}, {"size": 83, "mtime": 1750895993110, "results": "237", "hashOfConfig": "125"}, {"size": 1786, "mtime": 1750895993343, "results": "238", "hashOfConfig": "125"}, {"size": 3131, "mtime": 1750898159479, "results": "239", "hashOfConfig": "125"}, {"size": 1924, "mtime": 1750847816099, "results": "240", "hashOfConfig": "125"}, {"size": 283, "mtime": 1750847816099, "results": "241", "hashOfConfig": "125"}, {"size": 3908, "mtime": 1750847816099, "results": "242", "hashOfConfig": "125"}, {"size": 3289, "mtime": 1750847816099, "results": "243", "hashOfConfig": "125"}, {"size": 1744, "mtime": 1750847816099, "results": "244", "hashOfConfig": "125"}, {"size": 3197, "mtime": 1750847816099, "results": "245", "hashOfConfig": "125"}, {"size": 1526, "mtime": 1750895993156, "results": "246", "hashOfConfig": "125"}, {"size": 5427, "mtime": 1750854540915, "results": "247", "hashOfConfig": "125"}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wlrykr", {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\chat\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\clear\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\custom-models\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\custom-models\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\call-tool\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\config\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-list\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\connect\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tool-config\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\validate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\create-modelfile\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\create-modelfile-from-path\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\prompt-optimize\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\AddServerModal.tsx", ["617"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\PageHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\ToolsModal.tsx", [], ["618"], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\hooks\\useMcpConfig.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\FileUploadModelForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\FormComponents.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModalWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelfileForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\ModelLogo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\components\\PageHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\page.tsx", ["619"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\hooks\\useAvailableModels.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\hooks\\usePromptOptimizeSettings.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\Sidebar.tsx", ["620"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\ChatContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\EmptyState.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\MessageInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\chat\\MessageList.tsx", ["621"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\conversation\\ChatHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\BaseControlButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\ChatActionsControl.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\ChatStyleControl.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\InputControlsGroup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\PromptOptimizeControl.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\input-controls\\ToolControl.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\tools\\ToolCallMessage.tsx", ["622"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\tools\\ToolPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\tools\\ToolSettings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\ui\\ErrorDisplay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\ui\\StreamedContent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\components\\ui\\ThinkingMode.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\chat\\useChatMessages.ts", [], ["623"], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useChatStyle.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useConversationEventHandlers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useConversationManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useMessageLoader.ts", ["624"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useToolSettings.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\hooks\\useUrlHandler.ts", ["625"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\page.tsx", ["626", "627"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\services\\streamingChatService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\simple-chat\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\WelcomePage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\Modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\examples\\NotificationDemo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContainer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationItem.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationManager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\connection.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\conversations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\custom-models.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-servers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-tools.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\messages.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-sse.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-streamable-http.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-multi-server-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-tools.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\modelLogo.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\ollama.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\theme.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\tools.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ThemeScript.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\hooks\\useThemePersistence.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\components\\AgentFormModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\components\\AgentList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\agents\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\agents\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\AssistantModelTab.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\MemorySection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\PromptOptimizeSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\SettingsTabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\settings\\components\\TitleSummarySection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\PageHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\agents.ts", [], [], {"ruleId": "628", "severity": 1, "message": "629", "line": 63, "column": 6, "nodeType": "630", "endLine": 63, "endColumn": 92, "suggestions": "631"}, {"ruleId": "628", "severity": 1, "message": "632", "line": 45, "column": 6, "nodeType": "630", "endLine": 45, "endColumn": 25, "suggestions": "633", "suppressions": "634"}, {"ruleId": "628", "severity": 1, "message": "635", "line": 307, "column": 6, "nodeType": "630", "endLine": 307, "endColumn": 8, "suggestions": "636"}, {"ruleId": "637", "severity": 1, "message": "638", "line": 167, "column": 11, "nodeType": "639", "endLine": 171, "endColumn": 13}, {"ruleId": "628", "severity": 1, "message": "640", "line": 219, "column": 6, "nodeType": "630", "endLine": 219, "endColumn": 8, "suggestions": "641"}, {"ruleId": "637", "severity": 1, "message": "638", "line": 60, "column": 19, "nodeType": "639", "endLine": 67, "endColumn": 21}, {"ruleId": "628", "severity": 1, "message": "642", "line": 189, "column": 6, "nodeType": "630", "endLine": 189, "endColumn": 8, "suggestions": "643", "suppressions": "644"}, {"ruleId": "628", "severity": 1, "message": "645", "line": 161, "column": 6, "nodeType": "630", "endLine": 161, "endColumn": 56, "suggestions": "646"}, {"ruleId": "628", "severity": 1, "message": "647", "line": 83, "column": 6, "nodeType": "630", "endLine": 83, "endColumn": 113, "suggestions": "648"}, {"ruleId": "628", "severity": 1, "message": "649", "line": 470, "column": 6, "nodeType": "630", "endLine": 470, "endColumn": 8, "suggestions": "650"}, {"ruleId": "628", "severity": 1, "message": "651", "line": 576, "column": 6, "nodeType": "630", "endLine": 579, "endColumn": 4, "suggestions": "652"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'cachedServerKey', 'getServerKey', and 'newServer'. Either include them or remove the dependency array.", "ArrayExpression", ["653"], "React Hook useEffect has a missing dependency: 'initializeToolConfig'. Either include it or remove the dependency array.", ["654"], ["655"], "React Hook useEffect has a missing dependency: 'loadModels'. Either include it or remove the dependency array.", ["656"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'messages.length'. Either include it or remove the dependency array.", ["657"], "React Hook useEffect has missing dependencies: 'selectBestModel' and 'selectedModel'. Either include them or remove the dependency array.", ["658"], ["659"], "React Hook useEffect has missing dependencies: 'currentConversation', 'loadConversationMessages', 'setMessages', and 'setToolCalls'. Either include them or remove the dependency array. If 'setMessages' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["660"], "React Hook useEffect has a missing dependency: 'currentConversation'. Either include it or remove the dependency array.", ["661"], "React Hook useCallback has a missing dependency: 'currentConversation'. Either include it or remove the dependency array.", ["662"], "React Hook useCallback has missing dependencies: 'setActiveTool', 'setCurrentAssistantMessageId', 'setInputMessage', 'setIsStreaming', 'setMessages', and 'setToolCalls'. Either include them or remove the dependency array.", ["663"], {"desc": "664", "fix": "665"}, {"desc": "666", "fix": "667"}, {"kind": "668", "justification": "669"}, {"desc": "670", "fix": "671"}, {"desc": "672", "fix": "673"}, {"desc": "674", "fix": "675"}, {"kind": "668", "justification": "669"}, {"desc": "676", "fix": "677"}, {"desc": "678", "fix": "679"}, {"desc": "680", "fix": "681"}, {"desc": "682", "fix": "683"}, "Update the dependencies array to be: [newServer.type, newServer.url, newServer.base_url, newServer.command, newServer.args, getServerKey, newServer, cachedServerKey]", {"range": "684", "text": "685"}, "Update the dependencies array to be: [initializeToolConfig, serverName, tools]", {"range": "686", "text": "687"}, "directive", "", "Update the dependencies array to be: [loadModels]", {"range": "688", "text": "689"}, "Update the dependencies array to be: [messages.length]", {"range": "690", "text": "691"}, "Update the dependencies array to be: [selectBestModel, selectedModel]", {"range": "692", "text": "693"}, "Update the dependencies array to be: [currentConversation, currentConversation.id, loadConversationMessages, models, selectBestModel, setMessages, setToolCalls]", {"range": "694", "text": "695"}, "Update the dependencies array to be: [searchParams, models.length, selectedModel, currentConversation.id, conversationLoading, isProcessingUrl, currentConversation]", {"range": "696", "text": "697"}, "Update the dependencies array to be: [currentConversation]", {"range": "698", "text": "699"}, "Update the dependencies array to be: [inputMessage, selectedModel, isStreaming, currentConversation, setMessages, setInputMessage, setIsStreaming, setToolCalls, setActiveTool, setCurrentAssistantMessageId, createConversation, setAbortController, enableTools, selectedTools, createStreamHandlers]", {"range": "700", "text": "701"}, [2093, 2179], "[newServer.type, newServer.url, newServer.base_url, newServer.command, newServer.args, getServerKey, newServer, cachedServerKey]", [1140, 1159], "[initializeToolConfig, serverName, tools]", [10347, 10349], "[loadModels]", [7022, 7024], "[messages.length]", [6247, 6249], "[selectBestModel, selectedModel]", [4914, 4964], "[currentConversation, currentConversation.id, loadConversationMessages, models, selectBestModel, setMessages, setToolCalls]", [2730, 2837], "[searchParams, models.length, selectedModel, currentConversation.id, conversationLoading, isProcessingUrl, currentConversation]", [16517, 16519], "[currentConversation]", [19570, 19749], "[inputMessage, selectedModel, isStreaming, currentConversation, setMessages, setInputMessage, setIsStreaming, setToolCalls, setActiveTool, setCurrentAssistantMessageId, createConversation, setAbortController, enableTools, selectedTools, createStreamHandlers]"]