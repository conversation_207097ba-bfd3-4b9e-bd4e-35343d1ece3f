# 本地MCP服务器工具注册脚本

本目录包含用于将本地MCP服务器工具注册到数据库的脚本。

## 文件说明

### 脚本文件

1. **register-local-tools.mjs** - 主要的注册脚本（推荐使用）
   - 使用ES模块语法
   - 自动检查并创建本地服务器记录
   - 注册5个基础工具到数据库

2. **register-local-tools-simple.js** - CommonJS版本（备用）
   - 使用CommonJS语法
   - 功能与.mjs版本相同

3. **register-local-tools.js** - 完整版本（高级用户）
   - 包含实际连接MCP服务器的逻辑
   - 需要MCP服务器正在运行

4. **check-tools.mjs** - 验证脚本
   - 查询数据库中已注册的本地工具
   - 用于验证注册是否成功

## 使用方法

### 1. 注册本地工具

```bash
# 使用推荐的ES模块版本
node register-local-tools.mjs
```

### 2. 验证注册结果

```bash
# 查看已注册的工具
node check-tools.mjs
```

## 注册的工具列表

脚本会注册以下5个本地MCP服务器工具：

1. **get_current_time** - 获取当前时间
2. **calculate** - 执行数学计算
3. **read_file** - 读取文件内容
4. **write_file** - 写入文件内容
5. **list_directory** - 列出目录内容

## 数据库结构

脚本会在以下表中创建记录：

- **mcp_servers** - 服务器信息
  - 创建名为 'local' 的服务器记录
  - 类型为 'stdio'
  - 状态设置为 'connected'

- **mcp_tools** - 工具信息
  - 每个工具包含名称、描述和输入模式
  - 所有工具默认启用
  - 关联到本地服务器记录

## 注意事项

1. 确保数据库文件 `chat.db` 存在于项目根目录
2. 如果本地服务器记录已存在，脚本会更新现有工具而不是重复创建
3. 脚本使用 `better-sqlite3` 模块，确保已安装此依赖
4. 运行前请确保没有其他进程正在使用数据库文件

## 故障排除

### 模块导入错误
如果遇到模块导入错误，请检查：
- 项目的 `package.json` 中 `"type": "module"` 配置
- 使用正确的文件扩展名（.mjs 或 .js）

### 数据库访问错误
如果遇到数据库访问错误，请检查：
- 数据库文件路径是否正确
- 是否有足够的文件系统权限
- 数据库文件是否被其他进程锁定

### 依赖缺失
如果提示缺少 `better-sqlite3`：
```bash
npm install better-sqlite3
```