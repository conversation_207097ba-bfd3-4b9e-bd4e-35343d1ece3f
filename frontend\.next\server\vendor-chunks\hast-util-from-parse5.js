"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-parse5";
exports.ids = ["vendor-chunks/hast-util-from-parse5"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-parse5/lib/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromParse5: () => (/* binding */ fromParse5)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var vfile_location__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile-location */ \"(ssr)/./node_modules/vfile-location/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/**\n * @import {ElementData, Element, Nodes, RootContent, Root} from 'hast'\n * @import {DefaultTreeAdapterMap, Token} from 'parse5'\n * @import {Schema} from 'property-information'\n * @import {Point, Position} from 'unist'\n * @import {VFile} from 'vfile'\n * @import {Options} from 'hast-util-from-parse5'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean} location\n *   Whether location info was found.\n * @property {Schema} schema\n *   Current schema.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n */\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {DefaultTreeAdapterMap['node']} tree\n *   `parse5` tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   hast tree.\n */\nfunction fromParse5(tree, options) {\n  const settings = options || {}\n\n  return one(\n    {\n      file: settings.file || undefined,\n      location: false,\n      schema: settings.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html,\n      verbose: settings.verbose || false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} node\n *   p5 node.\n * @returns {Nodes}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Nodes} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {DefaultTreeAdapterMap['commentNode']} */ (\n        node\n      )\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference =\n        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */ (\n          node\n        )\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const document = String(state.file)\n        const loc = (0,vfile_location__WEBPACK_IMPORTED_MODULE_1__.location)(document)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(document.length)\n        // Always defined as we give valid input.\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start, 'expected `start`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(end, 'expected `end`')\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {DefaultTreeAdapterMap['documentType']} */ (\n        node\n      )\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {DefaultTreeAdapterMap['textNode']} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {DefaultTreeAdapterMap['element']} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<DefaultTreeAdapterMap['node']>} nodes\n *   Nodes.\n * @returns {Array<RootContent>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<RootContent>} */\n  const results = []\n\n  while (++index < nodes.length) {\n    // Assume no roots in `nodes`.\n    const result = /** @type {RootContent} */ (one(state, nodes[index]))\n    results.push(result)\n  }\n\n  return results\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['element']} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces.svg ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const properties = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      properties[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const x = state.schema.space === 'svg' ? hastscript__WEBPACK_IMPORTED_MODULE_4__.s : hastscript__WEBPACK_IMPORTED_MODULE_4__.h\n  const result = x(node.tagName, properties, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {DefaultTreeAdapterMap['template']} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    // Root in, root out.\n    const content = /** @type {Root} */ (one(state, reference.content))\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} from\n *   p5 node.\n * @param {Nodes} to\n *   hast node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node.\n * @param {Token.ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const properties = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            properties[(0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(location.startTag, 'a start tag should exist')\n      const opening = position(location.startTag)\n      const closing = location.endTag ? position(location.endTag) : undefined\n      /** @type {ElementData['position']} */\n      const data = {opening}\n      if (closing) data.closing = closing\n      data.properties = properties\n\n      node.data = {position: data}\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {Token.Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n\n  // @ts-expect-error: we do use `undefined` for points if one or the other\n  // exists.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-parse5/node_modules/hast-util-parse-selector/lib/index.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/node_modules/hast-util-parse-selector/lib/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseSelector: () => (/* binding */ parseSelector)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n */\n\n/**\n * @template {string} SimpleSelector\n *   Selector type.\n * @template {string} DefaultTagName\n *   Default tag name.\n * @typedef {(\n *   SimpleSelector extends ''\n *     ? DefaultTagName\n *     : SimpleSelector extends `${infer TagName}.${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends `${infer TagName}#${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends string\n *     ? SimpleSelector\n *     : DefaultTagName\n * )} ExtractTagName\n *   Extract tag name from a simple selector.\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name (default: `'div'`).\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector (optional).\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nfunction parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: tag name is parsed.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/node_modules/hast-util-parse-selector/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/create-h.js":
/*!************************************************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/create-h.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createH: () => (/* binding */ createH)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-from-parse5/node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @import {Element, Nodes, RootContent, Root} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n/**\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n */\n\n/**\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n */\n\n/**\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n */\n\n/**\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n/**\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n */\n\n/**\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n */\n\n/**\n * @typedef {Record<string, PropertyValue | Style>} Properties\n *   Acceptable value for element properties.\n */\n\n/**\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n */\n\n/**\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n */\n\n/**\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n */\n\n\n\n\n\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {ReadonlyArray<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nfunction createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : undefined\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    /** @type {Result} */\n    let node\n\n    if (selector === null || selector === undefined) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n      // Normalize the name.\n      const lower = node.tagName.toLowerCase()\n      const adjusted = adjust ? adjust.get(lower) : undefined\n      node.tagName = adjusted || lower\n\n      // Handle properties.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        for (const [key, value] of Object.entries(properties)) {\n          addProperty(schema, node.properties, key, value)\n        }\n      }\n    }\n\n    // Handle children.\n    for (const child of children) {\n      addChild(node.children, child)\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {ReadonlyArray<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === null || value === undefined) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = [...value]\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    for (const item of result) {\n      // Assume no booleans in array.\n      finalResult.push(\n        /** @type {number | string} */ (\n          parsePrimitive(info, info.property, item)\n        )\n      )\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    result = properties.className.concat(\n      /** @type {Array<number | string> | number | string} */ (result)\n    )\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'number' || typeof value === 'string') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    for (const child of value) {\n      addChild(nodes, child)\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} styles\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(styles) {\n  /** @type {Array<string>} */\n  const result = []\n\n  for (const [key, value] of Object.entries(styles)) {\n    result.push([key, value].join(': '))\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {ReadonlyArray<string>} values\n *   List of properly cased keys.\n * @returns {Map<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Map<string, string>} */\n  const result = new Map()\n\n  for (const value of values) {\n    result.set(value.toLowerCase(), value)\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/create-h.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: () => (/* binding */ h),\n/* harmony export */   s: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _create_h_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-h.js */ \"(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/create-h.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\n\n\n\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst h = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst s = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: () => (/* binding */ svgCaseSensitiveTagNames)\n/* harmony export */ });\n/**\n * List of case-sensitive SVG tag names.\n *\n * @type {ReadonlyArray<string>}\n */\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWZyb20tcGFyc2U1L25vZGVfbW9kdWxlcy9oYXN0c2NyaXB0L2xpYi9zdmctY2FzZS1zZW5zaXRpdmUtdGFnLW5hbWVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC1mcm9tLXBhcnNlNVxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxsaWJcXHN2Zy1jYXNlLXNlbnNpdGl2ZS10YWctbmFtZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBMaXN0IG9mIGNhc2Utc2Vuc2l0aXZlIFNWRyB0YWcgbmFtZXMuXG4gKlxuICogQHR5cGUge1JlYWRvbmx5QXJyYXk8c3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IHN2Z0Nhc2VTZW5zaXRpdmVUYWdOYW1lcyA9IFtcbiAgJ2FsdEdseXBoJyxcbiAgJ2FsdEdseXBoRGVmJyxcbiAgJ2FsdEdseXBoSXRlbScsXG4gICdhbmltYXRlQ29sb3InLFxuICAnYW5pbWF0ZU1vdGlvbicsXG4gICdhbmltYXRlVHJhbnNmb3JtJyxcbiAgJ2NsaXBQYXRoJyxcbiAgJ2ZlQmxlbmQnLFxuICAnZmVDb2xvck1hdHJpeCcsXG4gICdmZUNvbXBvbmVudFRyYW5zZmVyJyxcbiAgJ2ZlQ29tcG9zaXRlJyxcbiAgJ2ZlQ29udm9sdmVNYXRyaXgnLFxuICAnZmVEaWZmdXNlTGlnaHRpbmcnLFxuICAnZmVEaXNwbGFjZW1lbnRNYXAnLFxuICAnZmVEaXN0YW50TGlnaHQnLFxuICAnZmVEcm9wU2hhZG93JyxcbiAgJ2ZlRmxvb2QnLFxuICAnZmVGdW5jQScsXG4gICdmZUZ1bmNCJyxcbiAgJ2ZlRnVuY0cnLFxuICAnZmVGdW5jUicsXG4gICdmZUdhdXNzaWFuQmx1cicsXG4gICdmZUltYWdlJyxcbiAgJ2ZlTWVyZ2UnLFxuICAnZmVNZXJnZU5vZGUnLFxuICAnZmVNb3JwaG9sb2d5JyxcbiAgJ2ZlT2Zmc2V0JyxcbiAgJ2ZlUG9pbnRMaWdodCcsXG4gICdmZVNwZWN1bGFyTGlnaHRpbmcnLFxuICAnZmVTcG90TGlnaHQnLFxuICAnZmVUaWxlJyxcbiAgJ2ZlVHVyYnVsZW5jZScsXG4gICdmb3JlaWduT2JqZWN0JyxcbiAgJ2dseXBoUmVmJyxcbiAgJ2xpbmVhckdyYWRpZW50JyxcbiAgJ3JhZGlhbEdyYWRpZW50JyxcbiAgJ3NvbGlkQ29sb3InLFxuICAndGV4dEFyZWEnLFxuICAndGV4dFBhdGgnXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n");

/***/ })

};
;