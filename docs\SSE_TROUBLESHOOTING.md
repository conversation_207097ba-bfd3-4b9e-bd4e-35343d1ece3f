# SSE MCP连接故障排除指南

## 412 Precondition Failed 错误

### 问题描述
当连接到SSE MCP服务器时出现412状态码错误，这通常表示"前置条件失败"。

### 常见原因

1. **缺少必要的请求头**
   - MCP SSE协议要求特定的HTTP头
   - 缺少`Accept: text/event-stream`头
   - 缺少`Content-Type: application/json`头

2. **会话管理问题**
   - 服务器要求`Mcp-Session-Id`头
   - 会话ID格式不正确或已过期

3. **认证问题**
   - 服务器要求认证但客户端未提供
   - 认证令牌格式不正确或已过期

4. **CORS配置问题**
   - 服务器CORS设置不允许来自客户端域的请求
   - 预检请求失败

5. **协议版本不匹配**
   - 客户端和服务器使用不同版本的MCP协议
   - 服务器不支持当前的SSE传输实现

### 解决方案

#### VPN环境问题（新增）

如果您在使用VPN时遇到412或422错误：

**快速解决方案**
1. **关闭VPN重试**: 最简单的方法是暂时关闭VPN后重新连接
2. **启用VPN兼容模式**: 在配置中添加 `"vpnCompatible": true`
3. **切换VPN节点**: 尝试使用不同的VPN服务器节点

**配置示例**
```json
{
  "name": "my-sse-server",
  "url": "https://api.example.com/sse",
  "type": "sse",
  "vpnCompatible": true,
  "timeout": 30000,
  "retryAttempts": 5
}
```

详细的VPN兼容性配置请参考 [VPN兼容性指南](./VPN_COMPATIBILITY_GUIDE.md)。

#### 1. 检查服务器状态和网络连接

```bash
# 使用curl测试服务器是否响应
curl -I https://mcp.api-inference.modelscope.net/23542ef8da164f/sse

# 测试网络连接
ping mcp.api-inference.modelscope.net

# 测试DNS解析
nslookup mcp.api-inference.modelscope.net
```

**常见网络问题：**
- 防火墙阻止HTTPS连接
- 代理服务器配置问题
- DNS解析失败
- 网络连接不稳定
- 服务器暂时不可用

#### 2. 验证请求头
确保客户端发送正确的请求头：
```javascript
headers: {
  'Accept': 'text/event-stream',
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive',
  'Content-Type': 'application/json',
  'User-Agent': 'kun-agent-sse-client'
}
```

#### 3. 检查网络连接
- 确保网络连接正常
- 检查防火墙设置
- 验证代理配置

#### 4. 服务器配置检查
如果你控制服务器，检查以下配置：

```javascript
// 正确的CORS配置
cors: {
  allowOrigin: "*",
  allowMethods: "GET, POST, OPTIONS",
  allowHeaders: "Content-Type, Authorization, x-api-key",
  exposeHeaders: "Content-Type, Authorization, x-api-key",
  maxAge: "86400"
}
```

#### 5. 使用测试脚本
运行提供的测试脚本来诊断问题：
```bash
# 测试默认URL
node test-sse-connection.js

# 测试自定义URL
node test-sse-connection.js https://your-mcp-server.com/sse
```

测试脚本会执行以下检查：
- HTTP连接测试
- SSE连接测试
- 请求头验证
- 响应状态分析

### 最新修复内容 (2024)

本次修复包括以下改进：

1. **增强的请求头设置**
   - 添加了所有必要的SSE请求头
   - 改进了User-Agent设置
   - 添加了MCP协议版本头
   - 支持自定义请求头配置

2. **更好的错误处理**
   - 专门处理412错误
   - 提供详细的错误诊断信息
   - 避免对配置问题进行无效重试
   - 增加了更多错误原因分析

3. **详细的日志记录**
   - 更清晰的错误消息
   - 故障排除建议
   - 连接状态跟踪
   - 显示当前请求头信息

4. **配置增强**
   - 支持自定义超时时间
   - 支持自定义重试次数
   - 支持自定义MCP协议版本
   - 支持自定义请求头

5. **诊断工具**
   - 新增SSE连接测试脚本
   - 自动化连接诊断
   - 详细的测试报告

### 预防措施

1. **定期测试连接**
   - 使用测试脚本定期验证连接
   - 监控服务器状态

2. **保持SDK更新**
   - 定期更新MCP SDK到最新版本
   - 关注协议变更

3. **配置验证**
   - 在生产环境中验证所有配置
   - 测试不同网络环境下的连接

### 相关资源

- [MCP协议文档](https://modelcontextprotocol.io/docs/concepts/transports)
- [SSE传输规范](https://modelcontextprotocol.io/specification/2025-03-26/basic/transports)
- [MCP Framework SSE文档](https://mcp-framework.com/docs/Transports/sse/)

### 联系支持

如果问题仍然存在，请提供以下信息：
- 完整的错误日志
- 服务器URL和配置
- 网络环境信息
- 使用的MCP SDK版本