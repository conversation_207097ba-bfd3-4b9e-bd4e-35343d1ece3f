"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/components/chat/ChatContainer.tsx":
/*!***************************************************************!*\
  !*** ./src/app/simple-chat/components/chat/ChatContainer.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatContainer: () => (/* binding */ ChatContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _conversation_ChatHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../conversation/ChatHeader */ \"(app-pages-browser)/./src/app/simple-chat/components/conversation/ChatHeader.tsx\");\n/* harmony import */ var _MessageList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageList */ \"(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageInput */ \"(app-pages-browser)/./src/app/simple-chat/components/chat/MessageInput.tsx\");\n/* harmony import */ var _EmptyState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EmptyState */ \"(app-pages-browser)/./src/app/simple-chat/components/chat/EmptyState.tsx\");\n/* harmony import */ var _ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/ErrorDisplay */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/ErrorDisplay.tsx\");\n/* harmony import */ var _tools_ToolSettings__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../tools/ToolSettings */ \"(app-pages-browser)/./src/app/simple-chat/components/tools/ToolSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatContainer auto */ \n\n\n\n\n\n\n\nfunction ChatContainer(param) {\n    let { currentConversation, models, selectedModel, onModelChange, agents, selectedAgentId, onAgentChange, selectorMode, onSelectorModeChange, messages, inputMessage, onInputChange, onSendMessage, isStreaming, onStopGeneration, expandedThinkingMessages, onToggleThinkingExpand, enableTools, selectedTools, onToolsToggle, onSelectedToolsChange, onInsertText, onClearChat, error, onDismissError, chatStyle, displaySize, onChatStyleChange, onDisplaySizeChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col min-w-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_conversation_ChatHeader__WEBPACK_IMPORTED_MODULE_2__.ChatHeader, {\n                currentConversation: currentConversation,\n                models: models,\n                selectedModel: selectedModel,\n                onModelChange: onModelChange,\n                agents: agents,\n                selectedAgentId: selectedAgentId,\n                onAgentChange: onAgentChange,\n                selectorMode: selectorMode,\n                onSelectorModeChange: onSelectorModeChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden relative\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center px-4 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EmptyState__WEBPACK_IMPORTED_MODULE_5__.EmptyState, {\n                        currentConversation: currentConversation\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 overflow-y-auto scrollbar-thin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center min-h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full chat-container-responsive \".concat(displaySize || 'compact'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageList__WEBPACK_IMPORTED_MODULE_3__.MessageList, {\n                                messages: messages,\n                                isStreaming: isStreaming,\n                                expandedThinkingMessages: expandedThinkingMessages,\n                                onToggleThinkingExpand: onToggleThinkingExpand,\n                                chatStyle: chatStyle,\n                                selectedModel: selectedModel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 mb-4 chat-container-responsive \".concat(displaySize || 'compact'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__.ErrorDisplay, {\n                        message: error,\n                        onDismiss: onDismissError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full p-4 chat-container-responsive \".concat(displaySize || 'compact'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_ToolSettings__WEBPACK_IMPORTED_MODULE_7__.ToolSettings, {\n                                selectedModel: selectedModel,\n                                enableTools: enableTools,\n                                selectedTools: selectedTools,\n                                onToolsToggle: onToolsToggle,\n                                onSelectedToolsChange: onSelectedToolsChange,\n                                onInsertText: onInsertText,\n                                onClearChat: onClearChat,\n                                chatStyle: chatStyle,\n                                displaySize: displaySize,\n                                onChatStyleChange: onChatStyleChange,\n                                onDisplaySizeChange: onDisplaySizeChange\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_4__.MessageInput, {\n                                inputMessage: inputMessage,\n                                onInputChange: onInputChange,\n                                onSendMessage: onSendMessage,\n                                onStopGeneration: onStopGeneration,\n                                isStreaming: isStreaming,\n                                currentConversation: currentConversation,\n                                selectedModel: selectedModel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_c = ChatContainer;\nvar _c;\n$RefreshReg$(_c, \"ChatContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/components/chat/ChatContainer.tsx\n"));

/***/ })

});