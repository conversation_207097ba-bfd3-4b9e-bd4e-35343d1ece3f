"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/factory.js":
/*!********************************************!*\
  !*** ./node_modules/hastscript/factory.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar find = __webpack_require__(/*! property-information/find */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/find.js\")\nvar normalize = __webpack_require__(/*! property-information/normalize */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\")\nvar parseSelector = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/index.js\")\nvar spaces = (__webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/hastscript/node_modules/space-separated-tokens/index.js\").parse)\nvar commas = (__webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/hastscript/node_modules/comma-separated-tokens/index.js\").parse)\n\nmodule.exports = factory\n\nvar own = {}.hasOwnProperty\n\nfunction factory(schema, defaultTagName, caseSensitive) {\n  var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null\n\n  return h\n\n  // Hyperscript compatible DSL for creating virtual hast trees.\n  function h(selector, properties) {\n    var node = parseSelector(selector, defaultTagName)\n    var children = Array.prototype.slice.call(arguments, 2)\n    var name = node.tagName.toLowerCase()\n    var property\n\n    node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name\n\n    if (properties && isChildren(properties, node)) {\n      children.unshift(properties)\n      properties = null\n    }\n\n    if (properties) {\n      for (property in properties) {\n        addProperty(node.properties, property, properties[property])\n      }\n    }\n\n    addChild(node.children, children)\n\n    if (node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  function addProperty(properties, key, value) {\n    var info\n    var property\n    var result\n\n    // Ignore nullish and NaN values.\n    if (value === null || value === undefined || value !== value) {\n      return\n    }\n\n    info = find(schema, key)\n    property = info.property\n    result = value\n\n    // Handle list values.\n    if (typeof result === 'string') {\n      if (info.spaceSeparated) {\n        result = spaces(result)\n      } else if (info.commaSeparated) {\n        result = commas(result)\n      } else if (info.commaOrSpaceSeparated) {\n        result = spaces(commas(result).join(' '))\n      }\n    }\n\n    // Accept `object` on style.\n    if (property === 'style' && typeof value !== 'string') {\n      result = style(result)\n    }\n\n    // Class-names (which can be added both on the `selector` and here).\n    if (property === 'className' && properties.className) {\n      result = properties.className.concat(result)\n    }\n\n    properties[property] = parsePrimitives(info, property, result)\n  }\n}\n\nfunction isChildren(value, node) {\n  return (\n    typeof value === 'string' ||\n    'length' in value ||\n    isNode(node.tagName, value)\n  )\n}\n\nfunction isNode(tagName, value) {\n  var type = value.type\n\n  if (tagName === 'input' || !type || typeof type !== 'string') {\n    return false\n  }\n\n  if (typeof value.children === 'object' && 'length' in value.children) {\n    return true\n  }\n\n  type = type.toLowerCase()\n\n  if (tagName === 'button') {\n    return (\n      type !== 'menu' &&\n      type !== 'submit' &&\n      type !== 'reset' &&\n      type !== 'button'\n    )\n  }\n\n  return 'value' in value\n}\n\nfunction addChild(nodes, value) {\n  var index\n  var length\n\n  if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n    return\n  }\n\n  if (typeof value === 'object' && 'length' in value) {\n    index = -1\n    length = value.length\n\n    while (++index < length) {\n      addChild(nodes, value[index])\n    }\n\n    return\n  }\n\n  if (typeof value !== 'object' || !('type' in value)) {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n\n  nodes.push(value)\n}\n\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n  var index\n  var length\n  var result\n\n  if (typeof value !== 'object' || !('length' in value)) {\n    return parsePrimitive(info, name, value)\n  }\n\n  length = value.length\n  index = -1\n  result = []\n\n  while (++index < length) {\n    result[index] = parsePrimitive(info, name, value[index])\n  }\n\n  return result\n}\n\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n  var result = value\n\n  if (info.number || info.positiveNumber) {\n    if (!isNaN(result) && result !== '') {\n      result = Number(result)\n    }\n  } else if (info.boolean || info.overloadedBoolean) {\n    // Accept `boolean` and `string`.\n    if (\n      typeof result === 'string' &&\n      (result === '' || normalize(value) === normalize(name))\n    ) {\n      result = true\n    }\n  }\n\n  return result\n}\n\nfunction style(value) {\n  var result = []\n  var key\n\n  for (key in value) {\n    result.push([key, value[key]].join(': '))\n  }\n\n  return result.join('; ')\n}\n\nfunction createAdjustMap(values) {\n  var length = values.length\n  var index = -1\n  var result = {}\n  var value\n\n  while (++index < length) {\n    value = values[index]\n    result[value.toLowerCase()] = value\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/html.js":
/*!*****************************************!*\
  !*** ./node_modules/hastscript/html.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar schema = __webpack_require__(/*! property-information/html */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/html.js\")\nvar factory = __webpack_require__(/*! ./factory */ \"(ssr)/./node_modules/hastscript/factory.js\")\n\nvar html = factory(schema, 'div')\nhtml.displayName = 'html'\n\nmodule.exports = html\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9odG1sLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyw0R0FBMkI7QUFDaEQsY0FBYyxtQkFBTyxDQUFDLDZEQUFXOztBQUVqQztBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXGh0bWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBzY2hlbWEgPSByZXF1aXJlKCdwcm9wZXJ0eS1pbmZvcm1hdGlvbi9odG1sJylcbnZhciBmYWN0b3J5ID0gcmVxdWlyZSgnLi9mYWN0b3J5JylcblxudmFyIGh0bWwgPSBmYWN0b3J5KHNjaGVtYSwgJ2RpdicpXG5odG1sLmRpc3BsYXlOYW1lID0gJ2h0bWwnXG5cbm1vZHVsZS5leHBvcnRzID0gaHRtbFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/index.js":
/*!******************************************!*\
  !*** ./node_modules/hastscript/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/hastscript/html.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWiw2RkFBa0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9odG1sJylcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/comma-separated-tokens/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/comma-separated-tokens/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar comma = ','\nvar space = ' '\nvar empty = ''\n\n// Parse comma-separated tokens to an array.\nfunction parse(value) {\n  var values = []\n  var input = String(value || empty)\n  var index = input.indexOf(comma)\n  var lastIndex = 0\n  var end = false\n  var val\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    val = input.slice(lastIndex, index).trim()\n\n    if (val || !end) {\n      values.push(val)\n    }\n\n    lastIndex = index + 1\n    index = input.indexOf(comma, lastIndex)\n  }\n\n  return values\n}\n\n// Compile an array to comma-separated tokens.\n// `options.padLeft` (default: `true`) pads a space left of each token, and\n// `options.padRight` (default: `false`) pads a space to the right of each token.\nfunction stringify(values, options) {\n  var settings = options || {}\n  var left = settings.padLeft === false ? empty : space\n  var right = settings.padRight ? space : empty\n\n  // Ensure the last empty entry is seen.\n  if (values[values.length - 1] === empty) {\n    values = values.concat(empty)\n  }\n\n  return values.join(right + comma + left).trim()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvY29tbWEtc2VwYXJhdGVkLXRva2Vucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixhQUFhO0FBQ2IsaUJBQWlCOztBQUVqQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xcY29tbWEtc2VwYXJhdGVkLXRva2Vuc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmV4cG9ydHMucGFyc2UgPSBwYXJzZVxuZXhwb3J0cy5zdHJpbmdpZnkgPSBzdHJpbmdpZnlcblxudmFyIGNvbW1hID0gJywnXG52YXIgc3BhY2UgPSAnICdcbnZhciBlbXB0eSA9ICcnXG5cbi8vIFBhcnNlIGNvbW1hLXNlcGFyYXRlZCB0b2tlbnMgdG8gYW4gYXJyYXkuXG5mdW5jdGlvbiBwYXJzZSh2YWx1ZSkge1xuICB2YXIgdmFsdWVzID0gW11cbiAgdmFyIGlucHV0ID0gU3RyaW5nKHZhbHVlIHx8IGVtcHR5KVxuICB2YXIgaW5kZXggPSBpbnB1dC5pbmRleE9mKGNvbW1hKVxuICB2YXIgbGFzdEluZGV4ID0gMFxuICB2YXIgZW5kID0gZmFsc2VcbiAgdmFyIHZhbFxuXG4gIHdoaWxlICghZW5kKSB7XG4gICAgaWYgKGluZGV4ID09PSAtMSkge1xuICAgICAgaW5kZXggPSBpbnB1dC5sZW5ndGhcbiAgICAgIGVuZCA9IHRydWVcbiAgICB9XG5cbiAgICB2YWwgPSBpbnB1dC5zbGljZShsYXN0SW5kZXgsIGluZGV4KS50cmltKClcblxuICAgIGlmICh2YWwgfHwgIWVuZCkge1xuICAgICAgdmFsdWVzLnB1c2godmFsKVxuICAgIH1cblxuICAgIGxhc3RJbmRleCA9IGluZGV4ICsgMVxuICAgIGluZGV4ID0gaW5wdXQuaW5kZXhPZihjb21tYSwgbGFzdEluZGV4KVxuICB9XG5cbiAgcmV0dXJuIHZhbHVlc1xufVxuXG4vLyBDb21waWxlIGFuIGFycmF5IHRvIGNvbW1hLXNlcGFyYXRlZCB0b2tlbnMuXG4vLyBgb3B0aW9ucy5wYWRMZWZ0YCAoZGVmYXVsdDogYHRydWVgKSBwYWRzIGEgc3BhY2UgbGVmdCBvZiBlYWNoIHRva2VuLCBhbmRcbi8vIGBvcHRpb25zLnBhZFJpZ2h0YCAoZGVmYXVsdDogYGZhbHNlYCkgcGFkcyBhIHNwYWNlIHRvIHRoZSByaWdodCBvZiBlYWNoIHRva2VuLlxuZnVuY3Rpb24gc3RyaW5naWZ5KHZhbHVlcywgb3B0aW9ucykge1xuICB2YXIgc2V0dGluZ3MgPSBvcHRpb25zIHx8IHt9XG4gIHZhciBsZWZ0ID0gc2V0dGluZ3MucGFkTGVmdCA9PT0gZmFsc2UgPyBlbXB0eSA6IHNwYWNlXG4gIHZhciByaWdodCA9IHNldHRpbmdzLnBhZFJpZ2h0ID8gc3BhY2UgOiBlbXB0eVxuXG4gIC8vIEVuc3VyZSB0aGUgbGFzdCBlbXB0eSBlbnRyeSBpcyBzZWVuLlxuICBpZiAodmFsdWVzW3ZhbHVlcy5sZW5ndGggLSAxXSA9PT0gZW1wdHkpIHtcbiAgICB2YWx1ZXMgPSB2YWx1ZXMuY29uY2F0KGVtcHR5KVxuICB9XG5cbiAgcmV0dXJuIHZhbHVlcy5qb2luKHJpZ2h0ICsgY29tbWEgKyBsZWZ0KS50cmltKClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/comma-separated-tokens/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/find.js":
/*!***************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/find.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! ./normalize */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\")\nvar DefinedInfo = __webpack_require__(/*! ./lib/util/defined-info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js\")\nvar Info = __webpack_require__(/*! ./lib/util/info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js\")\n\nvar data = 'data'\n\nmodule.exports = find\n\nvar valid = /^data[-\\w.:]+$/i\nvar dash = /-[a-z]/g\nvar cap = /[A-Z]/g\n\nfunction find(schema, value) {\n  var normal = normalize(value)\n  var prop = value\n  var Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      prop = datasetToProperty(value)\n    } else {\n      value = datasetToAttribute(value)\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\nfunction datasetToProperty(attribute) {\n  var value = attribute.slice(5).replace(dash, camelcase)\n  return data + value.charAt(0).toUpperCase() + value.slice(1)\n}\n\nfunction datasetToAttribute(property) {\n  var value = property.slice(4)\n\n  if (dash.test(value)) {\n    return property\n  }\n\n  value = value.replace(cap, kebab)\n\n  if (value.charAt(0) !== '-') {\n    value = '-' + value\n  }\n\n  return data + value\n}\n\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/html.js":
/*!***************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/html.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar merge = __webpack_require__(/*! ./lib/util/merge */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/merge.js\")\nvar xlink = __webpack_require__(/*! ./lib/xlink */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xlink.js\")\nvar xml = __webpack_require__(/*! ./lib/xml */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xml.js\")\nvar xmlns = __webpack_require__(/*! ./lib/xmlns */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xmlns.js\")\nvar aria = __webpack_require__(/*! ./lib/aria */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/aria.js\")\nvar html = __webpack_require__(/*! ./lib/html */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/html.js\")\n\nmodule.exports = merge([xml, xlink, xmlns, aria, html])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaHRtbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixZQUFZLG1CQUFPLENBQUMsNkdBQWtCO0FBQ3RDLFlBQVksbUJBQU8sQ0FBQyxtR0FBYTtBQUNqQyxVQUFVLG1CQUFPLENBQUMsK0ZBQVc7QUFDN0IsWUFBWSxtQkFBTyxDQUFDLG1HQUFhO0FBQ2pDLFdBQVcsbUJBQU8sQ0FBQyxpR0FBWTtBQUMvQixXQUFXLG1CQUFPLENBQUMsaUdBQVk7O0FBRS9CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGh0bWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBtZXJnZSA9IHJlcXVpcmUoJy4vbGliL3V0aWwvbWVyZ2UnKVxudmFyIHhsaW5rID0gcmVxdWlyZSgnLi9saWIveGxpbmsnKVxudmFyIHhtbCA9IHJlcXVpcmUoJy4vbGliL3htbCcpXG52YXIgeG1sbnMgPSByZXF1aXJlKCcuL2xpYi94bWxucycpXG52YXIgYXJpYSA9IHJlcXVpcmUoJy4vbGliL2FyaWEnKVxudmFyIGh0bWwgPSByZXF1aXJlKCcuL2xpYi9odG1sJylcblxubW9kdWxlLmV4cG9ydHMgPSBtZXJnZShbeG1sLCB4bGluaywgeG1sbnMsIGFyaWEsIGh0bWxdKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/aria.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/aria.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\n\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\n\nmodule.exports = create({\n  transform: ariaTransform,\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n\nfunction ariaTransform(_, prop) {\n  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/html.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/html.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\nvar caseInsensitiveTransform = __webpack_require__(/*! ./util/case-insensitive-transform */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js\")\n\nvar boolean = types.boolean\nvar overloadedBoolean = types.overloadedBoolean\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\nvar commaSeparated = types.commaSeparated\n\nmodule.exports = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    capture: boolean,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: commaSeparated,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforePrint: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextMenu: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: commaSeparated,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar caseSensitiveTransform = __webpack_require__(/*! ./case-sensitive-transform */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js\")\n\nmodule.exports = caseInsensitiveTransform\n\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosNkJBQTZCLG1CQUFPLENBQUMsMElBQTRCOztBQUVqRTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFxjYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0gPSByZXF1aXJlKCcuL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybScpXG5cbm1vZHVsZS5leHBvcnRzID0gY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtXG5cbmZ1bmN0aW9uIGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wZXJ0eSkge1xuICByZXR1cm4gY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wZXJ0eS50b0xvd2VyQ2FzZSgpKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = caseSensitiveTransform\n\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXGNhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtXG5cbmZ1bmN0aW9uIGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm0oYXR0cmlidXRlcywgYXR0cmlidXRlKSB7XG4gIHJldHVybiBhdHRyaWJ1dGUgaW4gYXR0cmlidXRlcyA/IGF0dHJpYnV0ZXNbYXR0cmlidXRlXSA6IGF0dHJpYnV0ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/create.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! ../../normalize */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\")\nvar Schema = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js\")\nvar DefinedInfo = __webpack_require__(/*! ./defined-info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js\")\n\nmodule.exports = create\n\nfunction create(definition) {\n  var space = definition.space\n  var mustUseProperty = definition.mustUseProperty || []\n  var attributes = definition.attributes || {}\n  var props = definition.properties\n  var transform = definition.transform\n  var property = {}\n  var normal = {}\n  var prop\n  var info\n\n  for (prop in props) {\n    info = new DefinedInfo(\n      prop,\n      transform(attributes, prop),\n      props[prop],\n      space\n    )\n\n    if (mustUseProperty.indexOf(prop) !== -1) {\n      info.mustUseProperty = true\n    }\n\n    property[prop] = info\n\n    normal[normalize(prop)] = prop\n    normal[normalize(info.attribute)] = prop\n  }\n\n  return new Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY3JlYXRlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLHVHQUFpQjtBQUN6QyxhQUFhLG1CQUFPLENBQUMsc0dBQVU7QUFDL0Isa0JBQWtCLG1CQUFPLENBQUMsa0hBQWdCOztBQUUxQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFxjcmVhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBub3JtYWxpemUgPSByZXF1aXJlKCcuLi8uLi9ub3JtYWxpemUnKVxudmFyIFNjaGVtYSA9IHJlcXVpcmUoJy4vc2NoZW1hJylcbnZhciBEZWZpbmVkSW5mbyA9IHJlcXVpcmUoJy4vZGVmaW5lZC1pbmZvJylcblxubW9kdWxlLmV4cG9ydHMgPSBjcmVhdGVcblxuZnVuY3Rpb24gY3JlYXRlKGRlZmluaXRpb24pIHtcbiAgdmFyIHNwYWNlID0gZGVmaW5pdGlvbi5zcGFjZVxuICB2YXIgbXVzdFVzZVByb3BlcnR5ID0gZGVmaW5pdGlvbi5tdXN0VXNlUHJvcGVydHkgfHwgW11cbiAgdmFyIGF0dHJpYnV0ZXMgPSBkZWZpbml0aW9uLmF0dHJpYnV0ZXMgfHwge31cbiAgdmFyIHByb3BzID0gZGVmaW5pdGlvbi5wcm9wZXJ0aWVzXG4gIHZhciB0cmFuc2Zvcm0gPSBkZWZpbml0aW9uLnRyYW5zZm9ybVxuICB2YXIgcHJvcGVydHkgPSB7fVxuICB2YXIgbm9ybWFsID0ge31cbiAgdmFyIHByb3BcbiAgdmFyIGluZm9cblxuICBmb3IgKHByb3AgaW4gcHJvcHMpIHtcbiAgICBpbmZvID0gbmV3IERlZmluZWRJbmZvKFxuICAgICAgcHJvcCxcbiAgICAgIHRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wKSxcbiAgICAgIHByb3BzW3Byb3BdLFxuICAgICAgc3BhY2VcbiAgICApXG5cbiAgICBpZiAobXVzdFVzZVByb3BlcnR5LmluZGV4T2YocHJvcCkgIT09IC0xKSB7XG4gICAgICBpbmZvLm11c3RVc2VQcm9wZXJ0eSA9IHRydWVcbiAgICB9XG5cbiAgICBwcm9wZXJ0eVtwcm9wXSA9IGluZm9cblxuICAgIG5vcm1hbFtub3JtYWxpemUocHJvcCldID0gcHJvcFxuICAgIG5vcm1hbFtub3JtYWxpemUoaW5mby5hdHRyaWJ1dGUpXSA9IHByb3BcbiAgfVxuXG4gIHJldHVybiBuZXcgU2NoZW1hKHByb3BlcnR5LCBub3JtYWwsIHNwYWNlKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Info = __webpack_require__(/*! ./info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js\")\nvar types = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\")\n\nmodule.exports = DefinedInfo\n\nDefinedInfo.prototype = new Info()\nDefinedInfo.prototype.defined = true\n\nvar checks = [\n  'boolean',\n  'booleanish',\n  'overloadedBoolean',\n  'number',\n  'commaSeparated',\n  'spaceSeparated',\n  'commaOrSpaceSeparated'\n]\nvar checksLength = checks.length\n\nfunction DefinedInfo(property, attribute, mask, space) {\n  var index = -1\n  var check\n\n  mark(this, 'space', space)\n\n  Info.call(this, property, attribute)\n\n  while (++index < checksLength) {\n    check = checks[index]\n    mark(this, check, (mask & types[check]) === types[check])\n  }\n}\n\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvZGVmaW5lZC1pbmZvLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLFdBQVcsbUJBQU8sQ0FBQyxrR0FBUTtBQUMzQixZQUFZLG1CQUFPLENBQUMsb0dBQVM7O0FBRTdCOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXGRlZmluZWQtaW5mby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIEluZm8gPSByZXF1aXJlKCcuL2luZm8nKVxudmFyIHR5cGVzID0gcmVxdWlyZSgnLi90eXBlcycpXG5cbm1vZHVsZS5leHBvcnRzID0gRGVmaW5lZEluZm9cblxuRGVmaW5lZEluZm8ucHJvdG90eXBlID0gbmV3IEluZm8oKVxuRGVmaW5lZEluZm8ucHJvdG90eXBlLmRlZmluZWQgPSB0cnVlXG5cbnZhciBjaGVja3MgPSBbXG4gICdib29sZWFuJyxcbiAgJ2Jvb2xlYW5pc2gnLFxuICAnb3ZlcmxvYWRlZEJvb2xlYW4nLFxuICAnbnVtYmVyJyxcbiAgJ2NvbW1hU2VwYXJhdGVkJyxcbiAgJ3NwYWNlU2VwYXJhdGVkJyxcbiAgJ2NvbW1hT3JTcGFjZVNlcGFyYXRlZCdcbl1cbnZhciBjaGVja3NMZW5ndGggPSBjaGVja3MubGVuZ3RoXG5cbmZ1bmN0aW9uIERlZmluZWRJbmZvKHByb3BlcnR5LCBhdHRyaWJ1dGUsIG1hc2ssIHNwYWNlKSB7XG4gIHZhciBpbmRleCA9IC0xXG4gIHZhciBjaGVja1xuXG4gIG1hcmsodGhpcywgJ3NwYWNlJywgc3BhY2UpXG5cbiAgSW5mby5jYWxsKHRoaXMsIHByb3BlcnR5LCBhdHRyaWJ1dGUpXG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaGVja3NMZW5ndGgpIHtcbiAgICBjaGVjayA9IGNoZWNrc1tpbmRleF1cbiAgICBtYXJrKHRoaXMsIGNoZWNrLCAobWFzayAmIHR5cGVzW2NoZWNrXSkgPT09IHR5cGVzW2NoZWNrXSlcbiAgfVxufVxuXG5mdW5jdGlvbiBtYXJrKHZhbHVlcywga2V5LCB2YWx1ZSkge1xuICBpZiAodmFsdWUpIHtcbiAgICB2YWx1ZXNba2V5XSA9IHZhbHVlXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js":
/*!************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/info.js ***!
  \************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = Info\n\nvar proto = Info.prototype\n\nproto.space = null\nproto.attribute = null\nproto.property = null\nproto.boolean = false\nproto.booleanish = false\nproto.overloadedBoolean = false\nproto.number = false\nproto.commaSeparated = false\nproto.spaceSeparated = false\nproto.commaOrSpaceSeparated = false\nproto.mustUseProperty = false\nproto.defined = false\n\nfunction Info(property, attribute) {\n  this.property = property\n  this.attribute = attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFxpbmZvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IEluZm9cblxudmFyIHByb3RvID0gSW5mby5wcm90b3R5cGVcblxucHJvdG8uc3BhY2UgPSBudWxsXG5wcm90by5hdHRyaWJ1dGUgPSBudWxsXG5wcm90by5wcm9wZXJ0eSA9IG51bGxcbnByb3RvLmJvb2xlYW4gPSBmYWxzZVxucHJvdG8uYm9vbGVhbmlzaCA9IGZhbHNlXG5wcm90by5vdmVybG9hZGVkQm9vbGVhbiA9IGZhbHNlXG5wcm90by5udW1iZXIgPSBmYWxzZVxucHJvdG8uY29tbWFTZXBhcmF0ZWQgPSBmYWxzZVxucHJvdG8uc3BhY2VTZXBhcmF0ZWQgPSBmYWxzZVxucHJvdG8uY29tbWFPclNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbnByb3RvLm11c3RVc2VQcm9wZXJ0eSA9IGZhbHNlXG5wcm90by5kZWZpbmVkID0gZmFsc2VcblxuZnVuY3Rpb24gSW5mbyhwcm9wZXJ0eSwgYXR0cmlidXRlKSB7XG4gIHRoaXMucHJvcGVydHkgPSBwcm9wZXJ0eVxuICB0aGlzLmF0dHJpYnV0ZSA9IGF0dHJpYnV0ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/merge.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar xtend = __webpack_require__(/*! xtend */ \"(ssr)/./node_modules/xtend/immutable.js\")\nvar Schema = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js\")\n\nmodule.exports = merge\n\nfunction merge(definitions) {\n  var length = definitions.length\n  var property = []\n  var normal = []\n  var index = -1\n  var info\n  var space\n\n  while (++index < length) {\n    info = definitions[index]\n    property.push(info.property)\n    normal.push(info.normal)\n    space = info.space\n  }\n\n  return new Schema(\n    xtend.apply(null, property),\n    xtend.apply(null, normal),\n    space\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosWUFBWSxtQkFBTyxDQUFDLHNEQUFPO0FBQzNCLGFBQWEsbUJBQU8sQ0FBQyxzR0FBVTs7QUFFL0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXG1lcmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgeHRlbmQgPSByZXF1aXJlKCd4dGVuZCcpXG52YXIgU2NoZW1hID0gcmVxdWlyZSgnLi9zY2hlbWEnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IG1lcmdlXG5cbmZ1bmN0aW9uIG1lcmdlKGRlZmluaXRpb25zKSB7XG4gIHZhciBsZW5ndGggPSBkZWZpbml0aW9ucy5sZW5ndGhcbiAgdmFyIHByb3BlcnR5ID0gW11cbiAgdmFyIG5vcm1hbCA9IFtdXG4gIHZhciBpbmRleCA9IC0xXG4gIHZhciBpbmZvXG4gIHZhciBzcGFjZVxuXG4gIHdoaWxlICgrK2luZGV4IDwgbGVuZ3RoKSB7XG4gICAgaW5mbyA9IGRlZmluaXRpb25zW2luZGV4XVxuICAgIHByb3BlcnR5LnB1c2goaW5mby5wcm9wZXJ0eSlcbiAgICBub3JtYWwucHVzaChpbmZvLm5vcm1hbClcbiAgICBzcGFjZSA9IGluZm8uc3BhY2VcbiAgfVxuXG4gIHJldHVybiBuZXcgU2NoZW1hKFxuICAgIHh0ZW5kLmFwcGx5KG51bGwsIHByb3BlcnR5KSxcbiAgICB4dGVuZC5hcHBseShudWxsLCBub3JtYWwpLFxuICAgIHNwYWNlXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = Schema\n\nvar proto = Schema.prototype\n\nproto.space = null\nproto.normal = {}\nproto.property = {}\n\nfunction Schema(property, normal, space) {\n  this.property = property\n  this.normal = normal\n\n  if (space) {\n    this.space = space\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFxzY2hlbWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gU2NoZW1hXG5cbnZhciBwcm90byA9IFNjaGVtYS5wcm90b3R5cGVcblxucHJvdG8uc3BhY2UgPSBudWxsXG5wcm90by5ub3JtYWwgPSB7fVxucHJvdG8ucHJvcGVydHkgPSB7fVxuXG5mdW5jdGlvbiBTY2hlbWEocHJvcGVydHksIG5vcm1hbCwgc3BhY2UpIHtcbiAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG4gIHRoaXMubm9ybWFsID0gbm9ybWFsXG5cbiAgaWYgKHNwYWNlKSB7XG4gICAgdGhpcy5zcGFjZSA9IHNwYWNlXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/types.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nvar powers = 0\n\nexports.boolean = increment()\nexports.booleanish = increment()\nexports.overloadedBoolean = increment()\nexports.number = increment()\nexports.spaceSeparated = increment()\nexports.commaSeparated = increment()\nexports.commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return Math.pow(2, ++powers)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsZUFBZTtBQUNmLGtCQUFrQjtBQUNsQix5QkFBeUI7QUFDekIsY0FBYztBQUNkLHNCQUFzQjtBQUN0QixzQkFBc0I7QUFDdEIsNkJBQTZCOztBQUU3QjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIHBvd2VycyA9IDBcblxuZXhwb3J0cy5ib29sZWFuID0gaW5jcmVtZW50KClcbmV4cG9ydHMuYm9vbGVhbmlzaCA9IGluY3JlbWVudCgpXG5leHBvcnRzLm92ZXJsb2FkZWRCb29sZWFuID0gaW5jcmVtZW50KClcbmV4cG9ydHMubnVtYmVyID0gaW5jcmVtZW50KClcbmV4cG9ydHMuc3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuZXhwb3J0cy5jb21tYVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5leHBvcnRzLmNvbW1hT3JTcGFjZVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5cbmZ1bmN0aW9uIGluY3JlbWVudCgpIHtcbiAgcmV0dXJuIE1hdGgucG93KDIsICsrcG93ZXJzKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xlink.js":
/*!********************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/xlink.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\n\nmodule.exports = create({\n  space: 'xlink',\n  transform: xlinkTransform,\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n\nfunction xlinkTransform(_, prop) {\n  return 'xlink:' + prop.slice(5).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQywyR0FBZTs7QUFFcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx4bGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGNyZWF0ZSA9IHJlcXVpcmUoJy4vdXRpbC9jcmVhdGUnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZSh7XG4gIHNwYWNlOiAneGxpbmsnLFxuICB0cmFuc2Zvcm06IHhsaW5rVHJhbnNmb3JtLFxuICBwcm9wZXJ0aWVzOiB7XG4gICAgeExpbmtBY3R1YXRlOiBudWxsLFxuICAgIHhMaW5rQXJjUm9sZTogbnVsbCxcbiAgICB4TGlua0hyZWY6IG51bGwsXG4gICAgeExpbmtSb2xlOiBudWxsLFxuICAgIHhMaW5rU2hvdzogbnVsbCxcbiAgICB4TGlua1RpdGxlOiBudWxsLFxuICAgIHhMaW5rVHlwZTogbnVsbFxuICB9XG59KVxuXG5mdW5jdGlvbiB4bGlua1RyYW5zZm9ybShfLCBwcm9wKSB7XG4gIHJldHVybiAneGxpbms6JyArIHByb3Auc2xpY2UoNSkudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xml.js":
/*!******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/xml.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\n\nmodule.exports = create({\n  space: 'xml',\n  transform: xmlTransform,\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n})\n\nfunction xmlTransform(_, prop) {\n  return 'xml:' + prop.slice(3).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixhQUFhLG1CQUFPLENBQUMsMkdBQWU7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHhtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGNyZWF0ZSA9IHJlcXVpcmUoJy4vdXRpbC9jcmVhdGUnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZSh7XG4gIHNwYWNlOiAneG1sJyxcbiAgdHJhbnNmb3JtOiB4bWxUcmFuc2Zvcm0sXG4gIHByb3BlcnRpZXM6IHtcbiAgICB4bWxMYW5nOiBudWxsLFxuICAgIHhtbEJhc2U6IG51bGwsXG4gICAgeG1sU3BhY2U6IG51bGxcbiAgfVxufSlcblxuZnVuY3Rpb24geG1sVHJhbnNmb3JtKF8sIHByb3ApIHtcbiAgcmV0dXJuICd4bWw6JyArIHByb3Auc2xpY2UoMykudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xmlns.js":
/*!********************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/xmlns.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\nvar caseInsensitiveTransform = __webpack_require__(/*! ./util/case-insensitive-transform */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js\")\n\nmodule.exports = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQywyR0FBZTtBQUNwQywrQkFBK0IsbUJBQU8sQ0FBQyxtSkFBbUM7O0FBRTFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxceG1sbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciBjcmVhdGUgPSByZXF1aXJlKCcuL3V0aWwvY3JlYXRlJylcbnZhciBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm0gPSByZXF1aXJlKCcuL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0nKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZSh7XG4gIHNwYWNlOiAneG1sbnMnLFxuICBhdHRyaWJ1dGVzOiB7XG4gICAgeG1sbnN4bGluazogJ3htbG5zOnhsaW5rJ1xuICB9LFxuICB0cmFuc2Zvcm06IGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybSxcbiAgcHJvcGVydGllczoge1xuICAgIHhtbG5zOiBudWxsLFxuICAgIHhtbG5zWExpbms6IG51bGxcbiAgfVxufSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xmlns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js":
/*!********************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/normalize.js ***!
  \********************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = normalize\n\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbm9ybWFsaXplLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxub3JtYWxpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gbm9ybWFsaXplXG5cbmZ1bmN0aW9uIG5vcm1hbGl6ZSh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/space-separated-tokens/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/space-separated-tokens/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar empty = ''\nvar space = ' '\nvar whiteSpace = /[ \\t\\n\\r\\f]+/g\n\nfunction parse(value) {\n  var input = String(value || empty).trim()\n  return input === empty ? [] : input.split(whiteSpace)\n}\n\nfunction stringify(values) {\n  return values.join(space).trim()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvc3BhY2Utc2VwYXJhdGVkLXRva2Vucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixhQUFhO0FBQ2IsaUJBQWlCOztBQUVqQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xcc3BhY2Utc2VwYXJhdGVkLXRva2Vuc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmV4cG9ydHMucGFyc2UgPSBwYXJzZVxuZXhwb3J0cy5zdHJpbmdpZnkgPSBzdHJpbmdpZnlcblxudmFyIGVtcHR5ID0gJydcbnZhciBzcGFjZSA9ICcgJ1xudmFyIHdoaXRlU3BhY2UgPSAvWyBcXHRcXG5cXHJcXGZdKy9nXG5cbmZ1bmN0aW9uIHBhcnNlKHZhbHVlKSB7XG4gIHZhciBpbnB1dCA9IFN0cmluZyh2YWx1ZSB8fCBlbXB0eSkudHJpbSgpXG4gIHJldHVybiBpbnB1dCA9PT0gZW1wdHkgPyBbXSA6IGlucHV0LnNwbGl0KHdoaXRlU3BhY2UpXG59XG5cbmZ1bmN0aW9uIHN0cmluZ2lmeSh2YWx1ZXMpIHtcbiAgcmV0dXJuIHZhbHVlcy5qb2luKHNwYWNlKS50cmltKClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/space-separated-tokens/index.js\n");

/***/ })

};
;