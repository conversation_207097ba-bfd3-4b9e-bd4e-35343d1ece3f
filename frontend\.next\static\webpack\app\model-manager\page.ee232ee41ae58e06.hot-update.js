"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/model-manager/page",{

/***/ "(app-pages-browser)/./src/app/model-manager/components/ModalWrapper.tsx":
/*!***********************************************************!*\
  !*** ./src/app/model-manager/components/ModalWrapper.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModalWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ModalWrapper(param) {\n    let { isOpen, onClose, title, subtitle, children, maxWidth = '2xl', showCloseButton = true, icon } = param;\n    _s();\n    // 添加ESC键退出弹窗功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModalWrapper.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"ModalWrapper.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === 'Escape') {\n                        onClose();\n                    }\n                }\n            }[\"ModalWrapper.useEffect.handleKeyDown\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleKeyDown);\n            }\n            return ({\n                \"ModalWrapper.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"ModalWrapper.useEffect\"];\n        }\n    }[\"ModalWrapper.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    const maxWidthClasses = {\n        sm: 'max-w-sm',\n        md: 'max-w-md',\n        lg: 'max-w-lg',\n        xl: 'max-w-xl',\n        '2xl': 'max-w-2xl',\n        '4xl': 'max-w-4xl',\n        '6xl': 'max-w-6xl'\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"fixed inset-0 bg-black/40 backdrop-blur-sm flex justify-center items-start z-50 p-4 pt-8\",\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"bg-theme-background rounded-2xl w-full \".concat(maxWidthClasses[maxWidth], \" max-h-[90vh] flex flex-col overflow-hidden border border-theme-border shadow-xl\"),\n                initial: {\n                    scale: 0.9,\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    scale: 0.95,\n                    opacity: 0,\n                    y: -10\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 pb-6 border-b border-theme-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 flex-1 min-w-0\",\n                                    children: [\n                                        icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-0 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"page-title text-theme-foreground\",\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this),\n                                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-theme-foreground-muted text-sm mt-1\",\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"w-10 h-10 rounded-full bg-theme-card hover:bg-theme-card-hover flex items-center justify-center text-theme-foreground-muted hover:text-theme-foreground transition-all duration-200 flex-shrink-0 border border-theme-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto scrollbar-thin\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModalWrapper.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(ModalWrapper, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = ModalWrapper;\nvar _c;\n$RefreshReg$(_c, \"ModalWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/model-manager/components/ModalWrapper.tsx\n"));

/***/ })

});