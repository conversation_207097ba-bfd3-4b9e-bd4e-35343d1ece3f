"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/page.tsx":
/*!**************************************!*\
  !*** ./src/app/simple-chat/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks */ \"(app-pages-browser)/./src/app/simple-chat/hooks/index.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/simple-chat/components/index.ts\");\n/* harmony import */ var _services_streamingChatService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/streamingChatService */ \"(app-pages-browser)/./src/app/simple-chat/services/streamingChatService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SimpleChatPage() {\n    _s();\n    // 使用模块化的hooks\n    const { conversations, currentConversation, loading: conversationLoading, error: conversationError, loadConversations, loadConversationsIfNeeded, createConversation, switchConversation, deleteConversation } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationManager)();\n    const { messages, setMessages, inputMessage, setInputMessage, isStreaming, setIsStreaming, selectedModel, setSelectedModel, models, expandedThinkingMessages, enableTools, setEnableTools, selectedTools, setSelectedTools, activeTool, setActiveTool, toolCalls, setToolCalls, currentAssistantMessageId, setCurrentAssistantMessageId, systemPrompt, selectAgent, toggleThinkingExpand, stopGeneration, selectBestModel, abortController, setAbortController } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useChatMessages)();\n    const { chatStyle, displaySize, setChatStyle: handleChatStyleChange, setDisplaySize: handleDisplaySizeChange } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useChatStyle)();\n    // UI状态\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Agent related state\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAgentId, setSelectedAgentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectorMode, setSelectorMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('model');\n    // 优化：直接使用models数据，无需转换\n    // models现在是CustomModel[]类型，包含完整的display_name等信息\n    // 使用ref来获取最新的messages值，避免在useCallback依赖中包含messages\n    const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages);\n    const selectedModelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(selectedModel);\n    const setMessagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setMessages);\n    const setActiveToolRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setActiveTool);\n    const setToolCallsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setToolCalls);\n    const setCurrentAssistantMessageIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setCurrentAssistantMessageId);\n    const setIsStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setIsStreaming);\n    const setErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setError);\n    const loadConversationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(loadConversations);\n    const systemPromptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(systemPrompt);\n    // 🔧 修复：添加清理函数的ref\n    const cleanupHandlersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            messagesRef.current = messages;\n            selectedModelRef.current = selectedModel;\n            setMessagesRef.current = setMessages;\n            setActiveToolRef.current = setActiveTool;\n            setToolCallsRef.current = setToolCalls;\n            setCurrentAssistantMessageIdRef.current = setCurrentAssistantMessageId;\n            setIsStreamingRef.current = setIsStreaming;\n            setErrorRef.current = setError;\n            loadConversationsRef.current = loadConversations;\n            systemPromptRef.current = systemPrompt;\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        messages,\n        selectedModel,\n        setMessages,\n        setActiveTool,\n        setToolCalls,\n        setCurrentAssistantMessageId,\n        setIsStreaming,\n        setError,\n        loadConversations,\n        systemPrompt\n    ]);\n    // 🔧 修复：组件卸载时清理所有pending的更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>{\n                    cleanupHandlersRef.current.forEach({\n                        \"SimpleChatPage.useEffect\": (cleanup)=>cleanup()\n                    }[\"SimpleChatPage.useEffect\"]);\n                    cleanupHandlersRef.current = [];\n                }\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], []);\n    // 从数据库数据更新消息的辅助函数\n    const updateMessagesFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[updateMessagesFromDatabase]\": (dbMessages)=>{\n            console.log('🔧 更新消息数据，总数:', dbMessages.length);\n            // 使用与useMessageLoader相同的逻辑处理工具调用消息\n            const allMessages = dbMessages.sort({\n                \"SimpleChatPage.useCallback[updateMessagesFromDatabase].allMessages\": (a, b)=>{\n                    if (a.timestamp !== b.timestamp) {\n                        return a.timestamp - b.timestamp;\n                    }\n                    return a.id - b.id;\n                }\n            }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase].allMessages\"]);\n            const formattedMessages = [];\n            const toolCallMessages = [];\n            for (const msg of allMessages){\n                if (msg.role === 'tool_call' && msg.tool_name) {\n                    // 处理工具调用消息\n                    let args = {};\n                    let result = '';\n                    try {\n                        args = msg.tool_args ? JSON.parse(msg.tool_args) : {};\n                    } catch (e) {\n                        args = {};\n                    }\n                    try {\n                        result = msg.tool_result ? typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result) : '';\n                    } catch (e) {\n                        result = msg.tool_result || '';\n                    }\n                    const toolCall = {\n                        id: \"tool-\".concat(msg.id),\n                        toolName: msg.tool_name,\n                        args: args,\n                        status: msg.tool_status || 'completed',\n                        result: result,\n                        error: msg.tool_error || undefined,\n                        startTime: msg.timestamp || new Date(msg.created_at).getTime(),\n                        executionTime: msg.tool_execution_time || 0\n                    };\n                    toolCallMessages.push(toolCall);\n                    formattedMessages.push({\n                        id: \"tool-placeholder-\".concat(msg.id),\n                        role: 'tool_call',\n                        content: '',\n                        timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                        toolCall: toolCall\n                    });\n                } else {\n                    formattedMessages.push({\n                        id: \"msg-\".concat(msg.id),\n                        role: msg.role,\n                        content: msg.content,\n                        timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                        model: msg.model,\n                        // 包含统计字段\n                        total_duration: msg.total_duration,\n                        load_duration: msg.load_duration,\n                        prompt_eval_count: msg.prompt_eval_count,\n                        prompt_eval_duration: msg.prompt_eval_duration,\n                        eval_count: msg.eval_count,\n                        eval_duration: msg.eval_duration\n                    });\n                }\n            }\n            // 检查是否有统计信息\n            const hasStats = formattedMessages.some({\n                \"SimpleChatPage.useCallback[updateMessagesFromDatabase].hasStats\": (msg)=>msg.role === 'assistant' && (msg.total_duration || msg.eval_count)\n            }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase].hasStats\"]);\n            console.log('🔧 更新后的消息是否包含统计信息:', hasStats);\n            console.log('🔧 更新后的工具调用数量:', toolCallMessages.length);\n            setMessagesRef.current(formattedMessages);\n            setToolCallsRef.current(toolCallMessages);\n        }\n    }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase]\"], []);\n    // 处理模型切换，传递对话ID以保存对话特定的模型选择\n    const handleModelChange = (modelName)=>{\n        const conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        setSelectedModel(modelName, conversationId);\n    };\n    // Fetch agents - 优化：添加缓存和错误处理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            let isMounted = true;\n            const fetchAgents = {\n                \"SimpleChatPage.useEffect.fetchAgents\": async ()=>{\n                    try {\n                        console.log('🤖 开始加载智能体列表');\n                        const response = await fetch('/api/agents');\n                        if (response.ok && isMounted) {\n                            const agents = await response.json();\n                            setAgents(agents);\n                            console.log(\"✅ 成功加载 \".concat(agents.length, \" 个智能体\"));\n                        }\n                    } catch (error) {\n                        if (isMounted) {\n                            console.error('❌ 加载智能体失败:', error);\n                        }\n                    }\n                }\n            }[\"SimpleChatPage.useEffect.fetchAgents\"];\n            fetchAgents();\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], []);\n    const handleAgentChange = (agentId)=>{\n        setSelectedAgentId(agentId);\n        selectAgent(agentId);\n    };\n    const handleSelectorModeChange = (mode)=>{\n        setSelectorMode(mode);\n    };\n    // 使用 URL 处理器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useUrlHandler)({\n        models,\n        selectedModel,\n        currentConversation,\n        conversationLoading,\n        createConversation,\n        switchConversation\n    });\n    // 使用消息加载器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useMessageLoader)({\n        currentConversation,\n        setSelectedModel,\n        setMessages,\n        setToolCalls,\n        selectedModel,\n        models,\n        selectBestModel\n    });\n    // 使用对话事件处理器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationEventHandlers)({\n        currentConversation,\n        conversations,\n        selectedModel,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        loadConversations,\n        setMessages,\n        setToolCalls,\n        setSelectedModel,\n        setError,\n        setIsProcessingUrl: {\n            \"SimpleChatPage.useConversationEventHandlers\": ()=>{}\n        }[\"SimpleChatPage.useConversationEventHandlers\"]\n    });\n    // 清空当前对话\n    const clearCurrentChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[clearCurrentChat]\": async ()=>{\n            if (!currentConversation) return;\n            try {\n                const response = await fetch(\"/api/conversations/\".concat(currentConversation.id, \"/clear\"), {\n                    method: 'POST'\n                });\n                if (response.ok) {\n                    // 清空当前消息\n                    setMessages([]);\n                    setToolCalls([]);\n                    setActiveTool(null);\n                    setError(null);\n                    // 重新加载对话列表\n                    loadConversations();\n                }\n            } catch (error) {\n                console.error('清空对话失败:', error);\n                setError('清空对话失败');\n            }\n        }\n    }[\"SimpleChatPage.useCallback[clearCurrentChat]\"], [\n        currentConversation,\n        setMessages,\n        setToolCalls,\n        setActiveTool,\n        setError,\n        loadConversations\n    ]);\n    // 创建流处理器句柄 - 修复：使用useCallback确保函数稳定性，移除不必要的依赖\n    const createStreamHandlers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n            const handlers = {\n                onMessageUpdate: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (messageId, content, stats)=>{\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>msg.id === messageId ? {\n                                            ...msg,\n                                            content,\n                                            ...stats || {}\n                                        } : msg\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallStart: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCall)=>{\n                        setActiveToolRef.current(toolCall);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    toolCall\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        const toolCallMessage = {\n                            id: \"tool-runtime-\".concat(toolCall.id),\n                            role: 'tool_call',\n                            content: '',\n                            timestamp: Date.now(),\n                            toolCall: toolCall\n                        };\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    toolCallMessage\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallComplete: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCallId, toolName, result, executionTime)=>{\n                        setActiveToolRef.current(null);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (tc)=>{\n                                        const isMatch = toolCallId ? tc.id === toolCallId : tc.toolName === toolName && tc.status === 'executing';\n                                        return isMatch ? {\n                                            ...tc,\n                                            status: 'completed',\n                                            result: typeof result === 'string' ? result : JSON.stringify(result),\n                                            executionTime: executionTime || Date.now() - tc.startTime\n                                        } : tc;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>{\n                                        if (msg.role === 'tool_call' && msg.toolCall) {\n                                            const isMatch = toolCallId ? msg.toolCall.id === toolCallId : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';\n                                            if (isMatch) {\n                                                return {\n                                                    ...msg,\n                                                    toolCall: {\n                                                        id: msg.toolCall.id,\n                                                        toolName: msg.toolCall.toolName,\n                                                        args: msg.toolCall.args,\n                                                        status: 'completed',\n                                                        result: typeof result === 'string' ? result : JSON.stringify(result),\n                                                        startTime: msg.toolCall.startTime,\n                                                        executionTime: executionTime || Date.now() - msg.toolCall.startTime\n                                                    }\n                                                };\n                                            }\n                                        }\n                                        return msg;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallError: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCallId, toolName, error, executionTime)=>{\n                        setActiveToolRef.current(null);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (tc)=>{\n                                        const isMatch = toolCallId ? tc.id === toolCallId : tc.toolName === toolName && tc.status === 'executing';\n                                        return isMatch ? {\n                                            ...tc,\n                                            status: 'error',\n                                            error: error || '工具调用失败',\n                                            executionTime: executionTime || Date.now() - tc.startTime\n                                        } : tc;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>{\n                                        if (msg.role === 'tool_call' && msg.toolCall) {\n                                            const isMatch = toolCallId ? msg.toolCall.id === toolCallId : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';\n                                            if (isMatch) {\n                                                return {\n                                                    ...msg,\n                                                    toolCall: {\n                                                        id: msg.toolCall.id,\n                                                        toolName: msg.toolCall.toolName,\n                                                        args: msg.toolCall.args,\n                                                        status: 'error',\n                                                        error: error || '工具调用失败',\n                                                        startTime: msg.toolCall.startTime,\n                                                        executionTime: executionTime || Date.now() - msg.toolCall.startTime\n                                                    }\n                                                };\n                                            }\n                                        }\n                                        return msg;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onNewAssistantMessage: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (messageId)=>{\n                        const newAssistantMessage = {\n                            id: messageId,\n                            role: 'assistant',\n                            content: '',\n                            timestamp: Date.now(),\n                            model: selectedModelRef.current\n                        };\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    newAssistantMessage\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setCurrentAssistantMessageIdRef.current(messageId);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onStreamEnd: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n                        setIsStreamingRef.current(false);\n                        setActiveToolRef.current(null);\n                        setCurrentAssistantMessageIdRef.current(null);\n                        // 优化：更智能的统计信息获取策略，减少API调用\n                        const cleanup = {\n                            \"SimpleChatPage.useCallback[createStreamHandlers].cleanup\": ()=>{\n                                if (currentConversation) {\n                                    console.log('🔧 对话完成，准备获取统计信息');\n                                    // 使用单次调用获取统计信息，如果没有则等待后重试一次\n                                    const fetchStats = {\n                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\": async function() {\n                                            let retryOnce = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n                                            try {\n                                                const response = await fetch(\"/api/conversations/\".concat(currentConversation.id));\n                                                const data = await response.json();\n                                                if (data.success && data.messages) {\n                                                    const hasStats = data.messages.some({\n                                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats.hasStats\": (msg)=>msg.role === 'assistant' && (msg.total_duration || msg.eval_count)\n                                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats.hasStats\"]);\n                                                    if (hasStats) {\n                                                        console.log('✅ 获取到统计信息，更新消息');\n                                                        updateMessagesFromDatabase(data.messages);\n                                                    } else if (retryOnce) {\n                                                        console.log('⏳ 统计信息未就绪，1秒后重试一次');\n                                                        setTimeout({\n                                                            \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\": ()=>fetchStats(false)\n                                                        }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\"], 1000);\n                                                    } else {\n                                                        console.log('⚠️ 统计信息仍未就绪，使用当前消息');\n                                                        updateMessagesFromDatabase(data.messages);\n                                                    }\n                                                } else {\n                                                    console.log('❌ 获取消息数据失败');\n                                                }\n                                            } catch (err) {\n                                                console.error('获取统计信息失败:', err);\n                                            }\n                                        }\n                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\"];\n                                    // 延迟300ms后开始获取，给服务器时间保存统计信息\n                                    setTimeout({\n                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup\": ()=>fetchStats()\n                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup\"], 300);\n                                } else {\n                                    console.log('🔄 无当前对话，刷新对话列表');\n                                    loadConversationsRef.current();\n                                }\n                            }\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup\"];\n                        // 添加到清理队列\n                        cleanupHandlersRef.current.push(cleanup);\n                        cleanup();\n                        // 从清理队列中移除\n                        setTimeout({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n                                const index = cleanupHandlersRef.current.indexOf(cleanup);\n                                if (index > -1) {\n                                    cleanupHandlersRef.current.splice(index, 1);\n                                }\n                            }\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"], 3000);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onError: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (errorMessage)=>{\n                        setErrorRef.current(errorMessage);\n                        setIsStreamingRef.current(false);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]\n            };\n            return handlers;\n        }\n    }[\"SimpleChatPage.useCallback[createStreamHandlers]\"], [\n        updateMessagesFromDatabase,\n        currentConversation\n    ]);\n    // 插入文本到输入框\n    const handleInsertText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[handleInsertText]\": (text)=>{\n            setInputMessage(text);\n        }\n    }[\"SimpleChatPage.useCallback[handleInsertText]\"], [\n        setInputMessage\n    ]);\n    // 发送消息的核心逻辑\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[sendMessage]\": async ()=>{\n            if (!inputMessage.trim() || !selectedModel || isStreaming) {\n                return;\n            }\n            let activeConversation = currentConversation;\n            if (!activeConversation) {\n                const title = inputMessage.trim().substring(0, 30) + (inputMessage.length > 30 ? '...' : '');\n                const conversationId = await createConversation(title, selectedModel);\n                if (!conversationId) {\n                    setError('创建对话失败');\n                    return;\n                }\n                await new Promise({\n                    \"SimpleChatPage.useCallback[sendMessage]\": (resolve)=>setTimeout(resolve, 100)\n                }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n                activeConversation = currentConversation;\n            }\n            const userMessage = {\n                id: \"user-\".concat(Date.now()),\n                role: 'user',\n                content: inputMessage.trim(),\n                timestamp: Date.now()\n            };\n            // 获取当前的消息列表（使用ref避免在依赖中包含messages）\n            const currentMessages = messagesRef.current;\n            setMessages({\n                \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        userMessage\n                    ]\n            }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n            setInputMessage('');\n            setIsStreaming(true);\n            setError(null);\n            setToolCalls([]);\n            setActiveTool(null);\n            const assistantMessageId = \"assistant-\".concat(Date.now());\n            const assistantMessage = {\n                id: assistantMessageId,\n                role: 'assistant',\n                content: '',\n                timestamp: Date.now(),\n                model: selectedModel\n            };\n            setMessages({\n                \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]\n            }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n            setCurrentAssistantMessageId(assistantMessageId);\n            try {\n                // 创建新的 AbortController\n                const controller = new AbortController();\n                setAbortController(controller);\n                const chatRequestBody = {\n                    model: selectedModel,\n                    conversationId: activeConversation === null || activeConversation === void 0 ? void 0 : activeConversation.id,\n                    messages: [\n                        ...systemPromptRef.current ? [\n                            {\n                                role: 'system',\n                                content: systemPromptRef.current\n                            }\n                        ] : [],\n                        ...currentMessages.map({\n                            \"SimpleChatPage.useCallback[sendMessage]\": (msg)=>({\n                                    role: msg.role,\n                                    content: msg.content\n                                })\n                        }[\"SimpleChatPage.useCallback[sendMessage]\"]),\n                        {\n                            role: 'user',\n                            content: userMessage.content\n                        }\n                    ],\n                    stream: true,\n                    enableTools,\n                    selectedTools\n                };\n                const response = await fetch('/api/chat', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(chatRequestBody),\n                    signal: controller.signal\n                });\n                if (!response.ok) {\n                    throw new Error('聊天请求失败');\n                }\n                // 使用流式服务处理响应，传递 AbortController\n                await _services_streamingChatService__WEBPACK_IMPORTED_MODULE_5__.streamingChatService.processStreamingResponse(response, createStreamHandlers(), assistantMessageId, controller);\n            } catch (err) {\n                // 如果是中断错误，不显示错误信息\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('🛑 用户主动停止了生成');\n                } else {\n                    setError(err instanceof Error ? err.message : '发送消息失败');\n                    setMessages({\n                        \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>prev.filter({\n                                \"SimpleChatPage.useCallback[sendMessage]\": (msg)=>msg.id !== assistantMessageId\n                            }[\"SimpleChatPage.useCallback[sendMessage]\"])\n                    }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n                }\n            } finally{\n                setIsStreaming(false);\n                setAbortController(null);\n            }\n        }\n    }[\"SimpleChatPage.useCallback[sendMessage]\"], [\n        inputMessage,\n        selectedModel,\n        isStreaming,\n        currentConversation,\n        enableTools,\n        selectedTools,\n        createConversation,\n        systemPrompt,\n        setAbortController\n    ]);\n    // 侧边栏事件处理函数 - 优化：按需加载对话列表\n    const handleCreateConversation = ()=>{\n        window.location.href = '/simple-chat?new=true';\n    };\n    const handleLoadConversation = async (conversationId)=>{\n        // 确保有对话列表数据\n        await loadConversationsIfNeeded();\n        window.location.href = \"/simple-chat?id=\".concat(conversationId);\n    };\n    const handleDeleteConversation = async (conversationId)=>{\n        try {\n            // 确保有对话列表数据\n            await loadConversationsIfNeeded();\n            const response = await fetch(\"/api/conversations/\".concat(conversationId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await loadConversations(); // 删除后刷新列表\n                // 如果删除的是当前对话，重定向到新对话\n                if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                    window.location.href = '/simple-chat?new=true';\n                }\n            }\n        } catch (error) {\n            console.error('Failed to delete conversation:', error);\n        }\n    };\n    // 确保侧边栏有对话列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            // 延迟加载对话列表，避免阻塞页面初始化\n            const timer = setTimeout({\n                \"SimpleChatPage.useEffect.timer\": ()=>{\n                    loadConversationsIfNeeded();\n                }\n            }[\"SimpleChatPage.useEffect.timer\"], 100);\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        loadConversationsIfNeeded\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-theme-background overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                conversations: conversations,\n                currentConversation: currentConversation,\n                onCreateConversation: handleCreateConversation,\n                onLoadConversation: handleLoadConversation,\n                onDeleteConversation: handleDeleteConversation\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n                lineNumber: 649,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_4__.ChatContainer, {\n                currentConversation: currentConversation,\n                models: models,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                agents: agents,\n                selectedAgentId: selectedAgentId,\n                onAgentChange: handleAgentChange,\n                selectorMode: selectorMode,\n                onSelectorModeChange: handleSelectorModeChange,\n                messages: messages,\n                inputMessage: inputMessage,\n                onInputChange: setInputMessage,\n                onSendMessage: sendMessage,\n                isStreaming: isStreaming,\n                onStopGeneration: stopGeneration,\n                expandedThinkingMessages: expandedThinkingMessages,\n                onToggleThinkingExpand: toggleThinkingExpand,\n                enableTools: enableTools,\n                selectedTools: selectedTools,\n                onToolsToggle: setEnableTools,\n                onSelectedToolsChange: setSelectedTools,\n                onInsertText: handleInsertText,\n                onClearChat: clearCurrentChat,\n                error: error,\n                onDismissError: ()=>setError(null),\n                chatStyle: chatStyle,\n                displaySize: displaySize,\n                onChatStyleChange: handleChatStyleChange,\n                onDisplaySizeChange: handleDisplaySizeChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n        lineNumber: 647,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatPage, \"SAsZipHqoI18tong0BKkor9DnWs=\", false, function() {\n    return [\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationManager,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useChatMessages,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useChatStyle,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useUrlHandler,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useMessageLoader,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationEventHandlers\n    ];\n});\n_c = SimpleChatPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/page.tsx\n"));

/***/ })

});