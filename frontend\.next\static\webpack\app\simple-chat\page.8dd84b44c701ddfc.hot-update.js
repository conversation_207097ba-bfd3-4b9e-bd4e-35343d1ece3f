"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/hooks/useUrlHandler.ts":
/*!****************************************************!*\
  !*** ./src/app/simple-chat/hooks/useUrlHandler.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUrlHandler: () => (/* binding */ useUrlHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useUrlHandler auto */ \n\nfunction useUrlHandler(param) {\n    let { models, selectedModel, currentConversation, conversationLoading, createConversation, switchConversation } = param;\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const [isProcessingUrl, setIsProcessingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 使用ref存储函数引用，避免useEffect依赖问题\n    const createConversationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(createConversation);\n    const switchConversationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(switchConversation);\n    // 更新ref的值\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUrlHandler.useEffect\": ()=>{\n            createConversationRef.current = createConversation;\n            switchConversationRef.current = switchConversation;\n        }\n    }[\"useUrlHandler.useEffect\"], [\n        createConversation,\n        switchConversation\n    ]);\n    // URL处理逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUrlHandler.useEffect\": ()=>{\n            const handleUrlChange = {\n                \"useUrlHandler.useEffect.handleUrlChange\": async ()=>{\n                    if (isProcessingUrl) return;\n                    const shouldCreateNew = searchParams.get('new') === 'true';\n                    const conversationId = searchParams.get('id');\n                    if (shouldCreateNew && models.length > 0 && selectedModel && !currentConversation && !conversationLoading) {\n                        setIsProcessingUrl(true);\n                        const title = \"新对话 - \".concat(new Date().toLocaleString('zh-CN', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        }));\n                        try {\n                            const newConversationId = await createConversationRef.current(title, selectedModel);\n                            if (newConversationId) {\n                                window.history.replaceState(null, '', \"/simple-chat?id=\".concat(newConversationId));\n                            }\n                        } catch (err) {\n                            console.error('自动创建对话失败:', err);\n                            throw new Error('自动创建对话失败');\n                        } finally{\n                            setIsProcessingUrl(false);\n                        }\n                    } else if (conversationId && !conversationLoading && !isProcessingUrl) {\n                        const id = parseInt(conversationId);\n                        if (!isNaN(id) && (!currentConversation || currentConversation.id !== id)) {\n                            setIsProcessingUrl(true);\n                            try {\n                                await switchConversationRef.current(id);\n                            } catch (err) {\n                                console.error('加载指定对话失败:', err);\n                                throw new Error('加载对话失败');\n                            } finally{\n                                setIsProcessingUrl(false);\n                            }\n                        }\n                    }\n                }\n            }[\"useUrlHandler.useEffect.handleUrlChange\"];\n            handleUrlChange().catch({\n                \"useUrlHandler.useEffect\": (error)=>{\n                    console.error('URL处理失败:', error);\n                }\n            }[\"useUrlHandler.useEffect\"]);\n        }\n    }[\"useUrlHandler.useEffect\"], [\n        searchParams,\n        models.length,\n        selectedModel,\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id,\n        conversationLoading,\n        isProcessingUrl\n    ]);\n    return {\n        isProcessingUrl,\n        setIsProcessingUrl\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/hooks/useUrlHandler.ts\n"));

/***/ })

});