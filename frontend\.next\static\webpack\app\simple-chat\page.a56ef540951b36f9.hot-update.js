"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/components/chat/ChatContainer.tsx":
/*!***************************************************************!*\
  !*** ./src/app/simple-chat/components/chat/ChatContainer.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatContainer: () => (/* binding */ ChatContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _conversation_ChatHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../conversation/ChatHeader */ \"(app-pages-browser)/./src/app/simple-chat/components/conversation/ChatHeader.tsx\");\n/* harmony import */ var _MessageList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageList */ \"(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageInput */ \"(app-pages-browser)/./src/app/simple-chat/components/chat/MessageInput.tsx\");\n/* harmony import */ var _EmptyState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EmptyState */ \"(app-pages-browser)/./src/app/simple-chat/components/chat/EmptyState.tsx\");\n/* harmony import */ var _ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/ErrorDisplay */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/ErrorDisplay.tsx\");\n/* harmony import */ var _tools_ToolSettings__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../tools/ToolSettings */ \"(app-pages-browser)/./src/app/simple-chat/components/tools/ToolSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatContainer auto */ \n\n\n\n\n\n\n\nfunction ChatContainer(param) {\n    let { currentConversation, models, selectedModel, onModelChange, agents, selectedAgentId, onAgentChange, selectorMode, onSelectorModeChange, customModels, messages, inputMessage, onInputChange, onSendMessage, isStreaming, onStopGeneration, expandedThinkingMessages, onToggleThinkingExpand, enableTools, selectedTools, onToolsToggle, onSelectedToolsChange, onInsertText, onClearChat, error, onDismissError, chatStyle, displaySize, onChatStyleChange, onDisplaySizeChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col min-w-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_conversation_ChatHeader__WEBPACK_IMPORTED_MODULE_2__.ChatHeader, {\n                currentConversation: currentConversation,\n                models: models,\n                selectedModel: selectedModel,\n                onModelChange: onModelChange,\n                agents: agents,\n                selectedAgentId: selectedAgentId,\n                onAgentChange: onAgentChange,\n                selectorMode: selectorMode,\n                onSelectorModeChange: onSelectorModeChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden relative\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center px-4 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EmptyState__WEBPACK_IMPORTED_MODULE_5__.EmptyState, {\n                        currentConversation: currentConversation\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 overflow-y-auto scrollbar-thin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center min-h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full chat-container-responsive \".concat(displaySize || 'compact'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageList__WEBPACK_IMPORTED_MODULE_3__.MessageList, {\n                                messages: messages,\n                                isStreaming: isStreaming,\n                                expandedThinkingMessages: expandedThinkingMessages,\n                                onToggleThinkingExpand: onToggleThinkingExpand,\n                                chatStyle: chatStyle,\n                                selectedModel: selectedModel,\n                                customModels: customModels\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 mb-4 chat-container-responsive \".concat(displaySize || 'compact'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__.ErrorDisplay, {\n                        message: error,\n                        onDismiss: onDismissError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full p-4 chat-container-responsive \".concat(displaySize || 'compact'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_ToolSettings__WEBPACK_IMPORTED_MODULE_7__.ToolSettings, {\n                                selectedModel: selectedModel,\n                                enableTools: enableTools,\n                                selectedTools: selectedTools,\n                                onToolsToggle: onToolsToggle,\n                                onSelectedToolsChange: onSelectedToolsChange,\n                                onInsertText: onInsertText,\n                                onClearChat: onClearChat,\n                                chatStyle: chatStyle,\n                                displaySize: displaySize,\n                                onChatStyleChange: onChatStyleChange,\n                                onDisplaySizeChange: onDisplaySizeChange\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_4__.MessageInput, {\n                                inputMessage: inputMessage,\n                                onInputChange: onInputChange,\n                                onSendMessage: onSendMessage,\n                                onStopGeneration: onStopGeneration,\n                                isStreaming: isStreaming,\n                                currentConversation: currentConversation,\n                                selectedModel: selectedModel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\ChatContainer.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_c = ChatContainer;\nvar _c;\n$RefreshReg$(_c, \"ChatContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/components/chat/ChatContainer.tsx\n"));

/***/ })

});