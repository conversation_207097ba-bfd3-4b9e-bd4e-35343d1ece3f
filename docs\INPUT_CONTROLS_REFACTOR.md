# 输入控制组件重构规范化

## 重构目标

解决原有输入区域控制按钮分散、样式不统一、职责不清的问题，创建规范化的组件架构。

## 问题分析

### 原有问题
1. **功能混合**：`ToolToggle.tsx` 包含工具切换和清空对话两个不同功能
2. **组件分散**：聊天样式切换在 `ui/` 目录，但实际是输入区域控制按钮
3. **样式不一致**：三个按钮使用不同的样式实现
4. **职责不清**：缺乏清晰的模块划分和组件层次

### 目录结构问题
```
components/
├── ui/ChatStyleToggle.tsx        # 聊天样式控制（位置不当）
└── tools/ToolToggle.tsx          # 工具控制+清空操作（功能混合）
```

## 重构方案

### 新的目录结构
```
components/
├── input-controls/               # 新增：输入控制组件目录
│   ├── BaseControlButton.tsx    # 基础控制按钮组件
│   ├── ChatStyleControl.tsx     # 聊天样式控制
│   ├── ToolControl.tsx          # 工具控制
│   ├── ChatActionsControl.tsx   # 聊天操作控制
│   ├── InputControlsGroup.tsx   # 控制组合组件
│   └── index.ts                 # 导出文件
├── ui/                          # 保留：通用UI组件
└── tools/                       # 保留：工具相关组件
```

### 组件职责划分

#### 1. BaseControlButton.tsx
**职责**：提供统一的按钮样式和行为基础
- 统一的尺寸规范（sm: 8x8, md: 10x10）
- 标准化的变体样式（default, primary, danger）
- 通用的状态管理（disabled, active, loading）
- 标准化的装饰元素（徽章、状态指示器、工具提示）

**特性**：
```typescript
interface BaseControlButtonProps {
  variant?: 'default' | 'primary' | 'danger';
  size?: 'sm' | 'md';
  badge?: { count: number; position: 'top-left' | 'top-right'; color?: 'blue' | 'red' | 'green' };
  statusIndicator?: { status: 'success' | 'error' | 'warning'; position: 'top-right' | 'top-left' };
  tooltip?: string;
  // ... 其他属性
}
```

#### 2. ChatStyleControl.tsx
**职责**：专门处理聊天样式的切换
- 样式选择菜单
- 当前样式状态显示
- 样式切换逻辑

#### 3. ToolControl.tsx
**职责**：专门处理MCP工具的控制
- 工具开关切换
- 工具设置面板控制
- 模型工具支持检测
- 工具数量显示

#### 4. ChatActionsControl.tsx
**职责**：处理聊天相关的操作
- 清空对话
- 未来可扩展其他聊天操作

#### 5. InputControlsGroup.tsx
**职责**：组合和布局所有输入控制组件
- 统一的布局管理
- 组件间的协调
- 响应式设计

## 设计规范

### 视觉规范
1. **尺寸标准**：
   - 默认按钮：10x10 (40px)
   - 小按钮：8x8 (32px)
   - 圆形设计，2px边框

2. **颜色系统**：
   - 默认状态：灰色边框，透明背景
   - 激活状态：蓝色边框，浅蓝背景
   - 危险操作：悬停时红色主题
   - 禁用状态：降低透明度，禁用交互

3. **状态指示**：
   - 成功：绿色圆点
   - 错误：红色圆点
   - 警告：黄色圆点
   - 位置：右上角或左上角

4. **数量徽章**：
   - 蓝色背景，白色文字
   - 位置：左上角或右上角
   - 最大显示99+

### 交互规范
1. **悬停效果**：200ms过渡动画
2. **加载状态**：旋转动画指示器
3. **工具提示**：悬停显示，底部居中
4. **禁用状态**：视觉反馈+交互禁用

## 使用方式

### 基础使用
```typescript
import { InputControlsGroup } from '../input-controls';

<InputControlsGroup
  chatStyle={chatStyle}
  onChatStyleChange={onChatStyleChange}
  enableTools={enableTools}
  // ... 其他属性
/>
```

### 单独使用
```typescript
import { ChatStyleControl, ToolControl, ChatActionsControl } from '../input-controls';

// 单独使用样式控制
<ChatStyleControl chatStyle={chatStyle} onStyleChange={onStyleChange} />

// 单独使用工具控制
<ToolControl enableTools={enableTools} onToolsToggle={onToolsToggle} />

// 单独使用操作控制
<ChatActionsControl onClearChat={onClearChat} />
```

### 自定义按钮
```typescript
import { BaseControlButton } from '../input-controls';

<BaseControlButton
  onClick={handleClick}
  variant="primary"
  badge={{ count: 5, position: 'top-left' }}
  statusIndicator={{ status: 'success', position: 'top-right' }}
  tooltip="自定义按钮"
>
  <CustomIcon className="w-5 h-5" />
</BaseControlButton>
```

## 迁移指南

### 从旧组件迁移

#### 1. ToolToggle → ToolControl + ChatActionsControl
```typescript
// 旧方式
<ToolToggle
  enableTools={enableTools}
  onToolsToggle={onToolsToggle}
  onClearChat={onClearChat}
  // ... 其他属性
/>

// 新方式
<ToolControl
  enableTools={enableTools}
  onToolsToggle={onToolsToggle}
  // ... 工具相关属性
/>
<ChatActionsControl onClearChat={onClearChat} />

// 或使用组合组件
<InputControlsGroup
  enableTools={enableTools}
  onToolsToggle={onToolsToggle}
  onClearChat={onClearChat}
  // ... 所有属性
/>
```

#### 2. ChatStyleToggle → ChatStyleControl
```typescript
// 旧方式
<ChatStyleButton onClick={() => setShowMenu(true)} />
<ChatStyleMenu isOpen={showMenu} onClose={() => setShowMenu(false)} />

// 新方式
<ChatStyleControl chatStyle={chatStyle} onStyleChange={onStyleChange} />
```

## 向后兼容

- 保留旧组件的导出，标记为废弃
- 提供迁移警告和建议
- 逐步迁移现有使用场景

## 扩展性

### 新增控制类型
1. 在 `input-controls/` 目录创建新组件
2. 继承 `BaseControlButton` 的设计规范
3. 在 `InputControlsGroup` 中添加布局位置
4. 更新导出文件

### 自定义样式
- 通过 `BaseControlButton` 的 `className` 属性
- 遵循现有的设计系统变量
- 保持视觉一致性

## 优势

1. **统一性**：所有控制按钮使用相同的设计语言
2. **可维护性**：清晰的职责划分，易于维护和测试
3. **可扩展性**：基于基础组件，易于添加新功能
4. **可复用性**：组件可在其他场景中复用
5. **类型安全**：完整的TypeScript类型定义 