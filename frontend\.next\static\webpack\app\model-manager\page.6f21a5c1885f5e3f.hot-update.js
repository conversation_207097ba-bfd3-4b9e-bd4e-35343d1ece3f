"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/model-manager/page",{

/***/ "(app-pages-browser)/./src/app/model-manager/components/FileUploadModelForm.tsx":
/*!******************************************************************!*\
  !*** ./src/app/model-manager/components/FileUploadModelForm.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileUploadModelForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Folder,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Folder,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Folder,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Folder,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _ModalWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModalWrapper */ \"(app-pages-browser)/./src/app/model-manager/components/ModalWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst QUANTIZATION_OPTIONS = [\n    {\n        value: '',\n        label: '不量化'\n    },\n    {\n        value: 'q4_K_M',\n        label: 'Q4_K_M (推荐, 中等质量)'\n    },\n    {\n        value: 'q4_K_S',\n        label: 'Q4_K_S (小尺寸)'\n    },\n    {\n        value: 'q8_0',\n        label: 'Q8_0 (推荐, 高质量)'\n    }\n];\n// 统一的表单区域组件\nconst FormSection = (param)=>{\n    let { title, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"section-title !text-theme-foreground-muted\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined);\n};\n_c = FormSection;\n// 统一的表单输入组件\nconst FormInput = (param)=>{\n    let { label, required = false, error, hint, children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"text-sm font-medium text-theme-foreground block\",\n                children: [\n                    label,\n                    required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-theme-error ml-1\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 20\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                lineNumber: 71,\n                columnNumber: 5\n            }, undefined),\n            children,\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-theme-error\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            hint && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-theme-foreground-muted\",\n                children: hint\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = FormInput;\nfunction FileUploadModelForm(param) {\n    let { onSave, onCancel, onSuccess } = param;\n    var _formData_files_, _formData_parameters;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        display_name: '',\n        files: [],\n        model_type: 'gguf',\n        upload_method: 'file_path',\n        system_prompt: '',\n        template: '',\n        license: '',\n        parameters: {},\n        quantize: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 验证表单\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.display_name.trim()) {\n            newErrors.display_name = '模型名称不能为空';\n        }\n        if (formData.files.length === 0) {\n            newErrors.files = '请选择模型文件';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // 保存模型\n    const handleSave = async ()=>{\n        if (!validateForm()) return;\n        if (isUploading) return;\n        try {\n            setIsUploading(true);\n            const response = await fetch('/api/models/create-modelfile-from-path', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                console.log('模型创建成功:', result.model);\n                if (onSuccess) {\n                    onSuccess('模型 \"'.concat(formData.display_name, '\" 创建成功！'));\n                }\n                onCancel();\n                return;\n            } else {\n                setErrors((prev)=>({\n                        ...prev,\n                        files: result.error || '创建模型失败'\n                    }));\n            }\n        } catch (error) {\n            console.error('创建模型失败:', error);\n            setErrors((prev)=>({\n                    ...prev,\n                    files: \"创建模型失败: \".concat(error instanceof Error ? error.message : '未知错误')\n                }));\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const modalIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-14 h-14 rounded-2xl bg-gradient-to-br from-theme-primary to-theme-accent flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-7 h-7 text-white\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n    // 根据操作系统生成占位符示例\n    const getPlaceholderPath = ()=>{\n        const platform = navigator.platform.toLowerCase();\n        if (platform.includes('win')) {\n            return '例如: D:\\\\Models\\\\your-model.gguf';\n        } else if (platform.includes('mac')) {\n            return '例如: /Users/<USER>/Models/your-model.gguf';\n        } else {\n            return '例如: /home/<USER>/models/your-model.gguf';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModalWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        isOpen: true,\n        onClose: onCancel,\n        title: \"从文件创建模型\",\n        subtitle: \"选择本地 GGUF 文件来创建自定义模型\",\n        icon: modalIcon,\n        maxWidth: \"2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full min-h-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-8 pb-6 space-y-8 overflow-y-auto scrollbar-thin\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormSection, {\n                            title: \"基本信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                label: \"模型名称\",\n                                required: true,\n                                error: errors.display_name,\n                                hint: \"为您的模型设置一个易于识别的名称\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.display_name,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                display_name: e.target.value\n                                            })),\n                                    className: \"form-input-base\",\n                                    placeholder: \"例如：我的自定义模型\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormSection, {\n                            title: \"模型文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                label: \"GGUF 文件路径\",\n                                required: true,\n                                error: errors.files,\n                                hint: \"请输入 GGUF 文件的完整路径\",\n                                children: [\n                                    errors.files && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-4 bg-theme-error/10 border border-theme-error/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 text-theme-error mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-theme-error\",\n                                                            children: \"路径错误\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-theme-error/80\",\n                                                            children: errors.files\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: ((_formData_files_ = formData.files[0]) === null || _formData_files_ === void 0 ? void 0 : _formData_files_.path) || '',\n                                        onChange: (e)=>{\n                                            const path = e.target.value;\n                                            if (path) {\n                                                const fileName = path.split(/[/\\\\]/).pop() || 'unknown';\n                                                const fileInfo = {\n                                                    file: {},\n                                                    name: fileName,\n                                                    size: 0,\n                                                    path: path,\n                                                    uploadStatus: 'completed',\n                                                    uploadProgress: 100\n                                                };\n                                                setFormData((prev)=>({\n                                                        ...prev,\n                                                        files: [\n                                                            fileInfo\n                                                        ]\n                                                    }));\n                                            } else {\n                                                setFormData((prev)=>({\n                                                        ...prev,\n                                                        files: []\n                                                    }));\n                                            }\n                                        },\n                                        className: \"form-input-base\",\n                                        placeholder: getPlaceholderPath()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 p-3 bg-theme-background-secondary border border-theme-border rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 text-theme-primary flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-theme-foreground truncate\",\n                                                            children: formData.files[0].name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-theme-foreground-muted font-mono break-all\",\n                                                            children: formData.files[0].path\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                files: []\n                                                            })),\n                                                    className: \"p-1 text-theme-foreground-muted hover:text-theme-error transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormSection, {\n                            title: \"高级设置\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                            label: \"量化选项\",\n                                            hint: \"量化可以减少模型大小但可能影响质量\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.quantize || '',\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            quantize: e.target.value\n                                                        })),\n                                                className: \"form-input-base\",\n                                                children: QUANTIZATION_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: option.value,\n                                                        children: option.label\n                                                    }, option.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                            label: \"上下文长度\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                value: String(((_formData_parameters = formData.parameters) === null || _formData_parameters === void 0 ? void 0 : _formData_parameters.num_ctx) || 2048),\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            parameters: {\n                                                                ...prev.parameters,\n                                                                num_ctx: parseInt(e.target.value) || 2048\n                                                            }\n                                                        })),\n                                                className: \"form-input-base\",\n                                                placeholder: \"2048\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                    label: \"系统提示词\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: formData.system_prompt || '',\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    system_prompt: e.target.value\n                                                })),\n                                        className: \"form-input-base h-24 resize-none\",\n                                        placeholder: \"设置模型的系统提示词...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                    label: \"对话模板\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: formData.template || '',\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    template: e.target.value\n                                                })),\n                                        className: \"form-input-base h-20 resize-none font-mono text-sm\",\n                                        placeholder: \"自定义对话格式模板...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormInput, {\n                                    label: \"许可证\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: formData.license || '',\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    license: e.target.value\n                                                })),\n                                        className: \"form-input-base h-16 resize-none\",\n                                        placeholder: \"指定模型的许可证...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 px-8 py-6 border-t border-theme-border bg-theme-background-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-theme-foreground-muted\",\n                                children: formData.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"已选择文件: \",\n                                        formData.files[0].name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onCancel,\n                                        disabled: isUploading,\n                                        className: \"btn-base btn-secondary px-6 py-3\",\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        disabled: isUploading || formData.files.length === 0,\n                                        className: \"btn-base btn-primary px-6 py-3\",\n                                        children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 animate-spin rounded-full border-2 border-white/30 border-t-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"创建中...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Folder_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"创建模型\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\FileUploadModelForm.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(FileUploadModelForm, \"N44wu2A87G5Dp6j7o7/VlpbP6G0=\");\n_c2 = FileUploadModelForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FormSection\");\n$RefreshReg$(_c1, \"FormInput\");\n$RefreshReg$(_c2, \"FileUploadModelForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/model-manager/components/FileUploadModelForm.tsx\n"));

/***/ })

});