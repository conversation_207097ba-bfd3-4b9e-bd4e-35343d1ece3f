"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx":
/*!*************************************************************!*\
  !*** ./src/app/simple-chat/components/chat/MessageList.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageList: () => (/* binding */ MessageList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/ThinkingMode */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/ThinkingMode.tsx\");\n/* harmony import */ var _ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/StreamedContent */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/StreamedContent.tsx\");\n/* harmony import */ var _tools_ToolCallMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tools/ToolCallMessage */ \"(app-pages-browser)/./src/app/simple-chat/components/tools/ToolCallMessage.tsx\");\n/* harmony import */ var _app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/model-manager/components/ModelLogo */ \"(app-pages-browser)/./src/app/model-manager/components/ModelLogo.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MessageList(param) {\n    let { messages, isStreaming, expandedThinkingMessages, onToggleThinkingExpand, chatStyle, selectedModel, customModels } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isNearBottom, setIsNearBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isNearTop, setIsNearTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollButtons, setShowScrollButtons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageCount, setMessageCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(messages.length);\n    const [userScrolled, setUserScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastScrollTime, setLastScrollTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const scrollTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 获取模型的显示信息 - 修复：正确查找customModels\n    const getModelDisplayInfo = (modelName)=>{\n        if (!modelName) return {\n            displayName: 'AI助手',\n            family: 'default'\n        };\n        // 查找对应的自定义模型信息\n        const customModel = customModels === null || customModels === void 0 ? void 0 : customModels.find((m)=>m.base_model === modelName);\n        return {\n            displayName: (customModel === null || customModel === void 0 ? void 0 : customModel.display_name) || modelName,\n            family: (customModel === null || customModel === void 0 ? void 0 : customModel.family) || modelName.split(':')[0] || 'default'\n        };\n    };\n    // 检查滚动位置 - 寻找真正的滚动容器\n    const checkScrollPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[checkScrollPosition]\": ()=>{\n            // 先尝试当前组件的滚动容器\n            let container = scrollContainerRef.current;\n            // 如果当前容器没有滚动条，查找父级的滚动容器\n            if (container && container.scrollHeight <= container.clientHeight) {\n                // 查找最近的可滚动父元素\n                let parent = container.parentElement;\n                while(parent){\n                    if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                        container = parent;\n                        break;\n                    }\n                    parent = parent.parentElement;\n                }\n            }\n            if (!container) return {\n                nearBottom: true,\n                nearTop: true\n            };\n            const { scrollTop, scrollHeight, clientHeight } = container;\n            const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);\n            const distanceFromTop = scrollTop;\n            // 检测是否接近顶部和底部 - 考虑段落间距影响\n            // space-y-4 = 16px，加上padding和其他间距，使用更宽松的阈值\n            const nearBottom = distanceFromBottom <= 50; // 放宽底部检测，应对段落间距\n            const nearTop = distanceFromTop <= 50;\n            // 智能显示按钮：当有足够内容可以滚动时就显示\n            const hasEnoughContentToScroll = scrollHeight > clientHeight + 100; // 内容高度超过容器高度100px以上\n            const showButtons = messages.length > 0 && hasEnoughContentToScroll;\n            setIsNearBottom(nearBottom);\n            setIsNearTop(nearTop);\n            setShowScrollButtons(showButtons);\n            return {\n                nearBottom,\n                nearTop\n            };\n        }\n    }[\"MessageList.useCallback[checkScrollPosition]\"], [\n        messages.length\n    ]);\n    // 滚动到底部 - 优化定位精度\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[scrollToBottom]\": function() {\n            let behavior = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'smooth';\n            var _scrollContainerRef_current;\n            // 方法1：使用 scrollIntoView，但加上 block: 'end' 确保精确定位\n            if (messagesEndRef.current) {\n                messagesEndRef.current.scrollIntoView({\n                    behavior,\n                    block: 'end',\n                    inline: 'nearest'\n                });\n                return;\n            }\n            // 方法2：备用方案，直接滚动到容器底部\n            const container = (_scrollContainerRef_current = scrollContainerRef.current) === null || _scrollContainerRef_current === void 0 ? void 0 : _scrollContainerRef_current.parentElement;\n            if (container && container.scrollHeight > container.clientHeight) {\n                container.scrollTo({\n                    top: container.scrollHeight,\n                    behavior\n                });\n            }\n        }\n    }[\"MessageList.useCallback[scrollToBottom]\"], []);\n    // 滚动到顶部\n    const scrollToTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[scrollToTop]\": function() {\n            let behavior = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'smooth';\n            // 寻找可滚动的容器\n            let container = scrollContainerRef.current;\n            // 如果当前容器不可滚动，查找父级滚动容器\n            if (container && container.scrollHeight <= container.clientHeight) {\n                let parent = container.parentElement;\n                while(parent){\n                    if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                        container = parent;\n                        break;\n                    }\n                    parent = parent.parentElement;\n                }\n            }\n            if (container) {\n                container.scrollTo({\n                    top: 0,\n                    behavior\n                });\n            }\n        }\n    }[\"MessageList.useCallback[scrollToTop]\"], []);\n    // 处理用户滚动 - 增加用户意图检测\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[handleScroll]\": ()=>{\n            const now = Date.now();\n            const timeSinceLastScroll = now - lastScrollTime;\n            // 如果距离上次滚动时间很短，认为是用户主动滚动\n            if (timeSinceLastScroll < 1000) {\n                setUserScrolled(true);\n                // 清除之前的定时器\n                if (scrollTimeoutRef.current) {\n                    clearTimeout(scrollTimeoutRef.current);\n                }\n                // 3秒后重置用户滚动状态\n                scrollTimeoutRef.current = setTimeout({\n                    \"MessageList.useCallback[handleScroll]\": ()=>{\n                        setUserScrolled(false);\n                    }\n                }[\"MessageList.useCallback[handleScroll]\"], 3000);\n            }\n            setLastScrollTime(now);\n            // 直接内联检查滚动位置，避免循环依赖\n            requestAnimationFrame({\n                \"MessageList.useCallback[handleScroll]\": ()=>{\n                    checkScrollPosition();\n                }\n            }[\"MessageList.useCallback[handleScroll]\"]);\n        }\n    }[\"MessageList.useCallback[handleScroll]\"], [\n        lastScrollTime\n    ]); // 移除checkScrollPosition依赖\n    // 监听外层滚动容器的滚动事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            // 找到外层的滚动容器并绑定事件\n            const findScrollContainer = {\n                \"MessageList.useEffect.findScrollContainer\": ()=>{\n                    let current = scrollContainerRef.current;\n                    if (!current) return null;\n                    // 向上找到真正的滚动容器\n                    let parent = current.parentElement;\n                    while(parent){\n                        if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                            return parent;\n                        }\n                        parent = parent.parentElement;\n                    }\n                    return current;\n                }\n            }[\"MessageList.useEffect.findScrollContainer\"];\n            const scrollContainer = findScrollContainer();\n            if (scrollContainer) {\n                scrollContainer.addEventListener('scroll', handleScroll, {\n                    passive: true\n                });\n                return ({\n                    \"MessageList.useEffect\": ()=>{\n                        scrollContainer.removeEventListener('scroll', handleScroll);\n                    }\n                })[\"MessageList.useEffect\"];\n            }\n        }\n    }[\"MessageList.useEffect\"], [\n        handleScroll\n    ]);\n    // 当消息发生变化时的智能滚动逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            const wasNearBottom = isNearBottom;\n            const newMessageCount = messages.length;\n            const hasNewMessages = newMessageCount > messageCount;\n            // 更新消息计数\n            setMessageCount(newMessageCount);\n            // 优化的智能滚动逻辑：\n            // 1. 用户主动滚动时，暂停自动滚动\n            // 2. 只有在接近底部且没有用户干预时才自动滚动\n            // 3. 流式更新使用防抖机制，减少频繁滚动\n            if (!userScrolled && wasNearBottom && (hasNewMessages || isStreaming)) {\n                // 清除之前的滚动定时器，实现防抖\n                if (scrollTimeoutRef.current) {\n                    clearTimeout(scrollTimeoutRef.current);\n                }\n                // 使用防抖延迟，避免频繁滚动导致的抖动\n                const scrollDelay = isStreaming ? 150 : 50; // 流式时更长延迟\n                scrollTimeoutRef.current = setTimeout({\n                    \"MessageList.useEffect\": ()=>{\n                        // 再次检查用户是否在此期间滚动了\n                        if (!userScrolled && isNearBottom) {\n                            // 流式更新时使用 'auto'，新消息时使用 'smooth'\n                            const behavior = isStreaming ? 'auto' : 'smooth';\n                            scrollToBottom(behavior);\n                        }\n                    }\n                }[\"MessageList.useEffect\"], scrollDelay);\n            }\n            // 延迟重新检查位置，避免与滚动冲突\n            setTimeout({\n                \"MessageList.useEffect\": ()=>{\n                    requestAnimationFrame({\n                        \"MessageList.useEffect\": ()=>{\n                            checkScrollPosition();\n                        }\n                    }[\"MessageList.useEffect\"]);\n                }\n            }[\"MessageList.useEffect\"], 200);\n        }\n    }[\"MessageList.useEffect\"], [\n        messages,\n        isStreaming,\n        isNearBottom,\n        messageCount,\n        userScrolled\n    ]);\n    // 初始化时滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            if (messages.length > 0) {\n                // 首次加载时直接滚动到底部\n                requestAnimationFrame({\n                    \"MessageList.useEffect\": ()=>{\n                        scrollToBottom('auto');\n                    }\n                }[\"MessageList.useEffect\"]);\n            }\n        }\n    }[\"MessageList.useEffect\"], []); // 只在组件首次挂载时执行\n    // 组件挂载后立即检查滚动位置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            // 延迟检查确保DOM完全渲染\n            const timer = setTimeout({\n                \"MessageList.useEffect.timer\": ()=>{\n                    // 内联检查逻辑，避免函数依赖\n                    let container = scrollContainerRef.current;\n                    if (container && container.scrollHeight <= container.clientHeight) {\n                        let parent = container.parentElement;\n                        while(parent){\n                            if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                                container = parent;\n                                break;\n                            }\n                            parent = parent.parentElement;\n                        }\n                    }\n                    if (!container) return;\n                    const { scrollTop, scrollHeight, clientHeight } = container;\n                    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);\n                    const distanceFromTop = scrollTop;\n                    const nearBottom = distanceFromBottom <= 50;\n                    const nearTop = distanceFromTop <= 50;\n                    const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;\n                    const showButtons = messages.length > 0 && hasEnoughContentToScroll;\n                    setIsNearBottom(nearBottom);\n                    setIsNearTop(nearTop);\n                    setShowScrollButtons(showButtons);\n                }\n            }[\"MessageList.useEffect.timer\"], 300);\n            return ({\n                \"MessageList.useEffect\": ()=>clearTimeout(timer)\n            })[\"MessageList.useEffect\"];\n        }\n    }[\"MessageList.useEffect\"], [\n        messages.length\n    ]); // 只依赖消息长度\n    // 清理定时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            return ({\n                \"MessageList.useEffect\": ()=>{\n                    if (scrollTimeoutRef.current) {\n                        clearTimeout(scrollTimeoutRef.current);\n                    }\n                }\n            })[\"MessageList.useEffect\"];\n        }\n    }[\"MessageList.useEffect\"], []);\n    // 格式化时间（纳秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    // 渲染生成统计信息图标\n    const renderGenerationStatsIcon = (message)=>{\n        // 检查是否为当前生成中的消息\n        const isCurrentlyGenerating = isStreaming && message.role === 'assistant' && messages.indexOf(message) === messages.length - 1;\n        // 如果有完整的统计数据（至少有总时长或生成token数量），显示详细信息\n        const hasCompleteStats = message.total_duration || message.eval_count;\n        const statsText = hasCompleteStats ? \"总时长: \".concat(formatDuration(message.total_duration), \"\\n\") + \"加载时长: \".concat(formatDuration(message.load_duration), \"\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : isCurrentlyGenerating ? '正在生成中，统计信息将在完成后显示...' : '统计信息不可用';\n        // 获取消息创建时间\n        const messageTime = message.timestamp ? new Date(message.timestamp).toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n        }) : new Date().toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative inline-block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group inline-block\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-theme-foreground-muted hover:text-theme-foreground cursor-help transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-0 bottom-full mb-1 bg-gray-800 text-white text-xs rounded px-3 py-2 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 min-w-max shadow-lg pointer-events-none\",\n                                children: statsText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-theme-foreground-muted\",\n                    children: messageTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                className: \"p-4 space-y-4\",\n                children: [\n                    messages.map((message, index)=>{\n                        // 如果是工具调用占位符消息，渲染工具调用组件\n                        if (message.role === 'tool_call' && message.toolCall) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_ToolCallMessage__WEBPACK_IMPORTED_MODULE_4__.ToolCallMessage, {\n                                toolCall: message.toolCall\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        // 检查消息是否包含思考内容\n                        const hasThinking = message.role === 'assistant' && (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.hasThinkingContent)(message.content);\n                        const contentWithoutThinking = hasThinking ? (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.removeThinkingContent)(message.content) : message.content;\n                        const isCurrentlyThinking = isStreaming && message.role === 'assistant' && index === messages.length - 1 && (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.hasThinkingContent)(message.content) && !(0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.removeThinkingContent)(message.content).trim();\n                        // 🔧 修复：检查是否应该显示消息气泡\n                        // 对于 assistant 消息，如果只有思考内容而没有实际内容，且不是正在生成状态，则不显示消息气泡\n                        const isLastMessage = index === messages.length - 1;\n                        const isGenerating = isStreaming && message.role === 'assistant' && isLastMessage;\n                        const hasActualContent = contentWithoutThinking.trim().length > 0;\n                        const shouldShowBubble = message.role === 'user' || hasActualContent || isGenerating && !isCurrentlyThinking;\n                        // 获取模型显示信息\n                        const modelDisplayInfo = getModelDisplayInfo(message.model || selectedModel);\n                        // 根据聊天样式决定布局\n                        if (chatStyle === 'conversation') {\n                            // 对话模式：用户右侧，AI左侧\n                            const isUser = message.role === 'user';\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 \".concat(isUser ? 'flex-row-reverse' : ''),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isUser ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            modelName: modelDisplayInfo.family,\n                                            size: \"lg\",\n                                            containerSize: 40,\n                                            imageSize: 32,\n                                            className: \"bg-transparent border-0 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] space-y-2 \".concat(isUser ? 'flex flex-col items-end' : ''),\n                                        children: [\n                                            (shouldShowBubble || hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 \".concat(isUser ? 'justify-end' : 'justify-start'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-theme-foreground-muted \".concat(isUser ? 'text-right' : 'text-left'),\n                                                        children: isUser ? '你' : modelDisplayInfo.displayName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !isUser && message.role === 'assistant' && renderGenerationStatsIcon(message)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.ThinkingMode, {\n                                                content: message.content,\n                                                isExpanded: expandedThinkingMessages.has(message.id),\n                                                onToggleExpand: ()=>onToggleThinkingExpand(message.id),\n                                                defaultHidden: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this),\n                                            shouldShowBubble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-block p-3 rounded-lg \".concat(isUser ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    content: contentWithoutThinking || (isGenerating && !isCurrentlyThinking ? '正在生成回复...' : ''),\n                                                    isStreaming: isGenerating,\n                                                    enableMarkdown: !isUser,\n                                                    className: !isUser ? \"break-words leading-[1.4]\" : \"break-words whitespace-pre-wrap leading-[1.4]\",\n                                                    style: {\n                                                        minWidth: 0,\n                                                        maxWidth: '100%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, message.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 15\n                            }, this);\n                        } else {\n                            // 助手模式：所有消息都在左侧\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.role === 'user' ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                        children: message.role === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            modelName: modelDisplayInfo.family,\n                                            size: \"lg\",\n                                            containerSize: 40,\n                                            imageSize: 32,\n                                            className: \"bg-transparent border-0 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 space-y-2\",\n                                        children: [\n                                            (shouldShowBubble || hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-theme-foreground-muted\",\n                                                        children: message.role === 'user' ? '你' : modelDisplayInfo.displayName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    message.role === 'assistant' && renderGenerationStatsIcon(message)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.ThinkingMode, {\n                                                content: message.content,\n                                                isExpanded: expandedThinkingMessages.has(message.id),\n                                                onToggleExpand: ()=>onToggleThinkingExpand(message.id),\n                                                defaultHidden: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 21\n                                            }, this),\n                                            shouldShowBubble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none text-theme-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    content: contentWithoutThinking || (isGenerating && !isCurrentlyThinking ? '正在生成回复...' : ''),\n                                                    isStreaming: isGenerating,\n                                                    enableMarkdown: message.role === 'assistant',\n                                                    className: message.role === 'assistant' ? \"break-words leading-[1.4]\" : \"break-words whitespace-pre-wrap leading-[1.4]\",\n                                                    style: {\n                                                        minWidth: 0,\n                                                        maxWidth: '100%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, message.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, this);\n                        }\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            showScrollButtons && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group fixed bottom-40 right-12 z-50 flex flex-col gap-1 p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-16 h-full -right-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-1 opacity-0 translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300 ease-out\",\n                        children: [\n                            !isNearTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToTop('smooth'),\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"回到顶部\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const { nearBottom, nearTop } = checkScrollPosition();\n                                    console.log('当前状态:', {\n                                        isNearTop,\n                                        isNearBottom,\n                                        nearTop,\n                                        nearBottom,\n                                        showScrollButtons\n                                    });\n                                },\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"调试信息\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            !isNearBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToBottom('smooth'),\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"回到底部\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 522,\n                columnNumber: 10\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n        lineNumber: 356,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageList, \"kAtPFrOasN3r8MRT6w+/YHio3xY=\");\n_c = MessageList;\nvar _c;\n$RefreshReg$(_c, \"MessageList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx\n"));

/***/ })

});