/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/models/route";
exports.ids = ["app/api/models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/models/route.ts */ \"(rsc)/./src/app/api/models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/models/route\",\n        pathname: \"/api/models\",\n        filename: \"route\",\n        bundlePath: \"app/api/models/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\models\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/models/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/models/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/ollama */ \"(rsc)/./src/lib/ollama.ts\");\n\n\nasync function GET(request) {\n    try {\n        // 检查Ollama服务是否可用\n        const isAvailable = await _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.isAvailable();\n        if (!isAvailable) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Ollama服务不可用',\n                message: '请确保Ollama正在运行并监听在localhost:11434端口'\n            }, {\n                status: 503\n            });\n        }\n        // 获取模型列表\n        const models = await _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.getModels();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            models: models.map((model)=>({\n                    name: model.name,\n                    model: model.model,\n                    size: model.size,\n                    modified_at: model.modified_at,\n                    details: model.details,\n                    // 添加格式化的信息\n                    displayName: model.name.split(':')[0],\n                    formattedSize: formatModelSize(model.size),\n                    parameterSize: model.details.parameter_size,\n                    quantization: model.details.quantization_level\n                }))\n        });\n    } catch (error) {\n        console.error('获取模型列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '获取模型列表失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 格式化模型大小的辅助函数\nfunction formatModelSize(bytes) {\n    const units = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    let size = bytes;\n    let unitIndex = 0;\n    while(size >= 1024 && unitIndex < units.length - 1){\n        size /= 1024;\n        unitIndex++;\n    }\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9tb2RlbHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdEO0FBQ0w7QUFFNUMsZUFBZUUsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLGlCQUFpQjtRQUNqQixNQUFNQyxjQUFjLE1BQU1ILHFEQUFZQSxDQUFDRyxXQUFXO1FBQ2xELElBQUksQ0FBQ0EsYUFBYTtZQUNoQixPQUFPSixxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtnQkFDRUMsT0FBTztnQkFDUEMsU0FBUztZQUNYLEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLFNBQVM7UUFDVCxNQUFNQyxTQUFTLE1BQU1SLHFEQUFZQSxDQUFDUyxTQUFTO1FBRTNDLE9BQU9WLHFEQUFZQSxDQUFDSyxJQUFJLENBQUM7WUFDdkJNLFNBQVM7WUFDVEYsUUFBUUEsT0FBT0csR0FBRyxDQUFDQyxDQUFBQSxRQUFVO29CQUMzQkMsTUFBTUQsTUFBTUMsSUFBSTtvQkFDaEJELE9BQU9BLE1BQU1BLEtBQUs7b0JBQ2xCRSxNQUFNRixNQUFNRSxJQUFJO29CQUNoQkMsYUFBYUgsTUFBTUcsV0FBVztvQkFDOUJDLFNBQVNKLE1BQU1JLE9BQU87b0JBQ3RCLFdBQVc7b0JBQ1hDLGFBQWFMLE1BQU1DLElBQUksQ0FBQ0ssS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29CQUNyQ0MsZUFBZUMsZ0JBQWdCUixNQUFNRSxJQUFJO29CQUN6Q08sZUFBZVQsTUFBTUksT0FBTyxDQUFDTSxjQUFjO29CQUMzQ0MsY0FBY1gsTUFBTUksT0FBTyxDQUFDUSxrQkFBa0I7Z0JBQ2hEO1FBQ0Y7SUFDRixFQUFFLE9BQU9uQixPQUFPO1FBQ2RvQixRQUFRcEIsS0FBSyxDQUFDLGFBQWFBO1FBRTNCLE9BQU9OLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO1lBQ0VDLE9BQU87WUFDUEMsU0FBU0QsaUJBQWlCcUIsUUFBUXJCLE1BQU1DLE9BQU8sR0FBRztRQUNwRCxHQUNBO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsZUFBZTtBQUNmLFNBQVNhLGdCQUFnQk8sS0FBYTtJQUNwQyxNQUFNQyxRQUFRO1FBQUM7UUFBSztRQUFNO1FBQU07UUFBTTtLQUFLO0lBQzNDLElBQUlkLE9BQU9hO0lBQ1gsSUFBSUUsWUFBWTtJQUVoQixNQUFPZixRQUFRLFFBQVFlLFlBQVlELE1BQU1FLE1BQU0sR0FBRyxFQUFHO1FBQ25EaEIsUUFBUTtRQUNSZTtJQUNGO0lBRUEsT0FBTyxHQUFHZixLQUFLaUIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxFQUFFSCxLQUFLLENBQUNDLFVBQVUsRUFBRTtBQUNqRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxhcHBcXGFwaVxcbW9kZWxzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgb2xsYW1hQ2xpZW50IH0gZnJvbSAnLi4vLi4vLi4vbGliL29sbGFtYSc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICAvLyDmo4Dmn6VPbGxhbWHmnI3liqHmmK/lkKblj6/nlKhcbiAgICBjb25zdCBpc0F2YWlsYWJsZSA9IGF3YWl0IG9sbGFtYUNsaWVudC5pc0F2YWlsYWJsZSgpO1xuICAgIGlmICghaXNBdmFpbGFibGUpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBcbiAgICAgICAgICBlcnJvcjogJ09sbGFtYeacjeWKoeS4jeWPr+eUqCcsXG4gICAgICAgICAgbWVzc2FnZTogJ+ivt+ehruS/nU9sbGFtYeato+WcqOi/kOihjOW5tuebkeWQrOWcqGxvY2FsaG9zdDoxMTQzNOerr+WPoydcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMyB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIOiOt+WPluaooeWei+WIl+ihqFxuICAgIGNvbnN0IG1vZGVscyA9IGF3YWl0IG9sbGFtYUNsaWVudC5nZXRNb2RlbHMoKTtcbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1vZGVsczogbW9kZWxzLm1hcChtb2RlbCA9PiAoe1xuICAgICAgICBuYW1lOiBtb2RlbC5uYW1lLFxuICAgICAgICBtb2RlbDogbW9kZWwubW9kZWwsXG4gICAgICAgIHNpemU6IG1vZGVsLnNpemUsXG4gICAgICAgIG1vZGlmaWVkX2F0OiBtb2RlbC5tb2RpZmllZF9hdCxcbiAgICAgICAgZGV0YWlsczogbW9kZWwuZGV0YWlscyxcbiAgICAgICAgLy8g5re75Yqg5qC85byP5YyW55qE5L+h5oGvXG4gICAgICAgIGRpc3BsYXlOYW1lOiBtb2RlbC5uYW1lLnNwbGl0KCc6JylbMF0sXG4gICAgICAgIGZvcm1hdHRlZFNpemU6IGZvcm1hdE1vZGVsU2l6ZShtb2RlbC5zaXplKSxcbiAgICAgICAgcGFyYW1ldGVyU2l6ZTogbW9kZWwuZGV0YWlscy5wYXJhbWV0ZXJfc2l6ZSxcbiAgICAgICAgcXVhbnRpemF0aW9uOiBtb2RlbC5kZXRhaWxzLnF1YW50aXphdGlvbl9sZXZlbCxcbiAgICAgIH0pKVxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaooeWei+WIl+ihqOWksei0pTonLCBlcnJvcik7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgZXJyb3I6ICfojrflj5bmqKHlnovliJfooajlpLHotKUnLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmnKrnn6XplJnor68nXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyDmoLzlvI/ljJbmqKHlnovlpKflsI/nmoTovoXliqnlh73mlbBcbmZ1bmN0aW9uIGZvcm1hdE1vZGVsU2l6ZShieXRlczogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3QgdW5pdHMgPSBbJ0InLCAnS0InLCAnTUInLCAnR0InLCAnVEInXTtcbiAgbGV0IHNpemUgPSBieXRlcztcbiAgbGV0IHVuaXRJbmRleCA9IDA7XG5cbiAgd2hpbGUgKHNpemUgPj0gMTAyNCAmJiB1bml0SW5kZXggPCB1bml0cy5sZW5ndGggLSAxKSB7XG4gICAgc2l6ZSAvPSAxMDI0O1xuICAgIHVuaXRJbmRleCsrO1xuICB9XG5cbiAgcmV0dXJuIGAke3NpemUudG9GaXhlZCgxKX0gJHt1bml0c1t1bml0SW5kZXhdfWA7XG59Il0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIm9sbGFtYUNsaWVudCIsIkdFVCIsInJlcXVlc3QiLCJpc0F2YWlsYWJsZSIsImpzb24iLCJlcnJvciIsIm1lc3NhZ2UiLCJzdGF0dXMiLCJtb2RlbHMiLCJnZXRNb2RlbHMiLCJzdWNjZXNzIiwibWFwIiwibW9kZWwiLCJuYW1lIiwic2l6ZSIsIm1vZGlmaWVkX2F0IiwiZGV0YWlscyIsImRpc3BsYXlOYW1lIiwic3BsaXQiLCJmb3JtYXR0ZWRTaXplIiwiZm9ybWF0TW9kZWxTaXplIiwicGFyYW1ldGVyU2l6ZSIsInBhcmFtZXRlcl9zaXplIiwicXVhbnRpemF0aW9uIiwicXVhbnRpemF0aW9uX2xldmVsIiwiY29uc29sZSIsIkVycm9yIiwiYnl0ZXMiLCJ1bml0cyIsInVuaXRJbmRleCIsImxlbmd0aCIsInRvRml4ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/models/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaClient: () => (/* binding */ OllamaClient),\n/* harmony export */   ollamaClient: () => (/* binding */ ollamaClient)\n/* harmony export */ });\n// Ollama API 客户端\nconst OLLAMA_BASE_URL = 'http://localhost:11434';\nclass OllamaClient {\n    constructor(baseUrl = OLLAMA_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    /**\n   * 获取本地可用的模型列表\n   */ async getModels() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const data = await response.json();\n            return data.models || [];\n        } catch (error) {\n            console.error('获取模型列表失败:', error);\n            throw new Error('无法连接到Ollama服务，请确保Ollama正在运行');\n        }\n    }\n    /**\n   * 获取指定模型的详细信息\n   */ async getModelDetails(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/show`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: modelName\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`获取模型 '${modelName}' 详细信息失败:`, response.status, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const details = await response.json();\n            // 改进的系统提示词提取逻辑\n            let systemPrompt = '';\n            if (details.modelfile) {\n                // 尝试多种 SYSTEM 指令格式（不区分大小写）\n                const patterns = [\n                    // 三引号格式：SYSTEM \"\"\"content\"\"\"\n                    /(?:SYSTEM|system)\\s+\"\"\"([\\s\\S]*?)\"\"\"/i,\n                    // 双引号格式：SYSTEM \"content\"\n                    /(?:SYSTEM|system)\\s+\"([^\"]*?)\"/i,\n                    // 单引号格式：SYSTEM 'content'\n                    /(?:SYSTEM|system)\\s+'([^']*?)'/i,\n                    // 无引号格式（到行尾）：SYSTEM content\n                    /(?:SYSTEM|system)\\s+([^\\n\\r]*)/i\n                ];\n                for (const pattern of patterns){\n                    const match = details.modelfile.match(pattern);\n                    if (match && match[1].trim()) {\n                        systemPrompt = match[1].trim();\n                        break;\n                    }\n                }\n            }\n            details.system = systemPrompt;\n            return details;\n        } catch (error) {\n            console.error(`请求模型 '${modelName}' 详细信息时出错:`, error);\n            throw new Error(`无法获取模型 '${modelName}' 的详细信息`);\n        }\n    }\n    /**\n   * 发送聊天请求（非流式）\n   */ async chat(request) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('聊天请求失败:', error);\n            throw new Error('聊天请求失败，请检查网络连接和Ollama服务状态');\n        }\n    }\n    /**\n   * 发送流式聊天请求\n   */ async *chatStream(request) {\n        try {\n            // console.log('Ollama chatStream 请求:', JSON.stringify(request, null, 2));\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: true\n                })\n            });\n            // console.log('Ollama 响应状态:', response.status, response.statusText);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            if (!response.body) {\n                throw new Error('响应体为空');\n            }\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let buffer = '';\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    buffer += decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = buffer.split('\\n');\n                    // 保留最后一行（可能不完整）\n                    buffer = lines.pop() || '';\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine) {\n                            try {\n                                const data = JSON.parse(trimmedLine);\n                                yield data;\n                                // 如果收到完成标志，结束生成\n                                if (data.done) {\n                                    return;\n                                }\n                            } catch (parseError) {\n                                console.warn('解析JSON失败:', parseError, '原始数据:', trimmedLine);\n                            }\n                        }\n                    }\n                }\n                // 处理缓冲区中剩余的数据\n                if (buffer.trim()) {\n                    try {\n                        const data = JSON.parse(buffer.trim());\n                        yield data;\n                    } catch (parseError) {\n                        console.warn('解析最后的JSON失败:', parseError);\n                    }\n                }\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.error('流式聊天请求失败:', error);\n            if (error instanceof Error) {\n                throw error; // 保持原始错误信息\n            } else {\n                throw new Error('流式聊天请求失败，请检查网络连接和Ollama服务状态');\n            }\n        }\n    }\n    /**\n   * 检查Ollama服务是否可用\n   */ async isAvailable() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                signal: AbortSignal.timeout(5000)\n            });\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    }\n    /**\n   * 检查指定模型是否已加载到内存中\n   */ async isModelLoaded(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/ps`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                console.error('获取模型状态失败:', response.status, response.statusText);\n                return false;\n            }\n            const data = await response.json();\n            const loadedModels = data.models || [];\n            // 检查指定模型是否在已加载的模型列表中\n            return loadedModels.some((model)=>model.name === modelName);\n        } catch (error) {\n            console.error('检查模型加载状态失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 格式化模型大小\n   */ static formatModelSize(bytes) {\n        const units = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        let size = bytes;\n        let unitIndex = 0;\n        while(size >= 1024 && unitIndex < units.length - 1){\n            size /= 1024;\n            unitIndex++;\n        }\n        return `${size.toFixed(1)} ${units[unitIndex]}`;\n    }\n    /**\n   * 格式化模型名称（移除标签）\n   */ static formatModelName(name) {\n        return name.split(':')[0];\n    }\n}\n// 默认客户端实例\nconst ollamaClient = new OllamaClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ollama.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();