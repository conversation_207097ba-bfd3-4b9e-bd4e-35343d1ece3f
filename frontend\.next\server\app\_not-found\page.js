/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/examples/NotificationDemo.tsx */ \"(rsc)/./src/components/notification/examples/NotificationDemo.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContainer.tsx */ \"(rsc)/./src/components/notification/NotificationContainer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContext.tsx */ \"(rsc)/./src/components/notification/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationItem.tsx */ \"(rsc)/./src/components/notification/NotificationItem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationManager.tsx */ \"(rsc)/./src/components/notification/NotificationManager.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/components/ColorThemeScript.tsx */ \"(rsc)/./src/theme/components/ColorThemeScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/contexts/ThemeContext.tsx */ \"(rsc)/./src/theme/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"378dbfee842a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNzhkYmZlZTg0MmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/theme/contexts/ThemeContext */ \"(rsc)/./src/theme/contexts/ThemeContext.tsx\");\n/* harmony import */ var _theme_components_ThemeScript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/theme/components/ThemeScript */ \"(rsc)/./src/theme/components/ThemeScript.tsx\");\n/* harmony import */ var _theme_components_ColorThemeScript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/theme/components/ColorThemeScript */ \"(rsc)/./src/theme/components/ColorThemeScript.tsx\");\n/* harmony import */ var _components_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notification */ \"(rsc)/./src/components/notification/index.ts\");\n/* harmony import */ var _components_notification_NotificationManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/notification/NotificationManager */ \"(rsc)/./src/components/notification/NotificationManager.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'Kun Agent',\n    description: '智能对话助手'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeScript__WEBPACK_IMPORTED_MODULE_3__.ThemeScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ColorThemeScript__WEBPACK_IMPORTED_MODULE_4__.ColorThemeScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className)} bg-theme-background text-theme-foreground transition-opacity duration-200`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__.NotificationProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification_NotificationManager__WEBPACK_IMPORTED_MODULE_6__.NotificationManager, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationContainer.tsx":
/*!***************************************************************!*\
  !*** ./src/components/notification/NotificationContainer.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationContainer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationContainer() from the server but NotificationContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContainer.tsx",
"NotificationContainer",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationContext.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationContext.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),
/* harmony export */   useNotification: () => (/* binding */ useNotification)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx",
"NotificationProvider",
);const useNotification = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useNotification() from the server but useNotification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx",
"useNotification",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationItem.tsx":
/*!**********************************************************!*\
  !*** ./src/components/notification/NotificationItem.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationItem: () => (/* binding */ NotificationItem)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationItem = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationItem() from the server but NotificationItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationItem.tsx",
"NotificationItem",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationManager.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationManager.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationManager: () => (/* binding */ NotificationManager)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationManager = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationManager() from the server but NotificationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationManager.tsx",
"NotificationManager",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/examples/NotificationDemo.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/notification/examples/NotificationDemo.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationDemo: () => (/* binding */ NotificationDemo)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationDemo = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationDemo() from the server but NotificationDemo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\examples\\NotificationDemo.tsx",
"NotificationDemo",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/index.ts":
/*!**********************************************!*\
  !*** ./src/components/notification/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContainer: () => (/* reexport safe */ _NotificationContainer__WEBPACK_IMPORTED_MODULE_2__.NotificationContainer),\n/* harmony export */   NotificationDemo: () => (/* reexport safe */ _examples_NotificationDemo__WEBPACK_IMPORTED_MODULE_5__.NotificationDemo),\n/* harmony export */   NotificationItem: () => (/* reexport safe */ _NotificationItem__WEBPACK_IMPORTED_MODULE_3__.NotificationItem),\n/* harmony export */   NotificationManager: () => (/* reexport safe */ _NotificationManager__WEBPACK_IMPORTED_MODULE_4__.NotificationManager),\n/* harmony export */   NotificationProvider: () => (/* reexport safe */ _NotificationContext__WEBPACK_IMPORTED_MODULE_1__.NotificationProvider),\n/* harmony export */   \"default\": () => (/* reexport safe */ _NotificationContext__WEBPACK_IMPORTED_MODULE_1__.NotificationProvider),\n/* harmony export */   useNotification: () => (/* reexport safe */ _NotificationContext__WEBPACK_IMPORTED_MODULE_1__.useNotification)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/components/notification/types.ts\");\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NotificationContext */ \"(rsc)/./src/components/notification/NotificationContext.tsx\");\n/* harmony import */ var _NotificationContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationContainer */ \"(rsc)/./src/components/notification/NotificationContainer.tsx\");\n/* harmony import */ var _NotificationItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationItem */ \"(rsc)/./src/components/notification/NotificationItem.tsx\");\n/* harmony import */ var _NotificationManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationManager */ \"(rsc)/./src/components/notification/NotificationManager.tsx\");\n/* harmony import */ var _examples_NotificationDemo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./examples/NotificationDemo */ \"(rsc)/./src/components/notification/examples/NotificationDemo.tsx\");\n\n\n\n\n\n// 演示组件\n\n// 默认导出一个组合组件\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QjtBQUNjO0FBQ0U7QUFDTDtBQUN5QjtBQUU1RCxPQUFPO0FBQ3dEO0FBRS9ELGFBQWE7QUFDMkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcbm90aWZpY2F0aW9uXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3R5cGVzJztcclxuZXhwb3J0ICogZnJvbSAnLi9Ob3RpZmljYXRpb25Db250ZXh0JztcclxuZXhwb3J0ICogZnJvbSAnLi9Ob3RpZmljYXRpb25Db250YWluZXInO1xyXG5leHBvcnQgKiBmcm9tICcuL05vdGlmaWNhdGlvbkl0ZW0nO1xyXG5leHBvcnQgeyBOb3RpZmljYXRpb25NYW5hZ2VyIH0gZnJvbSAnLi9Ob3RpZmljYXRpb25NYW5hZ2VyJztcclxuXHJcbi8vIOa8lOekuue7hOS7tlxyXG5leHBvcnQgeyBOb3RpZmljYXRpb25EZW1vIH0gZnJvbSAnLi9leGFtcGxlcy9Ob3RpZmljYXRpb25EZW1vJztcclxuXHJcbi8vIOm7mOiupOWvvOWHuuS4gOS4que7hOWQiOe7hOS7tlxyXG5leHBvcnQgeyBOb3RpZmljYXRpb25Qcm92aWRlciBhcyBkZWZhdWx0IH0gZnJvbSAnLi9Ob3RpZmljYXRpb25Db250ZXh0JzsgIl0sIm5hbWVzIjpbIk5vdGlmaWNhdGlvbk1hbmFnZXIiLCJOb3RpZmljYXRpb25EZW1vIiwiTm90aWZpY2F0aW9uUHJvdmlkZXIiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/notification/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/notification/types.ts":
/*!**********************************************!*\
  !*** ./src/components/notification/types.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/notification/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/theme/components/ColorThemeScript.tsx":
/*!***************************************************!*\
  !*** ./src/theme/components/ColorThemeScript.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ColorThemeScript: () => (/* binding */ ColorThemeScript)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ColorThemeScript = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ColorThemeScript() from the server but ColorThemeScript is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ColorThemeScript.tsx",
"ColorThemeScript",
);

/***/ }),

/***/ "(rsc)/./src/theme/components/ThemeScript.tsx":
/*!**********************************************!*\
  !*** ./src/theme/components/ThemeScript.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeScript: () => (/* binding */ ThemeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// 主题预加载脚本组件\n// 这个组件会在页面加载前立即执行，防止主题闪烁\n\nfunction ThemeScript() {\n    const initScript = `\n    (function() {\n      try {\n        // 1. 主题处理 - 优先从 localStorage 获取主题\n        const savedTheme = localStorage.getItem('theme');\n        \n        // 如果没有保存的主题，则根据系统偏好设置\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        \n        // 决定最终主题\n        const theme = savedTheme || systemTheme;\n        \n        // 立即在 <html> 元素上应用主题类\n        document.documentElement.classList.add(theme);\n\n        // 如果没有保存过主题，则将当前主题存入 localStorage\n        if (!savedTheme) {\n          localStorage.setItem('theme', theme);\n        }\n\n        // 2. 侧边栏状态处理 - 立即设置状态避免闪烁\n        const sidebarExpanded = localStorage.getItem('sidebar-expanded');\n        const isExpanded = sidebarExpanded !== null ? JSON.parse(sidebarExpanded) : true;\n        \n        // 立即设置侧边栏状态，避免任何延迟\n        document.documentElement.setAttribute('data-sidebar-state', isExpanded ? 'expanded' : 'collapsed');\n        \n      } catch (e) {\n        // 如果出现错误，默认使用浅色主题和展开的侧边栏\n        document.documentElement.classList.add('light');\n        document.documentElement.setAttribute('data-sidebar-state', 'expanded');\n      }\n    })();\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: initScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeScript.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/theme/components/ThemeScript.tsx\n");

/***/ }),

/***/ "(rsc)/./src/theme/contexts/ThemeContext.tsx":
/*!*********************************************!*\
  !*** ./src/theme/contexts/ThemeContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme),
/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx",
"useTheme",
);const useThemeToggle = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useThemeToggle() from the server but useThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx",
"useThemeToggle",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/examples/NotificationDemo.tsx */ \"(ssr)/./src/components/notification/examples/NotificationDemo.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContainer.tsx */ \"(ssr)/./src/components/notification/NotificationContainer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContext.tsx */ \"(ssr)/./src/components/notification/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationItem.tsx */ \"(ssr)/./src/components/notification/NotificationItem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationManager.tsx */ \"(ssr)/./src/components/notification/NotificationManager.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/components/ColorThemeScript.tsx */ \"(ssr)/./src/theme/components/ColorThemeScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/contexts/ThemeContext.tsx */ \"(ssr)/./src/theme/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationContainer.tsx":
/*!***************************************************************!*\
  !*** ./src/components/notification/NotificationContainer.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _NotificationItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationItem */ \"(ssr)/./src/components/notification/NotificationItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationContainer auto */ \n\n\n\nfunction NotificationContainer({ notifications, onDismiss, position = 'top-right', maxNotifications = 5 }) {\n    // 限制显示的通知数量\n    const visibleNotifications = notifications.slice(0, maxNotifications);\n    // 根据位置获取容器样式\n    const getContainerStyles = ()=>{\n        const baseStyles = 'fixed z-50 flex flex-col gap-3 p-4 pointer-events-none';\n        const positionStyles = {\n            'top-right': 'top-0 right-0',\n            'top-left': 'top-0 left-0',\n            'bottom-right': 'bottom-0 right-0',\n            'bottom-left': 'bottom-0 left-0'\n        };\n        return `${baseStyles} ${positionStyles[position]}`;\n    };\n    // 根据位置决定通知堆叠顺序\n    const orderedNotifications = position.includes('bottom') ? [\n        ...visibleNotifications\n    ].reverse() : visibleNotifications;\n    // 居中弹窗样式\n    const getCenterModalStyles = ()=>'fixed z-[9999] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center pointer-events-auto';\n    if (notifications.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: notifications.filter((n)=>n.actions && n.actions.length > 0).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: getCenterModalStyles(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationItem__WEBPACK_IMPORTED_MODULE_2__.NotificationItem, {\n                            notification: notification,\n                            onDismiss: onDismiss,\n                            position: position\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this)\n                    }, notification.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: getContainerStyles(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                        mode: \"popLayout\",\n                        children: orderedNotifications.filter((n)=>!(n.actions && n.actions.length > 0)).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pointer-events-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationItem__WEBPACK_IMPORTED_MODULE_2__.NotificationItem, {\n                                    notification: notification,\n                                    onDismiss: onDismiss,\n                                    position: position\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    notifications.length > maxNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-theme-background-secondary border border-theme-border rounded-xl p-3 shadow-lg backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-theme-foreground-muted text-center\",\n                                    children: [\n                                        \"还有 \",\n                                        notifications.length - maxNotifications,\n                                        \" 条通知...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // 清除所有超出显示限制的通知\n                                        notifications.slice(maxNotifications).forEach((notification)=>{\n                                            onDismiss(notification.id);\n                                        });\n                                    },\n                                    className: \"w-full mt-2 px-3 py-1.5 text-xs font-medium text-theme-foreground-muted hover:text-theme-foreground bg-theme-background-tertiary hover:bg-theme-card-hover rounded-lg transition-colors\",\n                                    children: \"清除所有\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationContext.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationContext.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NotificationProvider,useNotification auto */ \n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction NotificationProvider({ children, maxNotifications = 10, defaultDuration = 4000 }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 生成唯一ID\n    const generateId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[generateId]\": ()=>{\n            return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n        }\n    }[\"NotificationProvider.useCallback[generateId]\"], []);\n    // 添加通知\n    const show = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[show]\": (config)=>{\n            const id = generateId();\n            const notification = {\n                id,\n                type: config.type,\n                title: config.title,\n                message: config.message,\n                duration: config.duration ?? defaultDuration,\n                dismissible: config.dismissible ?? true,\n                actions: config.actions,\n                icon: config.icon,\n                timestamp: Date.now()\n            };\n            setNotifications({\n                \"NotificationProvider.useCallback[show]\": (prev)=>{\n                    const newNotifications = [\n                        notification,\n                        ...prev\n                    ];\n                    // 限制通知数量，移除最旧的通知\n                    return newNotifications.slice(0, maxNotifications);\n                }\n            }[\"NotificationProvider.useCallback[show]\"]);\n            return id;\n        }\n    }[\"NotificationProvider.useCallback[show]\"], [\n        generateId,\n        defaultDuration,\n        maxNotifications\n    ]);\n    // 便捷方法\n    const success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[success]\": (title, message, options)=>{\n            return show({\n                type: 'success',\n                title,\n                message,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[success]\"], [\n        show\n    ]);\n    const error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[error]\": (title, message, options)=>{\n            return show({\n                type: 'error',\n                title,\n                message,\n                duration: options?.duration ?? 6000,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[error]\"], [\n        show\n    ]);\n    const warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[warning]\": (title, message, options)=>{\n            return show({\n                type: 'warning',\n                title,\n                message,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[warning]\"], [\n        show\n    ]);\n    const info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[info]\": (title, message, options)=>{\n            return show({\n                type: 'info',\n                title,\n                message,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[info]\"], [\n        show\n    ]);\n    // 移除指定通知\n    const dismiss = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[dismiss]\": (id)=>{\n            setNotifications({\n                \"NotificationProvider.useCallback[dismiss]\": (prev)=>prev.filter({\n                        \"NotificationProvider.useCallback[dismiss]\": (notification)=>notification.id !== id\n                    }[\"NotificationProvider.useCallback[dismiss]\"])\n            }[\"NotificationProvider.useCallback[dismiss]\"]);\n        }\n    }[\"NotificationProvider.useCallback[dismiss]\"], []);\n    // 清除所有通知\n    const dismissAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[dismissAll]\": ()=>{\n            setNotifications([]);\n        }\n    }[\"NotificationProvider.useCallback[dismissAll]\"], []);\n    const value = {\n        notifications,\n        show,\n        success,\n        error,\n        warning,\n        info,\n        dismiss,\n        dismissAll\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Hook for using notifications\nfunction useNotification() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (!context) {\n        throw new Error('useNotification must be used within a NotificationProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vTm90aWZpY2F0aW9uQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVnRjtBQUdoRixNQUFNSyxvQ0FBc0JKLG9EQUFhQSxDQUFzQ0s7QUFReEUsU0FBU0MscUJBQXFCLEVBQ25DQyxRQUFRLEVBQ1JDLG1CQUFtQixFQUFFLEVBQ3JCQyxrQkFBa0IsSUFBSSxFQUNJO0lBQzFCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdULCtDQUFRQSxDQUFpQixFQUFFO0lBRXJFLFNBQVM7SUFDVCxNQUFNVSxhQUFhVCxrREFBV0E7d0RBQUM7WUFDN0IsT0FBTyxDQUFDLGFBQWEsRUFBRVUsS0FBS0MsR0FBRyxHQUFHLENBQUMsRUFBRUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUcsSUFBSTtRQUNoRjt1REFBRyxFQUFFO0lBRUwsT0FBTztJQUNQLE1BQU1DLE9BQU9oQixrREFBV0E7a0RBQUMsQ0FBQ2lCO1lBQ3hCLE1BQU1DLEtBQUtUO1lBQ1gsTUFBTVUsZUFBNkI7Z0JBQ2pDRDtnQkFDQUUsTUFBTUgsT0FBT0csSUFBSTtnQkFDakJDLE9BQU9KLE9BQU9JLEtBQUs7Z0JBQ25CQyxTQUFTTCxPQUFPSyxPQUFPO2dCQUN2QkMsVUFBVU4sT0FBT00sUUFBUSxJQUFJakI7Z0JBQzdCa0IsYUFBYVAsT0FBT08sV0FBVyxJQUFJO2dCQUNuQ0MsU0FBU1IsT0FBT1EsT0FBTztnQkFDdkJDLE1BQU1ULE9BQU9TLElBQUk7Z0JBQ2pCQyxXQUFXakIsS0FBS0MsR0FBRztZQUNyQjtZQUVBSDswREFBaUJvQixDQUFBQTtvQkFDZixNQUFNQyxtQkFBbUI7d0JBQUNWOzJCQUFpQlM7cUJBQUs7b0JBQ2hELGlCQUFpQjtvQkFDakIsT0FBT0MsaUJBQWlCQyxLQUFLLENBQUMsR0FBR3pCO2dCQUNuQzs7WUFFQSxPQUFPYTtRQUNUO2lEQUFHO1FBQUNUO1FBQVlIO1FBQWlCRDtLQUFpQjtJQUVsRCxPQUFPO0lBQ1AsTUFBTTBCLFVBQVUvQixrREFBV0E7cURBQUMsQ0FDMUJxQixPQUNBQyxTQUNBVTtZQUVBLE9BQU9oQixLQUFLO2dCQUNWSSxNQUFNO2dCQUNOQztnQkFDQUM7Z0JBQ0EsR0FBR1UsT0FBTztZQUNaO1FBQ0Y7b0RBQUc7UUFBQ2hCO0tBQUs7SUFFVCxNQUFNaUIsUUFBUWpDLGtEQUFXQTttREFBQyxDQUN4QnFCLE9BQ0FDLFNBQ0FVO1lBRUEsT0FBT2hCLEtBQUs7Z0JBQ1ZJLE1BQU07Z0JBQ05DO2dCQUNBQztnQkFDQUMsVUFBVVMsU0FBU1QsWUFBWTtnQkFDL0IsR0FBR1MsT0FBTztZQUNaO1FBQ0Y7a0RBQUc7UUFBQ2hCO0tBQUs7SUFFVCxNQUFNa0IsVUFBVWxDLGtEQUFXQTtxREFBQyxDQUMxQnFCLE9BQ0FDLFNBQ0FVO1lBRUEsT0FBT2hCLEtBQUs7Z0JBQ1ZJLE1BQU07Z0JBQ05DO2dCQUNBQztnQkFDQSxHQUFHVSxPQUFPO1lBQ1o7UUFDRjtvREFBRztRQUFDaEI7S0FBSztJQUVULE1BQU1tQixPQUFPbkMsa0RBQVdBO2tEQUFDLENBQ3ZCcUIsT0FDQUMsU0FDQVU7WUFFQSxPQUFPaEIsS0FBSztnQkFDVkksTUFBTTtnQkFDTkM7Z0JBQ0FDO2dCQUNBLEdBQUdVLE9BQU87WUFDWjtRQUNGO2lEQUFHO1FBQUNoQjtLQUFLO0lBRVQsU0FBUztJQUNULE1BQU1vQixVQUFVcEMsa0RBQVdBO3FEQUFDLENBQUNrQjtZQUMzQlY7NkRBQWlCb0IsQ0FBQUEsT0FBUUEsS0FBS1MsTUFBTTtxRUFBQ2xCLENBQUFBLGVBQWdCQSxhQUFhRCxFQUFFLEtBQUtBOzs7UUFDM0U7b0RBQUcsRUFBRTtJQUVMLFNBQVM7SUFDVCxNQUFNb0IsYUFBYXRDLGtEQUFXQTt3REFBQztZQUM3QlEsaUJBQWlCLEVBQUU7UUFDckI7dURBQUcsRUFBRTtJQUVMLE1BQU0rQixRQUFpQztRQUNyQ2hDO1FBQ0FTO1FBQ0FlO1FBQ0FFO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FFO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3JDLG9CQUFvQnVDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQ2xDbkM7Ozs7OztBQUdQO0FBRUEsK0JBQStCO0FBQ3hCLFNBQVNxQztJQUNkLE1BQU1DLFVBQVU1QyxpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSSxDQUFDeUMsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcbm90aWZpY2F0aW9uXFxOb3RpZmljYXRpb25Db250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBOb3RpZmljYXRpb24sIE5vdGlmaWNhdGlvbkNvbmZpZywgTm90aWZpY2F0aW9uQ29udGV4dFR5cGUgfSBmcm9tICcuL3R5cGVzJztcclxuXHJcbmNvbnN0IE5vdGlmaWNhdGlvbkNvbnRleHQgPSBjcmVhdGVDb250ZXh0PE5vdGlmaWNhdGlvbkNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5cclxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvblByb3ZpZGVyUHJvcHMge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgbWF4Tm90aWZpY2F0aW9ucz86IG51bWJlcjtcclxuICBkZWZhdWx0RHVyYXRpb24/OiBudW1iZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBOb3RpZmljYXRpb25Qcm92aWRlcih7IFxyXG4gIGNoaWxkcmVuLCBcclxuICBtYXhOb3RpZmljYXRpb25zID0gMTAsXHJcbiAgZGVmYXVsdER1cmF0aW9uID0gNDAwMCBcclxufTogTm90aWZpY2F0aW9uUHJvdmlkZXJQcm9wcykge1xyXG4gIGNvbnN0IFtub3RpZmljYXRpb25zLCBzZXROb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvbltdPihbXSk7XHJcblxyXG4gIC8vIOeUn+aIkOWUr+S4gElEXHJcbiAgY29uc3QgZ2VuZXJhdGVJZCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHJldHVybiBgbm90aWZpY2F0aW9uLSR7RGF0ZS5ub3coKX0tJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIOa3u+WKoOmAmuefpVxyXG4gIGNvbnN0IHNob3cgPSB1c2VDYWxsYmFjaygoY29uZmlnOiBOb3RpZmljYXRpb25Db25maWcpOiBzdHJpbmcgPT4ge1xyXG4gICAgY29uc3QgaWQgPSBnZW5lcmF0ZUlkKCk7XHJcbiAgICBjb25zdCBub3RpZmljYXRpb246IE5vdGlmaWNhdGlvbiA9IHtcclxuICAgICAgaWQsXHJcbiAgICAgIHR5cGU6IGNvbmZpZy50eXBlLFxyXG4gICAgICB0aXRsZTogY29uZmlnLnRpdGxlLFxyXG4gICAgICBtZXNzYWdlOiBjb25maWcubWVzc2FnZSxcclxuICAgICAgZHVyYXRpb246IGNvbmZpZy5kdXJhdGlvbiA/PyBkZWZhdWx0RHVyYXRpb24sXHJcbiAgICAgIGRpc21pc3NpYmxlOiBjb25maWcuZGlzbWlzc2libGUgPz8gdHJ1ZSxcclxuICAgICAgYWN0aW9uczogY29uZmlnLmFjdGlvbnMsXHJcbiAgICAgIGljb246IGNvbmZpZy5pY29uLFxyXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXHJcbiAgICB9O1xyXG5cclxuICAgIHNldE5vdGlmaWNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgIGNvbnN0IG5ld05vdGlmaWNhdGlvbnMgPSBbbm90aWZpY2F0aW9uLCAuLi5wcmV2XTtcclxuICAgICAgLy8g6ZmQ5Yi26YCa55+l5pWw6YeP77yM56e76Zmk5pyA5pen55qE6YCa55+lXHJcbiAgICAgIHJldHVybiBuZXdOb3RpZmljYXRpb25zLnNsaWNlKDAsIG1heE5vdGlmaWNhdGlvbnMpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIGlkO1xyXG4gIH0sIFtnZW5lcmF0ZUlkLCBkZWZhdWx0RHVyYXRpb24sIG1heE5vdGlmaWNhdGlvbnNdKTtcclxuXHJcbiAgLy8g5L6/5o235pa55rOVXHJcbiAgY29uc3Qgc3VjY2VzcyA9IHVzZUNhbGxiYWNrKChcclxuICAgIHRpdGxlOiBzdHJpbmcsIFxyXG4gICAgbWVzc2FnZT86IHN0cmluZywgXHJcbiAgICBvcHRpb25zPzogUGFydGlhbDxOb3RpZmljYXRpb25Db25maWc+XHJcbiAgKTogc3RyaW5nID0+IHtcclxuICAgIHJldHVybiBzaG93KHtcclxuICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgbWVzc2FnZSxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH0pO1xyXG4gIH0sIFtzaG93XSk7XHJcblxyXG4gIGNvbnN0IGVycm9yID0gdXNlQ2FsbGJhY2soKFxyXG4gICAgdGl0bGU6IHN0cmluZywgXHJcbiAgICBtZXNzYWdlPzogc3RyaW5nLCBcclxuICAgIG9wdGlvbnM/OiBQYXJ0aWFsPE5vdGlmaWNhdGlvbkNvbmZpZz5cclxuICApOiBzdHJpbmcgPT4ge1xyXG4gICAgcmV0dXJuIHNob3coe1xyXG4gICAgICB0eXBlOiAnZXJyb3InLFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgbWVzc2FnZSxcclxuICAgICAgZHVyYXRpb246IG9wdGlvbnM/LmR1cmF0aW9uID8/IDYwMDAsIC8vIOmUmeivr+mAmuefpem7mOiupOaYvuekuuabtOmVv+aXtumXtFxyXG4gICAgICAuLi5vcHRpb25zLFxyXG4gICAgfSk7XHJcbiAgfSwgW3Nob3ddKTtcclxuXHJcbiAgY29uc3Qgd2FybmluZyA9IHVzZUNhbGxiYWNrKChcclxuICAgIHRpdGxlOiBzdHJpbmcsIFxyXG4gICAgbWVzc2FnZT86IHN0cmluZywgXHJcbiAgICBvcHRpb25zPzogUGFydGlhbDxOb3RpZmljYXRpb25Db25maWc+XHJcbiAgKTogc3RyaW5nID0+IHtcclxuICAgIHJldHVybiBzaG93KHtcclxuICAgICAgdHlwZTogJ3dhcm5pbmcnLFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgbWVzc2FnZSxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH0pO1xyXG4gIH0sIFtzaG93XSk7XHJcblxyXG4gIGNvbnN0IGluZm8gPSB1c2VDYWxsYmFjaygoXHJcbiAgICB0aXRsZTogc3RyaW5nLCBcclxuICAgIG1lc3NhZ2U/OiBzdHJpbmcsIFxyXG4gICAgb3B0aW9ucz86IFBhcnRpYWw8Tm90aWZpY2F0aW9uQ29uZmlnPlxyXG4gICk6IHN0cmluZyA9PiB7XHJcbiAgICByZXR1cm4gc2hvdyh7XHJcbiAgICAgIHR5cGU6ICdpbmZvJyxcclxuICAgICAgdGl0bGUsXHJcbiAgICAgIG1lc3NhZ2UsXHJcbiAgICAgIC4uLm9wdGlvbnMsXHJcbiAgICB9KTtcclxuICB9LCBbc2hvd10pO1xyXG5cclxuICAvLyDnp7vpmaTmjIflrprpgJrnn6VcclxuICBjb25zdCBkaXNtaXNzID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcpID0+IHtcclxuICAgIHNldE5vdGlmaWNhdGlvbnMocHJldiA9PiBwcmV2LmZpbHRlcihub3RpZmljYXRpb24gPT4gbm90aWZpY2F0aW9uLmlkICE9PSBpZCkpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8g5riF6Zmk5omA5pyJ6YCa55+lXHJcbiAgY29uc3QgZGlzbWlzc0FsbCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHNldE5vdGlmaWNhdGlvbnMoW10pO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgdmFsdWU6IE5vdGlmaWNhdGlvbkNvbnRleHRUeXBlID0ge1xyXG4gICAgbm90aWZpY2F0aW9ucyxcclxuICAgIHNob3csXHJcbiAgICBzdWNjZXNzLFxyXG4gICAgZXJyb3IsXHJcbiAgICB3YXJuaW5nLFxyXG4gICAgaW5mbyxcclxuICAgIGRpc21pc3MsXHJcbiAgICBkaXNtaXNzQWxsLFxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Tm90aWZpY2F0aW9uQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L05vdGlmaWNhdGlvbkNvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG5cclxuLy8gSG9vayBmb3IgdXNpbmcgbm90aWZpY2F0aW9uc1xyXG5leHBvcnQgZnVuY3Rpb24gdXNlTm90aWZpY2F0aW9uKCk6IE5vdGlmaWNhdGlvbkNvbnRleHRUeXBlIHtcclxuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChOb3RpZmljYXRpb25Db250ZXh0KTtcclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlTm90aWZpY2F0aW9uIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBOb3RpZmljYXRpb25Qcm92aWRlcicpO1xyXG4gIH1cclxuICByZXR1cm4gY29udGV4dDtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJOb3RpZmljYXRpb25Db250ZXh0IiwidW5kZWZpbmVkIiwiTm90aWZpY2F0aW9uUHJvdmlkZXIiLCJjaGlsZHJlbiIsIm1heE5vdGlmaWNhdGlvbnMiLCJkZWZhdWx0RHVyYXRpb24iLCJub3RpZmljYXRpb25zIiwic2V0Tm90aWZpY2F0aW9ucyIsImdlbmVyYXRlSWQiLCJEYXRlIiwibm93IiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwic2hvdyIsImNvbmZpZyIsImlkIiwibm90aWZpY2F0aW9uIiwidHlwZSIsInRpdGxlIiwibWVzc2FnZSIsImR1cmF0aW9uIiwiZGlzbWlzc2libGUiLCJhY3Rpb25zIiwiaWNvbiIsInRpbWVzdGFtcCIsInByZXYiLCJuZXdOb3RpZmljYXRpb25zIiwic2xpY2UiLCJzdWNjZXNzIiwib3B0aW9ucyIsImVycm9yIiwid2FybmluZyIsImluZm8iLCJkaXNtaXNzIiwiZmlsdGVyIiwiZGlzbWlzc0FsbCIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VOb3RpZmljYXRpb24iLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationItem.tsx":
/*!**********************************************************!*\
  !*** ./src/components/notification/NotificationItem.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationItem: () => (/* binding */ NotificationItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationItem auto */ \n\n\n\nfunction NotificationItem({ notification, onDismiss, position }) {\n    // 自动消失逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationItem.useEffect\": ()=>{\n            if (notification.duration && notification.duration > 0) {\n                const timer = setTimeout({\n                    \"NotificationItem.useEffect.timer\": ()=>{\n                        onDismiss(notification.id);\n                    }\n                }[\"NotificationItem.useEffect.timer\"], notification.duration);\n                return ({\n                    \"NotificationItem.useEffect\": ()=>clearTimeout(timer)\n                })[\"NotificationItem.useEffect\"];\n            }\n        }\n    }[\"NotificationItem.useEffect\"], [\n        notification.duration,\n        notification.id,\n        onDismiss\n    ]);\n    // 图标映射\n    const getIcon = (type)=>{\n        const defaultIcons = {\n            success: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            error: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            warning: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            info: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        };\n        if (notification.icon) {\n            return notification.icon;\n        }\n        const IconComponent = defaultIcons[type];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, this);\n    };\n    // 样式配置\n    const getStyles = (type)=>{\n        const styles = {\n            success: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-success)]',\n                icon: 'text-[var(--color-success)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            },\n            error: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-error)]',\n                icon: 'text-[var(--color-error)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            },\n            warning: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-warning)]',\n                icon: 'text-[var(--color-warning)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            },\n            info: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-info)]',\n                icon: 'text-[var(--color-info)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            }\n        };\n        return styles[type];\n    };\n    // 动画变体\n    const getAnimationVariants = ()=>{\n        const isRight = position.includes('right');\n        const isTop = position.includes('top');\n        return {\n            initial: {\n                opacity: 0,\n                x: isRight ? 300 : -300,\n                y: isTop ? -20 : 20,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                x: 0,\n                y: 0,\n                scale: 1\n            },\n            exit: {\n                opacity: 0,\n                x: isRight ? 300 : -300,\n                scale: 0.9,\n                transition: {\n                    duration: 0.2\n                }\n            }\n        };\n    };\n    const styles = getStyles(notification.type);\n    const icon = getIcon(notification.type);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        layout: true,\n        variants: getAnimationVariants(),\n        initial: \"initial\",\n        animate: \"animate\",\n        exit: \"exit\",\n        transition: {\n            type: \"spring\",\n            stiffness: 300,\n            damping: 30\n        },\n        className: \"w-full max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n        relative p-4 rounded-lg border shadow-sm\n        transition-all duration-200\n        ${styles.container}\n      `,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex-shrink-0 ${styles.icon}`,\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-sm font-semibold ${styles.title}`,\n                                    children: notification.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                notification.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-sm mt-1 ${styles.message}`,\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                notification.actions && notification.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 mt-3\",\n                                    children: notification.actions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: action.onClick,\n                                            className: `\n                      px-3 py-1.5 text-xs font-medium rounded-md border transition-colors\n                      ${action.variant === 'primary' ? `${styles.icon} border-[var(--color-border)] bg-[var(--color-background-tertiary)] hover:bg-[var(--color-card-hover)]` : 'text-[var(--color-foreground-muted)] border border-transparent hover:bg-[var(--color-background-tertiary)]'}\n                    `,\n                                            children: action.label\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        notification.dismissible !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onDismiss(notification.id),\n                            className: `\n                flex-shrink-0 p-1 rounded-md transition-colors\n                text-[var(--color-foreground-muted)] hover:bg-[var(--color-background-tertiary)]\n              `,\n                            \"aria-label\": \"关闭通知\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                notification.duration && notification.duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: `absolute bottom-0 left-0 h-1 rounded-bl-lg ${styles.icon} bg-current/20`,\n                    initial: {\n                        width: '100%'\n                    },\n                    animate: {\n                        width: '0%'\n                    },\n                    transition: {\n                        duration: notification.duration / 1000,\n                        ease: 'linear'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationItem.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationManager.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationManager.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationManager: () => (/* binding */ NotificationManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationContext */ \"(ssr)/./src/components/notification/NotificationContext.tsx\");\n/* harmony import */ var _NotificationContainer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationContainer */ \"(ssr)/./src/components/notification/NotificationContainer.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationManager auto */ \n\n\n\nfunction NotificationManager({ position = 'top-right', maxNotifications = 5 }) {\n    const { notifications, dismiss } = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_2__.useNotification)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationContainer__WEBPACK_IMPORTED_MODULE_3__.NotificationContainer, {\n        notifications: notifications,\n        onDismiss: dismiss,\n        position: position,\n        maxNotifications: maxNotifications\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationManager.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vTm90aWZpY2F0aW9uTWFuYWdlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFMEI7QUFDOEI7QUFDUTtBQU96RCxTQUFTRyxvQkFBb0IsRUFDbENDLFdBQVcsV0FBVyxFQUN0QkMsbUJBQW1CLENBQUMsRUFDSztJQUN6QixNQUFNLEVBQUVDLGFBQWEsRUFBRUMsT0FBTyxFQUFFLEdBQUdOLHFFQUFlQTtJQUVsRCxxQkFDRSw4REFBQ0MseUVBQXFCQTtRQUNwQkksZUFBZUE7UUFDZkUsV0FBV0Q7UUFDWEgsVUFBVUE7UUFDVkMsa0JBQWtCQTs7Ozs7O0FBR3hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXG5vdGlmaWNhdGlvblxcTm90aWZpY2F0aW9uTWFuYWdlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlTm90aWZpY2F0aW9uIH0gZnJvbSAnLi9Ob3RpZmljYXRpb25Db250ZXh0JztcclxuaW1wb3J0IHsgTm90aWZpY2F0aW9uQ29udGFpbmVyIH0gZnJvbSAnLi9Ob3RpZmljYXRpb25Db250YWluZXInO1xyXG5cclxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbk1hbmFnZXJQcm9wcyB7XHJcbiAgcG9zaXRpb24/OiAndG9wLXJpZ2h0JyB8ICd0b3AtbGVmdCcgfCAnYm90dG9tLXJpZ2h0JyB8ICdib3R0b20tbGVmdCc7XHJcbiAgbWF4Tm90aWZpY2F0aW9ucz86IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIE5vdGlmaWNhdGlvbk1hbmFnZXIoeyBcclxuICBwb3NpdGlvbiA9ICd0b3AtcmlnaHQnLFxyXG4gIG1heE5vdGlmaWNhdGlvbnMgPSA1IFxyXG59OiBOb3RpZmljYXRpb25NYW5hZ2VyUHJvcHMpIHtcclxuICBjb25zdCB7IG5vdGlmaWNhdGlvbnMsIGRpc21pc3MgfSA9IHVzZU5vdGlmaWNhdGlvbigpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPE5vdGlmaWNhdGlvbkNvbnRhaW5lclxyXG4gICAgICBub3RpZmljYXRpb25zPXtub3RpZmljYXRpb25zfVxyXG4gICAgICBvbkRpc21pc3M9e2Rpc21pc3N9XHJcbiAgICAgIHBvc2l0aW9uPXtwb3NpdGlvbn1cclxuICAgICAgbWF4Tm90aWZpY2F0aW9ucz17bWF4Tm90aWZpY2F0aW9uc31cclxuICAgIC8+XHJcbiAgKTtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VOb3RpZmljYXRpb24iLCJOb3RpZmljYXRpb25Db250YWluZXIiLCJOb3RpZmljYXRpb25NYW5hZ2VyIiwicG9zaXRpb24iLCJtYXhOb3RpZmljYXRpb25zIiwibm90aWZpY2F0aW9ucyIsImRpc21pc3MiLCJvbkRpc21pc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/examples/NotificationDemo.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/notification/examples/NotificationDemo.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationDemo: () => (/* binding */ NotificationDemo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../NotificationContext */ \"(ssr)/./src/components/notification/NotificationContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationDemo auto */ \n\n\n\nfunction NotificationDemo() {\n    const notification = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_2__.useNotification)();\n    const showSuccess = ()=>{\n        notification.success('操作成功', '您的操作已成功完成');\n    };\n    const showError = ()=>{\n        notification.error('操作失败', '系统遇到了一个错误，请稍后重试');\n    };\n    const showWarning = ()=>{\n        notification.warning('注意', '这是一个重要的警告信息');\n    };\n    const showInfo = ()=>{\n        notification.info('提示', '这是一条信息提示');\n    };\n    const showCustomNotification = ()=>{\n        notification.show({\n            type: 'info',\n            title: '自定义通知',\n            message: '这是一个带有操作按钮的通知',\n            duration: 0,\n            actions: [\n                {\n                    label: '确认',\n                    variant: 'primary',\n                    onClick: ()=>{\n                        notification.success('已确认', '您已确认此操作');\n                    }\n                },\n                {\n                    label: '取消',\n                    variant: 'secondary',\n                    onClick: ()=>{\n                        notification.warning('已取消', '操作已取消');\n                    }\n                }\n            ]\n        });\n    };\n    const showPersistentNotification = ()=>{\n        notification.show({\n            type: 'warning',\n            title: '持久通知',\n            message: '这个通知不会自动消失，需要手动关闭',\n            duration: 0,\n            dismissible: true\n        });\n    };\n    const showWithCustomIcon = ()=>{\n        notification.show({\n            type: 'info',\n            title: '自定义图标',\n            message: '这个通知使用了自定义图标',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 66,\n                columnNumber: 13\n            }, this)\n        });\n    };\n    const clearAllNotifications = ()=>{\n        notification.dismissAll();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-theme-foreground mb-2\",\n                        children: \"通知系统演示\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-theme-foreground-muted\",\n                        children: \"点击下方按钮体验不同类型的通知效果\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-theme-foreground flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"基础类型\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showSuccess,\n                                className: \"w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors\",\n                                children: \"成功通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showError,\n                                className: \"w-full px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors\",\n                                children: \"错误通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showWarning,\n                                className: \"w-full px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors\",\n                                children: \"警告通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showInfo,\n                                className: \"w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors\",\n                                children: \"信息通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-theme-foreground flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"高级功能\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showCustomNotification,\n                                className: \"w-full px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors\",\n                                children: \"带操作按钮\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showPersistentNotification,\n                                className: \"w-full px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors\",\n                                children: \"持久通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showWithCustomIcon,\n                                className: \"w-full px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg transition-colors\",\n                                children: \"自定义图标\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearAllNotifications,\n                                className: \"w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors\",\n                                children: \"清除所有通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 bg-theme-background-secondary rounded-lg border border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-theme-foreground mb-2\",\n                        children: [\n                            \"当前通知数量: \",\n                            notification.notifications.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    notification.notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            notification.notifications.slice(0, 3).map((notif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-theme-foreground-muted\",\n                                    children: [\n                                        notif.type,\n                                        \": \",\n                                        notif.title\n                                    ]\n                                }, notif.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)),\n                            notification.notifications.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-theme-foreground-muted\",\n                                children: [\n                                    \"还有 \",\n                                    notification.notifications.length - 3,\n                                    \" 条通知...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/examples/NotificationDemo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getSavedTheme: () => (/* binding */ getSavedTheme),\n/* harmony export */   getSystemTheme: () => (/* binding */ getSystemTheme),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   saveTheme: () => (/* binding */ saveTheme)\n/* harmony export */ });\n// 主题类型定义\n// 获取系统主题偏好\nconst getSystemTheme = ()=>{\n    if (false) {}\n    return 'light';\n};\n// 从localStorage获取保存的主题\nconst getSavedTheme = ()=>{\n    if (false) {}\n    return null;\n};\n// 保存主题到localStorage\nconst saveTheme = (theme)=>{\n    if (false) {}\n};\n// 应用主题到DOM（带过渡效果）\nconst applyTheme = (theme)=>{\n    if (false) {}\n};\n// 初始化主题\nconst initializeTheme = ()=>{\n    const savedTheme = getSavedTheme();\n    const theme = savedTheme || getSystemTheme();\n    // 首次加载时直接应用主题，不带过渡\n    if (false) {}\n    return theme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/theme.ts\n");

/***/ }),

/***/ "(ssr)/./src/theme/components/ColorThemeScript.tsx":
/*!***************************************************!*\
  !*** ./src/theme/components/ColorThemeScript.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorThemeScript: () => (/* binding */ ColorThemeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ColorThemeScript auto */ \n\nconst THEME_STORAGE_KEY = 'color-theme';\n// This script is injected into the <head> to prevent FOUC (Flash of Unstyled Content)\n// for the color theme. It runs before the page content renders.\nconst colorThemeScript = `\n(function() {\n  try {\n    const theme = localStorage.getItem('${THEME_STORAGE_KEY}');\n    if (theme) {\n      document.documentElement.setAttribute('data-color-theme', theme);\n    } else {\n      // You can set a default theme if none is found\n      document.documentElement.setAttribute('data-color-theme', 'kun');\n    }\n  } catch (e) {\n    console.warn('Could not set color theme from localStorage', e);\n  }\n})();\n`;\nfunction ColorThemeScript() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: colorThemeScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeScript.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdGhlbWUvY29tcG9uZW50cy9Db2xvclRoZW1lU2NyaXB0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFMEI7QUFFMUIsTUFBTUMsb0JBQW9CO0FBRTFCLHNGQUFzRjtBQUN0RixnRUFBZ0U7QUFDaEUsTUFBTUMsbUJBQW1CLENBQUM7Ozt3Q0FHYyxFQUFFRCxrQkFBa0I7Ozs7Ozs7Ozs7O0FBVzVELENBQUM7QUFFTSxTQUFTRTtJQUNkLHFCQUFPLDhEQUFDQztRQUFPQyx5QkFBeUI7WUFBRUMsUUFBUUo7UUFBaUI7Ozs7OztBQUNyRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFx0aGVtZVxcY29tcG9uZW50c1xcQ29sb3JUaGVtZVNjcmlwdC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IFRIRU1FX1NUT1JBR0VfS0VZID0gJ2NvbG9yLXRoZW1lJztcclxuXHJcbi8vIFRoaXMgc2NyaXB0IGlzIGluamVjdGVkIGludG8gdGhlIDxoZWFkPiB0byBwcmV2ZW50IEZPVUMgKEZsYXNoIG9mIFVuc3R5bGVkIENvbnRlbnQpXHJcbi8vIGZvciB0aGUgY29sb3IgdGhlbWUuIEl0IHJ1bnMgYmVmb3JlIHRoZSBwYWdlIGNvbnRlbnQgcmVuZGVycy5cclxuY29uc3QgY29sb3JUaGVtZVNjcmlwdCA9IGBcclxuKGZ1bmN0aW9uKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCB0aGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCcke1RIRU1FX1NUT1JBR0VfS0VZfScpO1xyXG4gICAgaWYgKHRoZW1lKSB7XHJcbiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zZXRBdHRyaWJ1dGUoJ2RhdGEtY29sb3ItdGhlbWUnLCB0aGVtZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBZb3UgY2FuIHNldCBhIGRlZmF1bHQgdGhlbWUgaWYgbm9uZSBpcyBmb3VuZFxyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2V0QXR0cmlidXRlKCdkYXRhLWNvbG9yLXRoZW1lJywgJ2t1bicpO1xyXG4gICAgfVxyXG4gIH0gY2F0Y2ggKGUpIHtcclxuICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IHNldCBjb2xvciB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZScsIGUpO1xyXG4gIH1cclxufSkoKTtcclxuYDtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBDb2xvclRoZW1lU2NyaXB0KCkge1xyXG4gIHJldHVybiA8c2NyaXB0IGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7IF9faHRtbDogY29sb3JUaGVtZVNjcmlwdCB9fSAvPjtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJUSEVNRV9TVE9SQUdFX0tFWSIsImNvbG9yVGhlbWVTY3JpcHQiLCJDb2xvclRoZW1lU2NyaXB0Iiwic2NyaXB0IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/theme/components/ColorThemeScript.tsx\n");

/***/ }),

/***/ "(ssr)/./src/theme/contexts/ThemeContext.tsx":
/*!*********************************************!*\
  !*** ./src/theme/contexts/ThemeContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(ssr)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeToggle auto */ \n\n\n// 创建主题上下文\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 主题提供者组件\nfunction ThemeProvider({ children, defaultTheme = 'light' }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 设置主题的函数\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.saveTheme)(newTheme);\n    };\n    // 切换主题的函数\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : 'light';\n        setTheme(newTheme);\n    };\n    // 初始化主题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const initialTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.initializeTheme)();\n            setThemeState(initialTheme);\n            setIsInitialized(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // 监听系统主题变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!isInitialized) return;\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": (e)=>{\n                    // 只有在没有手动设置主题时才跟随系统主题\n                    const savedTheme = localStorage.getItem('theme');\n                    if (!savedTheme) {\n                        const systemTheme = e.matches ? 'dark' : 'light';\n                        setTheme(systemTheme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        isInitialized\n    ]);\n    // 提供主题配置\n    const themeConfig = {\n        theme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: themeConfig,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n// 使用主题的Hook\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n// 主题切换Hook（简化版本）\nfunction useThemeToggle() {\n    const { theme, toggleTheme } = useTheme();\n    return {\n        theme,\n        toggleTheme,\n        isDark: theme === 'dark',\n        isLight: theme === 'light'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdGhlbWUvY29udGV4dHMvVGhlbWVDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEU7QUFDVztBQUV6RixVQUFVO0FBQ1YsTUFBTVEsNkJBQWVQLG9EQUFhQSxDQUEwQlE7QUFRNUQsVUFBVTtBQUNILFNBQVNDLGNBQWMsRUFBRUMsUUFBUSxFQUFFQyxlQUFlLE9BQU8sRUFBc0I7SUFDcEYsTUFBTSxDQUFDQyxPQUFPQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFRUTtJQUMvQyxNQUFNLENBQUNHLGVBQWVDLGlCQUFpQixHQUFHWiwrQ0FBUUEsQ0FBQztJQUVuRCxVQUFVO0lBQ1YsTUFBTWEsV0FBVyxDQUFDQztRQUNoQkosY0FBY0k7UUFDZFosc0RBQVVBLENBQUNZO1FBQ1hYLHFEQUFTQSxDQUFDVztJQUNaO0lBRUEsVUFBVTtJQUNWLE1BQU1DLGNBQWM7UUFDbEIsTUFBTUQsV0FBV0wsVUFBVSxVQUFVLFNBQVM7UUFDOUNJLFNBQVNDO0lBQ1g7SUFFQSxRQUFRO0lBQ1JmLGdEQUFTQTttQ0FBQztZQUNSLE1BQU1pQixlQUFlZiwyREFBZUE7WUFDcENTLGNBQWNNO1lBQ2RKLGlCQUFpQjtRQUNuQjtrQ0FBRyxFQUFFO0lBRUwsV0FBVztJQUNYYixnREFBU0E7bUNBQUM7WUFDUixJQUFJLENBQUNZLGVBQWU7WUFFcEIsTUFBTU0sYUFBYUMsT0FBT0MsVUFBVSxDQUFDO1lBRXJDLE1BQU1DO3dEQUFlLENBQUNDO29CQUNwQixzQkFBc0I7b0JBQ3RCLE1BQU1DLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztvQkFDeEMsSUFBSSxDQUFDRixZQUFZO3dCQUNmLE1BQU1HLGNBQWNKLEVBQUVLLE9BQU8sR0FBRyxTQUFTO3dCQUN6Q2IsU0FBU1k7b0JBQ1g7Z0JBQ0Y7O1lBRUFSLFdBQVdVLGdCQUFnQixDQUFDLFVBQVVQO1lBRXRDOzJDQUFPO29CQUNMSCxXQUFXVyxtQkFBbUIsQ0FBQyxVQUFVUjtnQkFDM0M7O1FBQ0Y7a0NBQUc7UUFBQ1Q7S0FBYztJQUVsQixTQUFTO0lBQ1QsTUFBTWtCLGNBQTJCO1FBQy9CcEI7UUFDQUk7UUFDQUU7SUFDRjtJQUVBLHFCQUNFLDhEQUFDWCxhQUFhMEIsUUFBUTtRQUFDQyxPQUFPRjtrQkFDM0J0Qjs7Ozs7O0FBR1A7QUFFQSxZQUFZO0FBQ0wsU0FBU3lCO0lBQ2QsTUFBTUMsVUFBVW5DLGlEQUFVQSxDQUFDTTtJQUUzQixJQUFJNkIsWUFBWTVCLFdBQVc7UUFDekIsTUFBTSxJQUFJNkIsTUFBTTtJQUNsQjtJQUVBLE9BQU9EO0FBQ1Q7QUFFQSxpQkFBaUI7QUFDVixTQUFTRTtJQUNkLE1BQU0sRUFBRTFCLEtBQUssRUFBRU0sV0FBVyxFQUFFLEdBQUdpQjtJQUUvQixPQUFPO1FBQ0x2QjtRQUNBTTtRQUNBcUIsUUFBUTNCLFVBQVU7UUFDbEI0QixTQUFTNUIsVUFBVTtJQUNyQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXHRoZW1lXFxjb250ZXh0c1xcVGhlbWVDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVGhlbWUsIFRoZW1lQ29uZmlnLCBpbml0aWFsaXplVGhlbWUsIGFwcGx5VGhlbWUsIHNhdmVUaGVtZSB9IGZyb20gJ0AvbGliL3RoZW1lJztcblxuLy8g5Yib5bu65Li76aKY5LiK5LiL5paHXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29uZmlnIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG4vLyDkuLvpopjmj5DkvpvogIXnu4Tku7blsZ7mgKdcbmludGVyZmFjZSBUaGVtZVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBkZWZhdWx0VGhlbWU/OiBUaGVtZTtcbn1cblxuLy8g5Li76aKY5o+Q5L6b6ICF57uE5Lu2XG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCBkZWZhdWx0VGhlbWUgPSAnbGlnaHQnIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbdGhlbWUsIHNldFRoZW1lU3RhdGVdID0gdXNlU3RhdGU8VGhlbWU+KGRlZmF1bHRUaGVtZSk7XG4gIGNvbnN0IFtpc0luaXRpYWxpemVkLCBzZXRJc0luaXRpYWxpemVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDorr7nva7kuLvpopjnmoTlh73mlbBcbiAgY29uc3Qgc2V0VGhlbWUgPSAobmV3VGhlbWU6IFRoZW1lKSA9PiB7XG4gICAgc2V0VGhlbWVTdGF0ZShuZXdUaGVtZSk7XG4gICAgYXBwbHlUaGVtZShuZXdUaGVtZSk7XG4gICAgc2F2ZVRoZW1lKG5ld1RoZW1lKTtcbiAgfTtcblxuICAvLyDliIfmjaLkuLvpopjnmoTlh73mlbBcbiAgY29uc3QgdG9nZ2xlVGhlbWUgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3VGhlbWUgPSB0aGVtZSA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCc7XG4gICAgc2V0VGhlbWUobmV3VGhlbWUpO1xuICB9O1xuXG4gIC8vIOWIneWni+WMluS4u+mimFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxUaGVtZSA9IGluaXRpYWxpemVUaGVtZSgpO1xuICAgIHNldFRoZW1lU3RhdGUoaW5pdGlhbFRoZW1lKTtcbiAgICBzZXRJc0luaXRpYWxpemVkKHRydWUpO1xuICB9LCBbXSk7XG5cbiAgLy8g55uR5ZCs57O757uf5Li76aKY5Y+Y5YyWXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFpc0luaXRpYWxpemVkKSByZXR1cm47XG5cbiAgICBjb25zdCBtZWRpYVF1ZXJ5ID0gd2luZG93Lm1hdGNoTWVkaWEoJyhwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyayknKTtcbiAgICBcbiAgICBjb25zdCBoYW5kbGVDaGFuZ2UgPSAoZTogTWVkaWFRdWVyeUxpc3RFdmVudCkgPT4ge1xuICAgICAgLy8g5Y+q5pyJ5Zyo5rKh5pyJ5omL5Yqo6K6+572u5Li76aKY5pe25omN6Lef6ZqP57O757uf5Li76aKYXG4gICAgICBjb25zdCBzYXZlZFRoZW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3RoZW1lJyk7XG4gICAgICBpZiAoIXNhdmVkVGhlbWUpIHtcbiAgICAgICAgY29uc3Qgc3lzdGVtVGhlbWUgPSBlLm1hdGNoZXMgPyAnZGFyaycgOiAnbGlnaHQnO1xuICAgICAgICBzZXRUaGVtZShzeXN0ZW1UaGVtZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIG1lZGlhUXVlcnkuYWRkRXZlbnRMaXN0ZW5lcignY2hhbmdlJywgaGFuZGxlQ2hhbmdlKTtcbiAgICBcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgbWVkaWFRdWVyeS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVDaGFuZ2UpO1xuICAgIH07XG4gIH0sIFtpc0luaXRpYWxpemVkXSk7XG5cbiAgLy8g5o+Q5L6b5Li76aKY6YWN572uXG4gIGNvbnN0IHRoZW1lQ29uZmlnOiBUaGVtZUNvbmZpZyA9IHtcbiAgICB0aGVtZSxcbiAgICBzZXRUaGVtZSxcbiAgICB0b2dnbGVUaGVtZSxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxUaGVtZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3RoZW1lQ29uZmlnfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1RoZW1lQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cblxuLy8g5L2/55So5Li76aKY55qESG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVRoZW1lKCk6IFRoZW1lQ29uZmlnIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVGhlbWVDb250ZXh0KTtcbiAgXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVRoZW1lIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUaGVtZVByb3ZpZGVyJyk7XG4gIH1cbiAgXG4gIHJldHVybiBjb250ZXh0O1xufVxuXG4vLyDkuLvpopjliIfmjaJIb29r77yI566A5YyW54mI5pys77yJXG5leHBvcnQgZnVuY3Rpb24gdXNlVGhlbWVUb2dnbGUoKSB7XG4gIGNvbnN0IHsgdGhlbWUsIHRvZ2dsZVRoZW1lIH0gPSB1c2VUaGVtZSgpO1xuICBcbiAgcmV0dXJuIHtcbiAgICB0aGVtZSxcbiAgICB0b2dnbGVUaGVtZSxcbiAgICBpc0Rhcms6IHRoZW1lID09PSAnZGFyaycsXG4gICAgaXNMaWdodDogdGhlbWUgPT09ICdsaWdodCcsXG4gIH07XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiaW5pdGlhbGl6ZVRoZW1lIiwiYXBwbHlUaGVtZSIsInNhdmVUaGVtZSIsIlRoZW1lQ29udGV4dCIsInVuZGVmaW5lZCIsIlRoZW1lUHJvdmlkZXIiLCJjaGlsZHJlbiIsImRlZmF1bHRUaGVtZSIsInRoZW1lIiwic2V0VGhlbWVTdGF0ZSIsImlzSW5pdGlhbGl6ZWQiLCJzZXRJc0luaXRpYWxpemVkIiwic2V0VGhlbWUiLCJuZXdUaGVtZSIsInRvZ2dsZVRoZW1lIiwiaW5pdGlhbFRoZW1lIiwibWVkaWFRdWVyeSIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJoYW5kbGVDaGFuZ2UiLCJlIiwic2F2ZWRUaGVtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzeXN0ZW1UaGVtZSIsIm1hdGNoZXMiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInRoZW1lQ29uZmlnIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVRoZW1lIiwiY29udGV4dCIsIkVycm9yIiwidXNlVGhlbWVUb2dnbGUiLCJpc0RhcmsiLCJpc0xpZ2h0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/theme/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();