"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/components/conversation/ChatHeader.tsx":
/*!********************************************************************!*\
  !*** ./src/app/simple-chat/components/conversation/ChatHeader.tsx ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatHeader: () => (/* binding */ ChatHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ModelSelector */ \"(app-pages-browser)/./src/components/ModelSelector.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatHeader auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ChatHeader(param) {\n    let { currentConversation, models, selectedModel, onModelChange, agents, selectedAgentId, onAgentChange, selectorMode, onSelectorModeChange } = param;\n    _s();\n    const title = currentConversation ? currentConversation.title : \"新对话\";\n    const [isAgentDropdownOpen, setIsAgentDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const agentDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 获取当前选中的Agent\n    const currentAgent = selectedAgentId ? agents.find((a)=>a.id === selectedAgentId) : null;\n    // 适配层：将CustomModel[]转换为ModelSelector期望的格式\n    const adaptedModels = models.map((model)=>({\n            name: model.base_model,\n            model: model.base_model,\n            modified_at: model.ollama_modified_at || model.updated_at || model.created_at,\n            size: model.size || 0,\n            digest: model.digest || '',\n            details: {\n                parent_model: '',\n                format: model.format || '',\n                family: model.family,\n                families: [],\n                parameter_size: model.parameter_count ? \"\".concat(model.parameter_count) : '',\n                quantization_level: model.quantization_level || ''\n            }\n        }));\n    const adaptedCustomModels = models.map((model)=>({\n            base_model: model.base_model,\n            display_name: model.display_name,\n            family: model.family\n        }));\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatHeader.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"ChatHeader.useEffect.handleClickOutside\": (event)=>{\n                    if (agentDropdownRef.current && !agentDropdownRef.current.contains(event.target)) {\n                        setIsAgentDropdownOpen(false);\n                    }\n                }\n            }[\"ChatHeader.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"ChatHeader.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"ChatHeader.useEffect\"];\n        }\n    }[\"ChatHeader.useEffect\"], []);\n    const renderModelSelector = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__.ModelSelector, {\n            models: adaptedModels,\n            selectedModel: selectedModel,\n            onModelChange: onModelChange,\n            customModels: adaptedCustomModels\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    };\n    const renderAgentSelector = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            ref: agentDropdownRef,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsAgentDropdownOpen(!isAgentDropdownOpen),\n                    className: \"w-full flex items-center gap-3 px-3 py-2 text-sm border border-theme-border rounded-md bg-theme-card text-theme-foreground hover:bg-theme-card-hover focus:border-theme-primary focus:ring-1 focus:ring-theme-primary transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-md flex items-center justify-center flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex-1 text-left truncate\",\n                            children: (currentAgent === null || currentAgent === void 0 ? void 0 : currentAgent.name) || 'Select an Agent...'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-theme-foreground-muted transition-transform duration-200 \".concat(isAgentDropdownOpen ? 'rotate-180' : ''),\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 9l-7 7-7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                isAgentDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-full left-0 right-0 mt-1 bg-theme-card border border-theme-border rounded-md shadow-lg z-50 max-h-60 overflow-y-auto scrollbar-thin\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onAgentChange(null);\n                                setIsAgentDropdownOpen(false);\n                            },\n                            className: \"w-full flex items-center gap-3 px-3 py-2 text-sm text-left hover:bg-theme-card-hover transition-colors duration-200 \".concat(!selectedAgentId ? 'bg-theme-background-secondary' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-gray-400 rounded-md flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 truncate text-theme-foreground-muted\",\n                                    children: \"No Agent Selected\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                !selectedAgentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-theme-primary\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        agents.map((agent)=>{\n                            const isSelected = agent.id === selectedAgentId;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onAgentChange(agent.id);\n                                    setIsAgentDropdownOpen(false);\n                                },\n                                className: \"w-full flex items-center gap-3 px-3 py-2 text-sm text-left hover:bg-theme-card-hover transition-colors duration-200 \".concat(isSelected ? 'bg-theme-background-secondary' : ''),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-md flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3 text-white\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-1 truncate text-theme-foreground\",\n                                        children: agent.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this),\n                                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 text-theme-primary\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, agent.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    };\n    const renderSelector = ()=>{\n        if (selectorMode === 'agent') {\n            return renderAgentSelector();\n        }\n        return renderModelSelector();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 bg-theme-card transition-colors duration-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 min-w-0 flex-1 max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center bg-theme-background-secondary p-1 rounded-lg flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onSelectorModeChange('model'),\n                                    className: \"px-3 py-1 text-sm rounded-md transition-colors duration-200 whitespace-nowrap \".concat(selectorMode === 'model' ? 'bg-theme-card shadow text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                                    children: \"Model\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onSelectorModeChange('agent'),\n                                    className: \"px-3 py-1 text-sm rounded-md transition-colors duration-200 whitespace-nowrap \".concat(selectorMode === 'agent' ? 'bg-theme-card shadow text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                                    children: \"Agent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: renderSelector()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-theme-foreground text-right\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\conversation\\\\ChatHeader.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatHeader, \"tXlzKo2gbFhqhdcD1Zs98Lfh5xc=\");\n_c = ChatHeader;\nvar _c;\n$RefreshReg$(_c, \"ChatHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/components/conversation/ChatHeader.tsx\n"));

/***/ })

});