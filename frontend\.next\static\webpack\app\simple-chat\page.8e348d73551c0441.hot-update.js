"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx":
/*!*************************************************************!*\
  !*** ./src/app/simple-chat/components/chat/MessageList.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageList: () => (/* binding */ MessageList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/ThinkingMode */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/ThinkingMode.tsx\");\n/* harmony import */ var _ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/StreamedContent */ \"(app-pages-browser)/./src/app/simple-chat/components/ui/StreamedContent.tsx\");\n/* harmony import */ var _tools_ToolCallMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tools/ToolCallMessage */ \"(app-pages-browser)/./src/app/simple-chat/components/tools/ToolCallMessage.tsx\");\n/* harmony import */ var _app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/model-manager/components/ModelLogo */ \"(app-pages-browser)/./src/app/model-manager/components/ModelLogo.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MessageList(param) {\n    let { messages, isStreaming, expandedThinkingMessages, onToggleThinkingExpand, chatStyle, selectedModel, customModels } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isNearBottom, setIsNearBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isNearTop, setIsNearTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollButtons, setShowScrollButtons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageCount, setMessageCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(messages.length);\n    const [userScrolled, setUserScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastScrollTime, setLastScrollTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const scrollTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 获取模型的显示信息 - 简化：直接使用模型名称\n    const getModelDisplayInfo = (modelName)=>{\n        if (!modelName) return {\n            displayName: 'AI助手',\n            family: 'default'\n        };\n        return {\n            displayName: modelName,\n            family: modelName.split(':')[0] || modelName // 使用模型名称的前缀作为家族\n        };\n    };\n    // 检查滚动位置 - 寻找真正的滚动容器\n    const checkScrollPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[checkScrollPosition]\": ()=>{\n            // 先尝试当前组件的滚动容器\n            let container = scrollContainerRef.current;\n            // 如果当前容器没有滚动条，查找父级的滚动容器\n            if (container && container.scrollHeight <= container.clientHeight) {\n                // 查找最近的可滚动父元素\n                let parent = container.parentElement;\n                while(parent){\n                    if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                        container = parent;\n                        break;\n                    }\n                    parent = parent.parentElement;\n                }\n            }\n            if (!container) return {\n                nearBottom: true,\n                nearTop: true\n            };\n            const { scrollTop, scrollHeight, clientHeight } = container;\n            const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);\n            const distanceFromTop = scrollTop;\n            // 检测是否接近顶部和底部 - 考虑段落间距影响\n            // space-y-4 = 16px，加上padding和其他间距，使用更宽松的阈值\n            const nearBottom = distanceFromBottom <= 50; // 放宽底部检测，应对段落间距\n            const nearTop = distanceFromTop <= 50;\n            // 智能显示按钮：当有足够内容可以滚动时就显示\n            const hasEnoughContentToScroll = scrollHeight > clientHeight + 100; // 内容高度超过容器高度100px以上\n            const showButtons = messages.length > 0 && hasEnoughContentToScroll;\n            setIsNearBottom(nearBottom);\n            setIsNearTop(nearTop);\n            setShowScrollButtons(showButtons);\n            return {\n                nearBottom,\n                nearTop\n            };\n        }\n    }[\"MessageList.useCallback[checkScrollPosition]\"], [\n        messages.length\n    ]);\n    // 滚动到底部 - 优化定位精度\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[scrollToBottom]\": function() {\n            let behavior = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'smooth';\n            var _scrollContainerRef_current;\n            // 方法1：使用 scrollIntoView，但加上 block: 'end' 确保精确定位\n            if (messagesEndRef.current) {\n                messagesEndRef.current.scrollIntoView({\n                    behavior,\n                    block: 'end',\n                    inline: 'nearest'\n                });\n                return;\n            }\n            // 方法2：备用方案，直接滚动到容器底部\n            const container = (_scrollContainerRef_current = scrollContainerRef.current) === null || _scrollContainerRef_current === void 0 ? void 0 : _scrollContainerRef_current.parentElement;\n            if (container && container.scrollHeight > container.clientHeight) {\n                container.scrollTo({\n                    top: container.scrollHeight,\n                    behavior\n                });\n            }\n        }\n    }[\"MessageList.useCallback[scrollToBottom]\"], []);\n    // 滚动到顶部\n    const scrollToTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[scrollToTop]\": function() {\n            let behavior = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'smooth';\n            // 寻找可滚动的容器\n            let container = scrollContainerRef.current;\n            // 如果当前容器不可滚动，查找父级滚动容器\n            if (container && container.scrollHeight <= container.clientHeight) {\n                let parent = container.parentElement;\n                while(parent){\n                    if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                        container = parent;\n                        break;\n                    }\n                    parent = parent.parentElement;\n                }\n            }\n            if (container) {\n                container.scrollTo({\n                    top: 0,\n                    behavior\n                });\n            }\n        }\n    }[\"MessageList.useCallback[scrollToTop]\"], []);\n    // 处理用户滚动 - 增加用户意图检测\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageList.useCallback[handleScroll]\": ()=>{\n            const now = Date.now();\n            const timeSinceLastScroll = now - lastScrollTime;\n            // 如果距离上次滚动时间很短，认为是用户主动滚动\n            if (timeSinceLastScroll < 1000) {\n                setUserScrolled(true);\n                // 清除之前的定时器\n                if (scrollTimeoutRef.current) {\n                    clearTimeout(scrollTimeoutRef.current);\n                }\n                // 3秒后重置用户滚动状态\n                scrollTimeoutRef.current = setTimeout({\n                    \"MessageList.useCallback[handleScroll]\": ()=>{\n                        setUserScrolled(false);\n                    }\n                }[\"MessageList.useCallback[handleScroll]\"], 3000);\n            }\n            setLastScrollTime(now);\n            // 直接内联检查滚动位置，避免循环依赖\n            requestAnimationFrame({\n                \"MessageList.useCallback[handleScroll]\": ()=>{\n                    checkScrollPosition();\n                }\n            }[\"MessageList.useCallback[handleScroll]\"]);\n        }\n    }[\"MessageList.useCallback[handleScroll]\"], [\n        lastScrollTime\n    ]); // 移除checkScrollPosition依赖\n    // 监听外层滚动容器的滚动事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            // 找到外层的滚动容器并绑定事件\n            const findScrollContainer = {\n                \"MessageList.useEffect.findScrollContainer\": ()=>{\n                    let current = scrollContainerRef.current;\n                    if (!current) return null;\n                    // 向上找到真正的滚动容器\n                    let parent = current.parentElement;\n                    while(parent){\n                        if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                            return parent;\n                        }\n                        parent = parent.parentElement;\n                    }\n                    return current;\n                }\n            }[\"MessageList.useEffect.findScrollContainer\"];\n            const scrollContainer = findScrollContainer();\n            if (scrollContainer) {\n                scrollContainer.addEventListener('scroll', handleScroll, {\n                    passive: true\n                });\n                return ({\n                    \"MessageList.useEffect\": ()=>{\n                        scrollContainer.removeEventListener('scroll', handleScroll);\n                    }\n                })[\"MessageList.useEffect\"];\n            }\n        }\n    }[\"MessageList.useEffect\"], [\n        handleScroll\n    ]);\n    // 当消息发生变化时的智能滚动逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            const wasNearBottom = isNearBottom;\n            const newMessageCount = messages.length;\n            const hasNewMessages = newMessageCount > messageCount;\n            // 更新消息计数\n            setMessageCount(newMessageCount);\n            // 优化的智能滚动逻辑：\n            // 1. 用户主动滚动时，暂停自动滚动\n            // 2. 只有在接近底部且没有用户干预时才自动滚动\n            // 3. 流式更新使用防抖机制，减少频繁滚动\n            if (!userScrolled && wasNearBottom && (hasNewMessages || isStreaming)) {\n                // 清除之前的滚动定时器，实现防抖\n                if (scrollTimeoutRef.current) {\n                    clearTimeout(scrollTimeoutRef.current);\n                }\n                // 使用防抖延迟，避免频繁滚动导致的抖动\n                const scrollDelay = isStreaming ? 150 : 50; // 流式时更长延迟\n                scrollTimeoutRef.current = setTimeout({\n                    \"MessageList.useEffect\": ()=>{\n                        // 再次检查用户是否在此期间滚动了\n                        if (!userScrolled && isNearBottom) {\n                            // 流式更新时使用 'auto'，新消息时使用 'smooth'\n                            const behavior = isStreaming ? 'auto' : 'smooth';\n                            scrollToBottom(behavior);\n                        }\n                    }\n                }[\"MessageList.useEffect\"], scrollDelay);\n            }\n            // 延迟重新检查位置，避免与滚动冲突\n            setTimeout({\n                \"MessageList.useEffect\": ()=>{\n                    requestAnimationFrame({\n                        \"MessageList.useEffect\": ()=>{\n                            checkScrollPosition();\n                        }\n                    }[\"MessageList.useEffect\"]);\n                }\n            }[\"MessageList.useEffect\"], 200);\n        }\n    }[\"MessageList.useEffect\"], [\n        messages,\n        isStreaming,\n        isNearBottom,\n        messageCount,\n        userScrolled\n    ]);\n    // 初始化时滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            if (messages.length > 0) {\n                // 首次加载时直接滚动到底部\n                requestAnimationFrame({\n                    \"MessageList.useEffect\": ()=>{\n                        scrollToBottom('auto');\n                    }\n                }[\"MessageList.useEffect\"]);\n            }\n        }\n    }[\"MessageList.useEffect\"], []); // 只在组件首次挂载时执行\n    // 组件挂载后立即检查滚动位置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            // 延迟检查确保DOM完全渲染\n            const timer = setTimeout({\n                \"MessageList.useEffect.timer\": ()=>{\n                    // 内联检查逻辑，避免函数依赖\n                    let container = scrollContainerRef.current;\n                    if (container && container.scrollHeight <= container.clientHeight) {\n                        let parent = container.parentElement;\n                        while(parent){\n                            if (parent.scrollHeight > parent.clientHeight && getComputedStyle(parent).overflowY !== 'visible') {\n                                container = parent;\n                                break;\n                            }\n                            parent = parent.parentElement;\n                        }\n                    }\n                    if (!container) return;\n                    const { scrollTop, scrollHeight, clientHeight } = container;\n                    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);\n                    const distanceFromTop = scrollTop;\n                    const nearBottom = distanceFromBottom <= 50;\n                    const nearTop = distanceFromTop <= 50;\n                    const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;\n                    const showButtons = messages.length > 0 && hasEnoughContentToScroll;\n                    setIsNearBottom(nearBottom);\n                    setIsNearTop(nearTop);\n                    setShowScrollButtons(showButtons);\n                }\n            }[\"MessageList.useEffect.timer\"], 300);\n            return ({\n                \"MessageList.useEffect\": ()=>clearTimeout(timer)\n            })[\"MessageList.useEffect\"];\n        }\n    }[\"MessageList.useEffect\"], [\n        messages.length\n    ]); // 只依赖消息长度\n    // 清理定时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            return ({\n                \"MessageList.useEffect\": ()=>{\n                    if (scrollTimeoutRef.current) {\n                        clearTimeout(scrollTimeoutRef.current);\n                    }\n                }\n            })[\"MessageList.useEffect\"];\n        }\n    }[\"MessageList.useEffect\"], []);\n    // 格式化时间（纳秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    // 渲染生成统计信息图标\n    const renderGenerationStatsIcon = (message)=>{\n        // 检查是否为当前生成中的消息\n        const isCurrentlyGenerating = isStreaming && message.role === 'assistant' && messages.indexOf(message) === messages.length - 1;\n        // 如果有完整的统计数据（至少有总时长或生成token数量），显示详细信息\n        const hasCompleteStats = message.total_duration || message.eval_count;\n        const statsText = hasCompleteStats ? \"总时长: \".concat(formatDuration(message.total_duration), \"\\n\") + \"加载时长: \".concat(formatDuration(message.load_duration), \"\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : isCurrentlyGenerating ? '正在生成中，统计信息将在完成后显示...' : '统计信息不可用';\n        // 获取消息创建时间\n        const messageTime = message.timestamp ? new Date(message.timestamp).toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n        }) : new Date().toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative inline-block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group inline-block\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-theme-foreground-muted hover:text-theme-foreground cursor-help transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-0 bottom-full mb-1 bg-gray-800 text-white text-xs rounded px-3 py-2 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 min-w-max shadow-lg pointer-events-none\",\n                                children: statsText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-theme-foreground-muted\",\n                    children: messageTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                className: \"p-4 space-y-4\",\n                children: [\n                    messages.map((message, index)=>{\n                        // 如果是工具调用占位符消息，渲染工具调用组件\n                        if (message.role === 'tool_call' && message.toolCall) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_ToolCallMessage__WEBPACK_IMPORTED_MODULE_4__.ToolCallMessage, {\n                                toolCall: message.toolCall\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        // 检查消息是否包含思考内容\n                        const hasThinking = message.role === 'assistant' && (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.hasThinkingContent)(message.content);\n                        const contentWithoutThinking = hasThinking ? (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.removeThinkingContent)(message.content) : message.content;\n                        const isCurrentlyThinking = isStreaming && message.role === 'assistant' && index === messages.length - 1 && (0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.hasThinkingContent)(message.content) && !(0,_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.removeThinkingContent)(message.content).trim();\n                        // 🔧 修复：检查是否应该显示消息气泡\n                        // 对于 assistant 消息，如果只有思考内容而没有实际内容，且不是正在生成状态，则不显示消息气泡\n                        const isLastMessage = index === messages.length - 1;\n                        const isGenerating = isStreaming && message.role === 'assistant' && isLastMessage;\n                        const hasActualContent = contentWithoutThinking.trim().length > 0;\n                        const shouldShowBubble = message.role === 'user' || hasActualContent || isGenerating && !isCurrentlyThinking;\n                        // 获取模型显示信息\n                        const modelDisplayInfo = getModelDisplayInfo(message.model || selectedModel);\n                        // 根据聊天样式决定布局\n                        if (chatStyle === 'conversation') {\n                            // 对话模式：用户右侧，AI左侧\n                            const isUser = message.role === 'user';\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 \".concat(isUser ? 'flex-row-reverse' : ''),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isUser ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            modelName: modelDisplayInfo.family,\n                                            size: \"lg\",\n                                            containerSize: 40,\n                                            imageSize: 32,\n                                            className: \"bg-transparent border-0 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] space-y-2 \".concat(isUser ? 'flex flex-col items-end' : ''),\n                                        children: [\n                                            (shouldShowBubble || hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 \".concat(isUser ? 'justify-end' : 'justify-start'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-theme-foreground-muted \".concat(isUser ? 'text-right' : 'text-left'),\n                                                        children: isUser ? '你' : modelDisplayInfo.displayName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !isUser && message.role === 'assistant' && renderGenerationStatsIcon(message)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.ThinkingMode, {\n                                                content: message.content,\n                                                isExpanded: expandedThinkingMessages.has(message.id),\n                                                onToggleExpand: ()=>onToggleThinkingExpand(message.id),\n                                                defaultHidden: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 21\n                                            }, this),\n                                            shouldShowBubble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-block p-3 rounded-lg \".concat(isUser ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    content: contentWithoutThinking || (isGenerating && !isCurrentlyThinking ? '正在生成回复...' : ''),\n                                                    isStreaming: isGenerating,\n                                                    enableMarkdown: !isUser,\n                                                    className: !isUser ? \"break-words leading-[1.4]\" : \"break-words whitespace-pre-wrap leading-[1.4]\",\n                                                    style: {\n                                                        minWidth: 0,\n                                                        maxWidth: '100%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, message.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 15\n                            }, this);\n                        } else {\n                            // 助手模式：所有消息都在左侧\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 \".concat(message.role === 'user' ? 'bg-theme-primary text-white' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                                        children: message.role === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            modelName: modelDisplayInfo.family,\n                                            size: \"lg\",\n                                            containerSize: 40,\n                                            imageSize: 32,\n                                            className: \"bg-transparent border-0 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 space-y-2\",\n                                        children: [\n                                            (shouldShowBubble || hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-theme-foreground-muted\",\n                                                        children: message.role === 'user' ? '你' : modelDisplayInfo.displayName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    message.role === 'assistant' && renderGenerationStatsIcon(message)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThinkingMode__WEBPACK_IMPORTED_MODULE_2__.ThinkingMode, {\n                                                content: message.content,\n                                                isExpanded: expandedThinkingMessages.has(message.id),\n                                                onToggleExpand: ()=>onToggleThinkingExpand(message.id),\n                                                defaultHidden: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 21\n                                            }, this),\n                                            shouldShowBubble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none text-theme-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_StreamedContent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    content: contentWithoutThinking || (isGenerating && !isCurrentlyThinking ? '正在生成回复...' : ''),\n                                                    isStreaming: isGenerating,\n                                                    enableMarkdown: message.role === 'assistant',\n                                                    className: message.role === 'assistant' ? \"break-words leading-[1.4]\" : \"break-words whitespace-pre-wrap leading-[1.4]\",\n                                                    style: {\n                                                        minWidth: 0,\n                                                        maxWidth: '100%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, message.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, this);\n                        }\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            showScrollButtons && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group fixed bottom-40 right-12 z-50 flex flex-col gap-1 p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-16 h-full -right-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-1 opacity-0 translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300 ease-out\",\n                        children: [\n                            !isNearTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToTop('smooth'),\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"回到顶部\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const { nearBottom, nearTop } = checkScrollPosition();\n                                    console.log('当前状态:', {\n                                        isNearTop,\n                                        isNearBottom,\n                                        nearTop,\n                                        nearBottom,\n                                        showScrollButtons\n                                    });\n                                },\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"调试信息\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this),\n                            !isNearBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToBottom('smooth'),\n                                className: \"w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm\",\n                                title: \"回到底部\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 519,\n                columnNumber: 10\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\components\\\\chat\\\\MessageList.tsx\",\n        lineNumber: 353,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageList, \"kAtPFrOasN3r8MRT6w+/YHio3xY=\");\n_c = MessageList;\nvar _c;\n$RefreshReg$(_c, \"MessageList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/components/chat/MessageList.tsx\n"));

/***/ })

});