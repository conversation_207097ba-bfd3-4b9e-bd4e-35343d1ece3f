"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/page.tsx":
/*!**************************************!*\
  !*** ./src/app/simple-chat/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks */ \"(app-pages-browser)/./src/app/simple-chat/hooks/index.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/simple-chat/components/index.ts\");\n/* harmony import */ var _services_streamingChatService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/streamingChatService */ \"(app-pages-browser)/./src/app/simple-chat/services/streamingChatService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SimpleChatPage() {\n    _s();\n    // 使用模块化的hooks\n    const { conversations, currentConversation, loading: conversationLoading, error: conversationError, loadConversations, loadConversationsIfNeeded, createConversation, switchConversation, deleteConversation } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationManager)();\n    const { messages, setMessages, inputMessage, setInputMessage, isStreaming, setIsStreaming, selectedModel, setSelectedModel, models, expandedThinkingMessages, enableTools, setEnableTools, selectedTools, setSelectedTools, activeTool, setActiveTool, toolCalls, setToolCalls, currentAssistantMessageId, setCurrentAssistantMessageId, systemPrompt, selectAgent, toggleThinkingExpand, stopGeneration, selectBestModel, abortController, setAbortController } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useChatMessages)();\n    const { chatStyle, displaySize, setChatStyle: handleChatStyleChange, setDisplaySize: handleDisplaySizeChange } = (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useChatStyle)();\n    // UI状态\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Agent related state\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAgentId, setSelectedAgentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectorMode, setSelectorMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('model');\n    // 优化：直接使用models数据，无需转换\n    // models现在是CustomModel[]类型，包含完整的display_name等信息\n    // 为组件兼容性生成customModels格式\n    const [customModels, setCustomModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从CustomModel[]生成customModels显示信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            if (models.length > 0) {\n                const formattedCustomModels = models.map({\n                    \"SimpleChatPage.useEffect.formattedCustomModels\": (model)=>({\n                            base_model: model.base_model,\n                            display_name: model.display_name,\n                            family: model.family\n                        })\n                }[\"SimpleChatPage.useEffect.formattedCustomModels\"]);\n                setCustomModels(formattedCustomModels);\n                console.log('✅ 生成customModels显示信息:', formattedCustomModels.length, '个模型');\n            }\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        models\n    ]);\n    // 使用ref来获取最新的messages值，避免在useCallback依赖中包含messages\n    const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages);\n    const selectedModelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(selectedModel);\n    const setMessagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setMessages);\n    const setActiveToolRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setActiveTool);\n    const setToolCallsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setToolCalls);\n    const setCurrentAssistantMessageIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setCurrentAssistantMessageId);\n    const setIsStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setIsStreaming);\n    const setErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(setError);\n    const loadConversationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(loadConversations);\n    const systemPromptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(systemPrompt);\n    // 🔧 修复：添加清理函数的ref\n    const cleanupHandlersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            messagesRef.current = messages;\n            selectedModelRef.current = selectedModel;\n            setMessagesRef.current = setMessages;\n            setActiveToolRef.current = setActiveTool;\n            setToolCallsRef.current = setToolCalls;\n            setCurrentAssistantMessageIdRef.current = setCurrentAssistantMessageId;\n            setIsStreamingRef.current = setIsStreaming;\n            setErrorRef.current = setError;\n            loadConversationsRef.current = loadConversations;\n            systemPromptRef.current = systemPrompt;\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        messages,\n        selectedModel,\n        setMessages,\n        setActiveTool,\n        setToolCalls,\n        setCurrentAssistantMessageId,\n        setIsStreaming,\n        setError,\n        loadConversations,\n        systemPrompt\n    ]);\n    // 🔧 修复：组件卸载时清理所有pending的更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>{\n                    cleanupHandlersRef.current.forEach({\n                        \"SimpleChatPage.useEffect\": (cleanup)=>cleanup()\n                    }[\"SimpleChatPage.useEffect\"]);\n                    cleanupHandlersRef.current = [];\n                }\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], []);\n    // 从数据库数据更新消息的辅助函数\n    const updateMessagesFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[updateMessagesFromDatabase]\": (dbMessages)=>{\n            console.log('🔧 更新消息数据，总数:', dbMessages.length);\n            // 使用与useMessageLoader相同的逻辑处理工具调用消息\n            const allMessages = dbMessages.sort({\n                \"SimpleChatPage.useCallback[updateMessagesFromDatabase].allMessages\": (a, b)=>{\n                    if (a.timestamp !== b.timestamp) {\n                        return a.timestamp - b.timestamp;\n                    }\n                    return a.id - b.id;\n                }\n            }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase].allMessages\"]);\n            const formattedMessages = [];\n            const toolCallMessages = [];\n            for (const msg of allMessages){\n                if (msg.role === 'tool_call' && msg.tool_name) {\n                    // 处理工具调用消息\n                    let args = {};\n                    let result = '';\n                    try {\n                        args = msg.tool_args ? JSON.parse(msg.tool_args) : {};\n                    } catch (e) {\n                        args = {};\n                    }\n                    try {\n                        result = msg.tool_result ? typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result) : '';\n                    } catch (e) {\n                        result = msg.tool_result || '';\n                    }\n                    const toolCall = {\n                        id: \"tool-\".concat(msg.id),\n                        toolName: msg.tool_name,\n                        args: args,\n                        status: msg.tool_status || 'completed',\n                        result: result,\n                        error: msg.tool_error || undefined,\n                        startTime: msg.timestamp || new Date(msg.created_at).getTime(),\n                        executionTime: msg.tool_execution_time || 0\n                    };\n                    toolCallMessages.push(toolCall);\n                    formattedMessages.push({\n                        id: \"tool-placeholder-\".concat(msg.id),\n                        role: 'tool_call',\n                        content: '',\n                        timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                        toolCall: toolCall\n                    });\n                } else {\n                    formattedMessages.push({\n                        id: \"msg-\".concat(msg.id),\n                        role: msg.role,\n                        content: msg.content,\n                        timestamp: msg.timestamp || new Date(msg.created_at).getTime(),\n                        model: msg.model,\n                        // 包含统计字段\n                        total_duration: msg.total_duration,\n                        load_duration: msg.load_duration,\n                        prompt_eval_count: msg.prompt_eval_count,\n                        prompt_eval_duration: msg.prompt_eval_duration,\n                        eval_count: msg.eval_count,\n                        eval_duration: msg.eval_duration\n                    });\n                }\n            }\n            // 检查是否有统计信息\n            const hasStats = formattedMessages.some({\n                \"SimpleChatPage.useCallback[updateMessagesFromDatabase].hasStats\": (msg)=>msg.role === 'assistant' && (msg.total_duration || msg.eval_count)\n            }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase].hasStats\"]);\n            console.log('🔧 更新后的消息是否包含统计信息:', hasStats);\n            console.log('🔧 更新后的工具调用数量:', toolCallMessages.length);\n            setMessagesRef.current(formattedMessages);\n            setToolCallsRef.current(toolCallMessages);\n        }\n    }[\"SimpleChatPage.useCallback[updateMessagesFromDatabase]\"], []);\n    // 处理模型切换，传递对话ID以保存对话特定的模型选择\n    const handleModelChange = (modelName)=>{\n        const conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        setSelectedModel(modelName, conversationId);\n    };\n    // Fetch agents - 优化：添加缓存和错误处理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            let isMounted = true;\n            const fetchAgents = {\n                \"SimpleChatPage.useEffect.fetchAgents\": async ()=>{\n                    try {\n                        console.log('🤖 开始加载智能体列表');\n                        const response = await fetch('/api/agents');\n                        if (response.ok && isMounted) {\n                            const agents = await response.json();\n                            setAgents(agents);\n                            console.log(\"✅ 成功加载 \".concat(agents.length, \" 个智能体\"));\n                        }\n                    } catch (error) {\n                        if (isMounted) {\n                            console.error('❌ 加载智能体失败:', error);\n                        }\n                    }\n                }\n            }[\"SimpleChatPage.useEffect.fetchAgents\"];\n            fetchAgents();\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], []);\n    const handleAgentChange = (agentId)=>{\n        setSelectedAgentId(agentId);\n        selectAgent(agentId);\n    };\n    const handleSelectorModeChange = (mode)=>{\n        setSelectorMode(mode);\n    };\n    // 使用 URL 处理器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useUrlHandler)({\n        models,\n        selectedModel,\n        currentConversation,\n        conversationLoading,\n        createConversation,\n        switchConversation\n    });\n    // 使用消息加载器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useMessageLoader)({\n        currentConversation,\n        setSelectedModel,\n        setMessages,\n        setToolCalls,\n        selectedModel,\n        models,\n        selectBestModel\n    });\n    // 使用对话事件处理器\n    (0,_hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationEventHandlers)({\n        currentConversation,\n        conversations,\n        selectedModel,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        loadConversations,\n        setMessages,\n        setToolCalls,\n        setSelectedModel,\n        setError,\n        setIsProcessingUrl: {\n            \"SimpleChatPage.useConversationEventHandlers\": ()=>{}\n        }[\"SimpleChatPage.useConversationEventHandlers\"]\n    });\n    // 清空当前对话\n    const clearCurrentChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[clearCurrentChat]\": async ()=>{\n            if (!currentConversation) return;\n            try {\n                const response = await fetch(\"/api/conversations/\".concat(currentConversation.id, \"/clear\"), {\n                    method: 'POST'\n                });\n                if (response.ok) {\n                    // 清空当前消息\n                    setMessages([]);\n                    setToolCalls([]);\n                    setActiveTool(null);\n                    setError(null);\n                    // 重新加载对话列表\n                    loadConversations();\n                }\n            } catch (error) {\n                console.error('清空对话失败:', error);\n                setError('清空对话失败');\n            }\n        }\n    }[\"SimpleChatPage.useCallback[clearCurrentChat]\"], [\n        currentConversation,\n        setMessages,\n        setToolCalls,\n        setActiveTool,\n        setError,\n        loadConversations\n    ]);\n    // 创建流处理器句柄 - 修复：使用useCallback确保函数稳定性，移除不必要的依赖\n    const createStreamHandlers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n            const handlers = {\n                onMessageUpdate: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (messageId, content, stats)=>{\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>msg.id === messageId ? {\n                                            ...msg,\n                                            content,\n                                            ...stats || {}\n                                        } : msg\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallStart: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCall)=>{\n                        setActiveToolRef.current(toolCall);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    toolCall\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        const toolCallMessage = {\n                            id: \"tool-runtime-\".concat(toolCall.id),\n                            role: 'tool_call',\n                            content: '',\n                            timestamp: Date.now(),\n                            toolCall: toolCall\n                        };\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    toolCallMessage\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallComplete: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCallId, toolName, result, executionTime)=>{\n                        setActiveToolRef.current(null);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (tc)=>{\n                                        const isMatch = toolCallId ? tc.id === toolCallId : tc.toolName === toolName && tc.status === 'executing';\n                                        return isMatch ? {\n                                            ...tc,\n                                            status: 'completed',\n                                            result: typeof result === 'string' ? result : JSON.stringify(result),\n                                            executionTime: executionTime || Date.now() - tc.startTime\n                                        } : tc;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>{\n                                        if (msg.role === 'tool_call' && msg.toolCall) {\n                                            const isMatch = toolCallId ? msg.toolCall.id === toolCallId : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';\n                                            if (isMatch) {\n                                                return {\n                                                    ...msg,\n                                                    toolCall: {\n                                                        id: msg.toolCall.id,\n                                                        toolName: msg.toolCall.toolName,\n                                                        args: msg.toolCall.args,\n                                                        status: 'completed',\n                                                        result: typeof result === 'string' ? result : JSON.stringify(result),\n                                                        startTime: msg.toolCall.startTime,\n                                                        executionTime: executionTime || Date.now() - msg.toolCall.startTime\n                                                    }\n                                                };\n                                            }\n                                        }\n                                        return msg;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onToolCallError: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (toolCallId, toolName, error, executionTime)=>{\n                        setActiveToolRef.current(null);\n                        setToolCallsRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (tc)=>{\n                                        const isMatch = toolCallId ? tc.id === toolCallId : tc.toolName === toolName && tc.status === 'executing';\n                                        return isMatch ? {\n                                            ...tc,\n                                            status: 'error',\n                                            error: error || '工具调用失败',\n                                            executionTime: executionTime || Date.now() - tc.startTime\n                                        } : tc;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>prev.map({\n                                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (msg)=>{\n                                        if (msg.role === 'tool_call' && msg.toolCall) {\n                                            const isMatch = toolCallId ? msg.toolCall.id === toolCallId : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';\n                                            if (isMatch) {\n                                                return {\n                                                    ...msg,\n                                                    toolCall: {\n                                                        id: msg.toolCall.id,\n                                                        toolName: msg.toolCall.toolName,\n                                                        args: msg.toolCall.args,\n                                                        status: 'error',\n                                                        error: error || '工具调用失败',\n                                                        startTime: msg.toolCall.startTime,\n                                                        executionTime: executionTime || Date.now() - msg.toolCall.startTime\n                                                    }\n                                                };\n                                            }\n                                        }\n                                        return msg;\n                                    }\n                                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"])\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onNewAssistantMessage: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (messageId)=>{\n                        const newAssistantMessage = {\n                            id: messageId,\n                            role: 'assistant',\n                            content: '',\n                            timestamp: Date.now(),\n                            model: selectedModelRef.current\n                        };\n                        setMessagesRef.current({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": (prev)=>[\n                                    ...prev,\n                                    newAssistantMessage\n                                ]\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]);\n                        setCurrentAssistantMessageIdRef.current(messageId);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onStreamEnd: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n                        setIsStreamingRef.current(false);\n                        setActiveToolRef.current(null);\n                        setCurrentAssistantMessageIdRef.current(null);\n                        // 优化：更智能的统计信息获取策略，减少API调用\n                        const cleanup = {\n                            \"SimpleChatPage.useCallback[createStreamHandlers].cleanup\": ()=>{\n                                if (currentConversation) {\n                                    console.log('🔧 对话完成，准备获取统计信息');\n                                    // 使用单次调用获取统计信息，如果没有则等待后重试一次\n                                    const fetchStats = {\n                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\": async function() {\n                                            let retryOnce = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n                                            try {\n                                                const response = await fetch(\"/api/conversations/\".concat(currentConversation.id));\n                                                const data = await response.json();\n                                                if (data.success && data.messages) {\n                                                    const hasStats = data.messages.some({\n                                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats.hasStats\": (msg)=>msg.role === 'assistant' && (msg.total_duration || msg.eval_count)\n                                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats.hasStats\"]);\n                                                    if (hasStats) {\n                                                        console.log('✅ 获取到统计信息，更新消息');\n                                                        updateMessagesFromDatabase(data.messages);\n                                                    } else if (retryOnce) {\n                                                        console.log('⏳ 统计信息未就绪，1秒后重试一次');\n                                                        setTimeout({\n                                                            \"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\": ()=>fetchStats(false)\n                                                        }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\"], 1000);\n                                                    } else {\n                                                        console.log('⚠️ 统计信息仍未就绪，使用当前消息');\n                                                        updateMessagesFromDatabase(data.messages);\n                                                    }\n                                                } else {\n                                                    console.log('❌ 获取消息数据失败');\n                                                }\n                                            } catch (err) {\n                                                console.error('获取统计信息失败:', err);\n                                            }\n                                        }\n                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup.fetchStats\"];\n                                    // 延迟300ms后开始获取，给服务器时间保存统计信息\n                                    setTimeout({\n                                        \"SimpleChatPage.useCallback[createStreamHandlers].cleanup\": ()=>fetchStats()\n                                    }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup\"], 300);\n                                } else {\n                                    console.log('🔄 无当前对话，刷新对话列表');\n                                    loadConversationsRef.current();\n                                }\n                            }\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers].cleanup\"];\n                        // 添加到清理队列\n                        cleanupHandlersRef.current.push(cleanup);\n                        cleanup();\n                        // 从清理队列中移除\n                        setTimeout({\n                            \"SimpleChatPage.useCallback[createStreamHandlers]\": ()=>{\n                                const index = cleanupHandlersRef.current.indexOf(cleanup);\n                                if (index > -1) {\n                                    cleanupHandlersRef.current.splice(index, 1);\n                                }\n                            }\n                        }[\"SimpleChatPage.useCallback[createStreamHandlers]\"], 3000);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"],\n                onError: {\n                    \"SimpleChatPage.useCallback[createStreamHandlers]\": (errorMessage)=>{\n                        setErrorRef.current(errorMessage);\n                        setIsStreamingRef.current(false);\n                    }\n                }[\"SimpleChatPage.useCallback[createStreamHandlers]\"]\n            };\n            return handlers;\n        }\n    }[\"SimpleChatPage.useCallback[createStreamHandlers]\"], [\n        updateMessagesFromDatabase,\n        currentConversation\n    ]);\n    // 插入文本到输入框\n    const handleInsertText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[handleInsertText]\": (text)=>{\n            setInputMessage(text);\n        }\n    }[\"SimpleChatPage.useCallback[handleInsertText]\"], [\n        setInputMessage\n    ]);\n    // 发送消息的核心逻辑\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleChatPage.useCallback[sendMessage]\": async ()=>{\n            if (!inputMessage.trim() || !selectedModel || isStreaming) {\n                return;\n            }\n            let activeConversation = currentConversation;\n            if (!activeConversation) {\n                const title = inputMessage.trim().substring(0, 30) + (inputMessage.length > 30 ? '...' : '');\n                const conversationId = await createConversation(title, selectedModel);\n                if (!conversationId) {\n                    setError('创建对话失败');\n                    return;\n                }\n                await new Promise({\n                    \"SimpleChatPage.useCallback[sendMessage]\": (resolve)=>setTimeout(resolve, 100)\n                }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n                activeConversation = currentConversation;\n            }\n            const userMessage = {\n                id: \"user-\".concat(Date.now()),\n                role: 'user',\n                content: inputMessage.trim(),\n                timestamp: Date.now()\n            };\n            // 获取当前的消息列表（使用ref避免在依赖中包含messages）\n            const currentMessages = messagesRef.current;\n            setMessages({\n                \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        userMessage\n                    ]\n            }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n            setInputMessage('');\n            setIsStreaming(true);\n            setError(null);\n            setToolCalls([]);\n            setActiveTool(null);\n            const assistantMessageId = \"assistant-\".concat(Date.now());\n            const assistantMessage = {\n                id: assistantMessageId,\n                role: 'assistant',\n                content: '',\n                timestamp: Date.now(),\n                model: selectedModel\n            };\n            setMessages({\n                \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]\n            }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n            setCurrentAssistantMessageId(assistantMessageId);\n            try {\n                // 创建新的 AbortController\n                const controller = new AbortController();\n                setAbortController(controller);\n                const chatRequestBody = {\n                    model: selectedModel,\n                    conversationId: activeConversation === null || activeConversation === void 0 ? void 0 : activeConversation.id,\n                    messages: [\n                        ...systemPromptRef.current ? [\n                            {\n                                role: 'system',\n                                content: systemPromptRef.current\n                            }\n                        ] : [],\n                        ...currentMessages.map({\n                            \"SimpleChatPage.useCallback[sendMessage]\": (msg)=>({\n                                    role: msg.role,\n                                    content: msg.content\n                                })\n                        }[\"SimpleChatPage.useCallback[sendMessage]\"]),\n                        {\n                            role: 'user',\n                            content: userMessage.content\n                        }\n                    ],\n                    stream: true,\n                    enableTools,\n                    selectedTools\n                };\n                const response = await fetch('/api/chat', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(chatRequestBody),\n                    signal: controller.signal\n                });\n                if (!response.ok) {\n                    throw new Error('聊天请求失败');\n                }\n                // 使用流式服务处理响应，传递 AbortController\n                await _services_streamingChatService__WEBPACK_IMPORTED_MODULE_5__.streamingChatService.processStreamingResponse(response, createStreamHandlers(), assistantMessageId, controller);\n            } catch (err) {\n                // 如果是中断错误，不显示错误信息\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('🛑 用户主动停止了生成');\n                } else {\n                    setError(err instanceof Error ? err.message : '发送消息失败');\n                    setMessages({\n                        \"SimpleChatPage.useCallback[sendMessage]\": (prev)=>prev.filter({\n                                \"SimpleChatPage.useCallback[sendMessage]\": (msg)=>msg.id !== assistantMessageId\n                            }[\"SimpleChatPage.useCallback[sendMessage]\"])\n                    }[\"SimpleChatPage.useCallback[sendMessage]\"]);\n                }\n            } finally{\n                setIsStreaming(false);\n                setAbortController(null);\n            }\n        }\n    }[\"SimpleChatPage.useCallback[sendMessage]\"], [\n        inputMessage,\n        selectedModel,\n        isStreaming,\n        currentConversation,\n        enableTools,\n        selectedTools,\n        createConversation,\n        systemPrompt,\n        setAbortController\n    ]);\n    // 侧边栏事件处理函数 - 优化：按需加载对话列表\n    const handleCreateConversation = ()=>{\n        window.location.href = '/simple-chat?new=true';\n    };\n    const handleLoadConversation = async (conversationId)=>{\n        // 确保有对话列表数据\n        await loadConversationsIfNeeded();\n        window.location.href = \"/simple-chat?id=\".concat(conversationId);\n    };\n    const handleDeleteConversation = async (conversationId)=>{\n        try {\n            // 确保有对话列表数据\n            await loadConversationsIfNeeded();\n            const response = await fetch(\"/api/conversations/\".concat(conversationId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await loadConversations(); // 删除后刷新列表\n                // 如果删除的是当前对话，重定向到新对话\n                if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                    window.location.href = '/simple-chat?new=true';\n                }\n            }\n        } catch (error) {\n            console.error('Failed to delete conversation:', error);\n        }\n    };\n    // 确保侧边栏有对话列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleChatPage.useEffect\": ()=>{\n            // 延迟加载对话列表，避免阻塞页面初始化\n            const timer = setTimeout({\n                \"SimpleChatPage.useEffect.timer\": ()=>{\n                    loadConversationsIfNeeded();\n                }\n            }[\"SimpleChatPage.useEffect.timer\"], 100);\n            return ({\n                \"SimpleChatPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"SimpleChatPage.useEffect\"];\n        }\n    }[\"SimpleChatPage.useEffect\"], [\n        loadConversationsIfNeeded\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-theme-background overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                conversations: conversations,\n                currentConversation: currentConversation,\n                onCreateConversation: handleCreateConversation,\n                onLoadConversation: handleLoadConversation,\n                onDeleteConversation: handleDeleteConversation\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n                lineNumber: 669,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_4__.ChatContainer, {\n                currentConversation: currentConversation,\n                models: models,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                agents: agents,\n                selectedAgentId: selectedAgentId,\n                onAgentChange: handleAgentChange,\n                selectorMode: selectorMode,\n                onSelectorModeChange: handleSelectorModeChange,\n                customModels: customModels,\n                messages: messages,\n                inputMessage: inputMessage,\n                onInputChange: setInputMessage,\n                onSendMessage: sendMessage,\n                isStreaming: isStreaming,\n                onStopGeneration: stopGeneration,\n                expandedThinkingMessages: expandedThinkingMessages,\n                onToggleThinkingExpand: toggleThinkingExpand,\n                enableTools: enableTools,\n                selectedTools: selectedTools,\n                onToolsToggle: setEnableTools,\n                onSelectedToolsChange: setSelectedTools,\n                onInsertText: handleInsertText,\n                onClearChat: clearCurrentChat,\n                error: error,\n                onDismissError: ()=>setError(null),\n                chatStyle: chatStyle,\n                displaySize: displaySize,\n                onChatStyleChange: handleChatStyleChange,\n                onDisplaySizeChange: handleDisplaySizeChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n                lineNumber: 678,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\simple-chat\\\\page.tsx\",\n        lineNumber: 667,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleChatPage, \"iuqQE5DSQi3b8pkj33zfFo3Ol10=\", false, function() {\n    return [\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationManager,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useChatMessages,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useChatStyle,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useUrlHandler,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useMessageLoader,\n        _hooks__WEBPACK_IMPORTED_MODULE_2__.useConversationEventHandlers\n    ];\n});\n_c = SimpleChatPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/page.tsx\n"));

/***/ })

});