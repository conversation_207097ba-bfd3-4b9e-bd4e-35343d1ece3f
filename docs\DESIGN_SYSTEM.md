# 设计系统指南

## 🎨 全局主题配色方案

我们的应用使用了完整的设计令牌系统，支持浅色和深色主题的无缝切换。

### 📋 颜色系统

#### 主要颜色
- `--color-primary`: #BDB2FF (浅色) / #4930D9 (深色)
- `--color-primary-hover`: #A497EC (浅色) / #3F26CF (深色)
- `--color-accent`: #4930D9
- `--color-accent-hover`: #3f26cf

#### 背景颜色
- `--color-background`: 主背景色
- `--color-background-secondary`: 次要背景色
- `--color-background-tertiary`: 第三级背景色

#### 前景颜色
- `--color-foreground`: 主文字颜色
- `--color-foreground-secondary`: 次要文字颜色
- `--color-foreground-muted`: 静音文字颜色

#### 状态颜色
- `--color-success`: #10b981 (成功)
- `--color-warning`: #f59e0b (警告)
- `--color-error`: #ef4444 (错误)
- `--color-info`: #3b82f6 (信息)

## 🔤 字体大小系统

### CSS变量
```css
--font-size-xs: 0.75rem;     /* 12px */
--font-size-sm: 0.875rem;    /* 14px */
--font-size-base: 1rem;      /* 16px */
--font-size-lg: 1.125rem;    /* 18px */
--font-size-xl: 1.25rem;     /* 20px */
--font-size-2xl: 1.5rem;     /* 24px */
--font-size-3xl: 1.875rem;   /* 30px */
--font-size-4xl: 2.25rem;    /* 36px */
--font-size-5xl: 3rem;       /* 48px */
--font-size-6xl: 3.75rem;    /* 60px */
```

### Tailwind类
```html
<!-- 使用Tailwind类 -->
<p class="text-xs">12px文字</p>
<p class="text-sm">14px文字</p>
<p class="text-base">16px文字</p>
<p class="text-lg">18px文字</p>
<p class="text-xl">20px文字</p>
<p class="text-2xl">24px文字</p>
<p class="text-3xl">30px文字</p>
```

## 📝 标题层级系统

### 语义化标题类

#### HTML标题
```html
<h1 class="heading-h1">主标题 (36px, 700)</h1>
<h2 class="heading-h2">二级标题 (30px, 600)</h2>
<h3 class="heading-h3">三级标题 (24px, 600)</h3>
<h4 class="heading-h4">四级标题 (20px, 600)</h4>
<h5 class="heading-h5">五级标题 (18px, 500)</h5>
<h6 class="heading-h6">六级标题 (16px, 500)</h6>
```

#### 页面专用标题
```html
<!-- 页面主标题 -->
<h1 class="page-title">页面标题 (30px, 700)</h1>

<!-- 页面副标题 -->
<p class="page-subtitle">页面描述文字 (18px, 400, muted)</p>

<!-- 区域标题 -->
<h2 class="section-title">区域标题 (24px, 600)</h2>

<!-- 卡片标题 -->
<h3 class="card-title">卡片标题 (20px, 600)</h3>
```

#### Tailwind语义化类
```html
<!-- 使用Tailwind语义化字体大小 -->
<h1 class="text-page-title font-bold">页面标题</h1>
<p class="text-page-subtitle text-theme-foreground-muted">页面副标题</p>
<h2 class="text-section-title font-semibold">区域标题</h2>
<h3 class="text-card-title font-semibold">卡片标题</h3>
```

## 📏 间距系统

### CSS变量
```css
--spacing-xs: 0.25rem;    /* 4px */
--spacing-sm: 0.5rem;     /* 8px */
--spacing-md: 1rem;       /* 16px */
--spacing-lg: 1.5rem;     /* 24px */
--spacing-xl: 2rem;       /* 32px */
--spacing-2xl: 3rem;      /* 48px */
--spacing-3xl: 4rem;      /* 64px */
```

### Tailwind类
```html
<div class="p-xs">4px内边距</div>
<div class="m-sm">8px外边距</div>
<div class="gap-md">16px间隙</div>
<div class="space-y-lg">24px垂直间距</div>
```

## 🔄 圆角系统

### CSS变量
```css
--radius-sm: 0.25rem;     /* 4px */
--radius-md: 0.5rem;      /* 8px */
--radius-lg: 0.75rem;     /* 12px */
--radius-xl: 1rem;        /* 16px */
--radius-2xl: 1.5rem;     /* 24px */
```

### Tailwind类
```html
<div class="rounded-sm">4px圆角</div>
<div class="rounded-md">8px圆角</div>
<div class="rounded-lg">12px圆角</div>
<div class="rounded-xl">16px圆角</div>
```

## 🌙 阴影系统

### CSS变量
```css
--shadow-sm: 轻微阴影
--shadow-md: 中等阴影
--shadow-lg: 较大阴影
--shadow-xl: 超大阴影
```

### Tailwind类
```html
<div class="shadow-sm">轻微阴影</div>
<div class="shadow-md">中等阴影</div>
<div class="shadow-lg">较大阴影</div>
<div class="shadow-xl">超大阴影</div>
```

## 🎯 使用建议

### 1. 页面标题布局
```html
<div className="mb-8">
  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 className="page-title">页面主标题</h1>
      <p className="page-subtitle mt-2">页面描述信息</p>
    </div>
    <div className="flex-shrink-0">
      <!-- 操作按钮 -->
    </div>
  </div>
</div>
```

### 2. 区域标题
```html
<div className="mb-6">
  <h2 className="section-title">区域标题</h2>
</div>
```

### 3. 卡片标题
```html
<div className="bg-theme-card rounded-lg p-4">
  <h3 className="card-title mb-2">卡片标题</h3>
  <p className="text-theme-foreground-muted">卡片内容</p>
</div>
```

### 4. 响应式设计
```html
<!-- 移动端和桌面端自适应 -->
<h1 className="page-title text-2xl sm:text-3xl">响应式标题</h1>
```

## 🔧 自定义调整

如需调整特定页面的标题大小，可以在`:root`中修改对应变量：

```css
:root {
  /* 自定义页面标题大小 */
  --page-title-size: 2.5rem; /* 40px */
  
  /* 自定义区域标题大小 */
  --section-title-size: 1.75rem; /* 28px */
}
```

## 📱 主题适配

所有设计令牌都支持浅色和深色主题自动切换，无需额外配置。主题切换通过在`html`元素上添加`.dark`类来实现。

---

通过使用这套设计系统，可以确保整个应用的视觉一致性，同时便于维护和扩展。 