"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/hooks/chat/useChatMessages.ts":
/*!***********************************************************!*\
  !*** ./src/app/simple-chat/hooks/chat/useChatMessages.ts ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChatMessages: () => (/* binding */ useChatMessages)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useChatMessages auto */ \n// 本地存储键名\nconst SELECTED_MODEL_KEY = 'chat_selected_model';\nconst CONVERSATION_MODEL_KEY_PREFIX = 'chat_conversation_model_';\nfunction useChatMessages() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [expandedThinkingMessages, setExpandedThinkingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Set());\n    // Agent-related state\n    const [selectedAgent, setSelectedAgent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [systemPrompt, setSystemPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // 工具相关状态\n    const [enableTools, setEnableTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedTools, setSelectedTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [toolCalls, setToolCalls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [currentAssistantMessageId, setCurrentAssistantMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // 添加AbortController来控制请求中断\n    const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // --- New function to handle agent selection ---\n    const selectAgent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[selectAgent]\": async (agentId)=>{\n            if (agentId === null) {\n                setSelectedAgent(null);\n                setSystemPrompt(null);\n                // Optionally reset other settings or leave them as they were\n                return;\n            }\n            try {\n                const response = await fetch(\"/api/agents/\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to fetch agent details');\n                }\n                const agent = await response.json();\n                setSelectedAgent(agent);\n                // Override chat settings with agent's configuration\n                setSelectedModel(agent.model.base_model);\n                setEnableTools(agent.tools.length > 0);\n                setSelectedTools(agent.tools.map({\n                    \"useChatMessages.useCallback[selectAgent]\": (t)=>t.name\n                }[\"useChatMessages.useCallback[selectAgent]\"]));\n                if (agent.system_prompt) {\n                    setSystemPrompt(agent.system_prompt);\n                } else {\n                    setSystemPrompt(null);\n                }\n                console.log('Agent \"'.concat(agent.name, '\" selected. Model set to \"').concat(agent.model.base_model, '\".'));\n            } catch (error) {\n                console.error('Error selecting agent:', error);\n                // Handle error, maybe show a notification to the user\n                setSelectedAgent(null); // Reset on error\n            }\n        }\n    }[\"useChatMessages.useCallback[selectAgent]\"], []);\n    // 从本地存储加载已保存的模型选择\n    const loadSavedModel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[loadSavedModel]\": ()=>{\n            try {\n                const savedModel = localStorage.getItem(SELECTED_MODEL_KEY);\n                return savedModel;\n            } catch (error) {\n                console.warn('无法从localStorage读取保存的模型:', error);\n                return null;\n            }\n        }\n    }[\"useChatMessages.useCallback[loadSavedModel]\"], []);\n    // 从本地存储加载特定对话的模型选择\n    const loadConversationModel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[loadConversationModel]\": (conversationId)=>{\n            try {\n                const key = \"\".concat(CONVERSATION_MODEL_KEY_PREFIX).concat(conversationId);\n                const savedModel = localStorage.getItem(key);\n                return savedModel;\n            } catch (error) {\n                console.warn('无法从localStorage读取对话模型:', error);\n                return null;\n            }\n        }\n    }[\"useChatMessages.useCallback[loadConversationModel]\"], []);\n    // 保存模型选择到本地存储 - 修复：移除useCallback依赖，使其稳定\n    const saveModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[saveModelSelection]\": (modelName, conversationId)=>{\n            try {\n                // 保存全局模型选择\n                localStorage.setItem(SELECTED_MODEL_KEY, modelName);\n                // 如果有对话ID，也保存对话特定的模型选择\n                if (conversationId) {\n                    const key = \"\".concat(CONVERSATION_MODEL_KEY_PREFIX).concat(conversationId);\n                    localStorage.setItem(key, modelName);\n                    console.log(\"保存对话 \".concat(conversationId, \" 的模型选择: \").concat(modelName));\n                }\n            } catch (error) {\n                console.warn('无法保存模型选择到localStorage:', error);\n            }\n        }\n    }[\"useChatMessages.useCallback[saveModelSelection]\"], []); // 空依赖数组，函数体内不依赖任何外部变量\n    // 包装setSelectedModel以添加持久化 - 修复：现在依赖稳定了\n    const setSelectedModelWithPersistence = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[setSelectedModelWithPersistence]\": (modelName, conversationId)=>{\n            setSelectedModel(modelName);\n            saveModelSelection(modelName, conversationId);\n        }\n    }[\"useChatMessages.useCallback[setSelectedModelWithPersistence]\"], [\n        saveModelSelection\n    ]); // saveModelSelection现在是稳定的\n    // 智能模型选择函数 - 修复：所有依赖现在都是稳定的\n    const selectBestModel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[selectBestModel]\": (availableModels, conversationId, lastUsedModel, conversationModel)=>{\n            var // 5. 第一个可用模型\n            _availableModels_;\n            if (!availableModels.length) return;\n            // 按优先级尝试不同的模型选择策略\n            const strategies = [\n                // 1. 对话中最后使用的模型\n                lastUsedModel,\n                // 2. 对话特定保存的模型\n                conversationId ? loadConversationModel(conversationId) : null,\n                // 3. 对话创建时的模型\n                conversationModel,\n                // 4. 全局保存的模型\n                loadSavedModel(),\n                (_availableModels_ = availableModels[0]) === null || _availableModels_ === void 0 ? void 0 : _availableModels_.name\n            ];\n            for (const candidateModel of strategies){\n                if (candidateModel && availableModels.some({\n                    \"useChatMessages.useCallback[selectBestModel]\": (model)=>model.name === candidateModel\n                }[\"useChatMessages.useCallback[selectBestModel]\"])) {\n                    console.log(\"选择模型: \".concat(candidateModel, \" (策略: \").concat(strategies.indexOf(candidateModel) + 1, \")\"));\n                    setSelectedModel(candidateModel);\n                    // 保存选择\n                    saveModelSelection(candidateModel, conversationId);\n                    return candidateModel;\n                }\n            }\n        }\n    }[\"useChatMessages.useCallback[selectBestModel]\"], [\n        loadSavedModel,\n        loadConversationModel,\n        saveModelSelection\n    ]); // 现在所有依赖都是稳定的\n    // 获取模型列表 - 优化：直接使用custom-models API数据，无需转换\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useChatMessages.useEffect\": ()=>{\n            const fetchModels = {\n                \"useChatMessages.useEffect.fetchModels\": async ()=>{\n                    try {\n                        console.log('🔄 开始加载模型列表');\n                        const response = await fetch('/api/custom-models');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.models) {\n                                // 直接使用数据库数据，保持完整的模型信息\n                                setModels(data.models);\n                                console.log(\"✅ 成功加载 \".concat(data.models.length, \" 个模型\"));\n                                if (data.models.length > 0 && !selectedModel) {\n                                    // 智能选择第一个可用模型\n                                    selectBestModel(data.models);\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        console.error('❌ 获取模型失败:', err);\n                    }\n                }\n            }[\"useChatMessages.useEffect.fetchModels\"];\n            fetchModels();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"useChatMessages.useEffect\"], []);\n    // 思考面板切换\n    const toggleThinkingExpand = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[toggleThinkingExpand]\": (messageId)=>{\n            setExpandedThinkingMessages({\n                \"useChatMessages.useCallback[toggleThinkingExpand]\": (prev)=>{\n                    const newExpanded = new Set(prev);\n                    if (newExpanded.has(messageId)) {\n                        newExpanded.delete(messageId);\n                    } else {\n                        newExpanded.add(messageId);\n                    }\n                    return newExpanded;\n                }\n            }[\"useChatMessages.useCallback[toggleThinkingExpand]\"]);\n        }\n    }[\"useChatMessages.useCallback[toggleThinkingExpand]\"], []);\n    // 停止生成\n    const stopGeneration = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatMessages.useCallback[stopGeneration]\": ()=>{\n            // 中断正在进行的请求\n            if (abortController) {\n                console.log('🛑 中断正在进行的请求');\n                abortController.abort();\n                setAbortController(null);\n            }\n            // 重置流式状态\n            setIsStreaming(false);\n        }\n    }[\"useChatMessages.useCallback[stopGeneration]\"], [\n        abortController\n    ]);\n    return {\n        // 消息状态\n        messages,\n        setMessages,\n        inputMessage,\n        setInputMessage,\n        isStreaming,\n        setIsStreaming,\n        selectedModel,\n        setSelectedModel: setSelectedModelWithPersistence,\n        models,\n        expandedThinkingMessages,\n        setExpandedThinkingMessages,\n        // Agent state\n        selectedAgent,\n        systemPrompt,\n        // 工具状态\n        enableTools,\n        setEnableTools,\n        selectedTools,\n        setSelectedTools,\n        activeTool,\n        setActiveTool,\n        toolCalls,\n        setToolCalls,\n        currentAssistantMessageId,\n        setCurrentAssistantMessageId,\n        // 方法\n        toggleThinkingExpand,\n        stopGeneration,\n        selectAgent,\n        // AbortController\n        abortController,\n        setAbortController,\n        // 新增的智能模型选择方法\n        selectBestModel\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/hooks/chat/useChatMessages.ts\n"));

/***/ })

});